// 🧠 SMART CONTRACT CLIENT - Interface with our on-chain intelligence
import { Connection, PublicKey, Keypair } from '@solana/web3.js';
import { Program, AnchorProvider, web3, BN } from '@coral-xyz/anchor';
import { CONFIG } from './config.js';

class SmartContractIntelligence {
  constructor() {
    this.connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
    this.programId = new PublicKey('MeteoraGoldExtractorIntelligenceSystem1111111111'); // Enhanced program ID
    this.intelligenceStatePDA = null;
    this.program = null;
    this.isInitialized = false;
    this.learningData = [];
    this.tradeHistory = [];
  }

  // 🚀 INITIALIZE CONNECTION TO SMART CONTRACT
  async initialize() {
    try {
      console.log('🧠 Connecting to Meteora Intelligence Smart Contract...');
      
      // Create provider (read-only for queries)
      const provider = new AnchorProvider(
        this.connection,
        { publicKey: PublicKey.default }, // No wallet needed for read operations
        { commitment: 'confirmed' }
      );
      
      // Load program IDL (you'd load this from file or network)
      const idl = await this.loadProgramIDL();
      this.program = new Program(idl, this.programId, provider);
      
      // Find intelligence state PDA for gold extractor
      [this.intelligenceStatePDA] = await PublicKey.findProgramAddress(
        [Buffer.from('gold_intelligence')],
        this.programId
      );
      
      console.log('✅ Smart contract connected!');
      console.log(`📍 Intelligence State: ${this.intelligenceStatePDA.toString()}`);
      
      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('❌ Failed to connect to smart contract:', error.message);
      return false;
    }
  }

  // 🏆 GET GOLD OPPORTUNITIES (Enhanced)
  async getTopPools() {
    if (!this.isInitialized) {
      console.log('⚠️ Smart contract not initialized, using fallback data...');
      return this.getFallbackTopPools();
    }

    try {
      console.log('🔍 Querying smart contract for gold opportunities...');

      const goldPools = await this.program.methods
        .getGoldOpportunities()
        .accounts({
          intelligenceState: this.intelligenceStatePDA
        })
        .view();

      console.log(`✅ Retrieved ${goldPools.length} gold opportunities from smart contract`);

      // Format for our system with enhanced data
      return goldPools.map((pool, index) => ({
        rank: index + 1,
        poolAddress: pool.poolAddress.toString(),
        tokenX: pool.tokenX.toString(),
        tokenY: pool.tokenY.toString(),
        tvl: pool.tvl,
        volume24h: pool.volume24h,
        fees24h: pool.fees24h,
        goldScore: pool.goldScore,                    // 🏆 THE ULTIMATE METRIC
        profitPotential: pool.profitPotential,        // Expected return
        entryConfidence: pool.entryConfidence,        // Confidence level
        optimalEntrySize: pool.optimalEntrySize,      // Position sizing
        successProbability: pool.successProbability,  // Success rate
        riskLevel: this.formatRiskLevel(pool.riskLevel),
        lastUpdated: new Date(pool.lastUpdated * 1000)
      }));

    } catch (error) {
      console.error('❌ Error querying smart contract:', error.message);
      return this.getFallbackTopPools();
    }
  }

  // 💎 GET SPECIFIC POOL GOLD ANALYSIS
  async getPoolRanking(poolAddress) {
    if (!this.isInitialized) {
      return null;
    }

    try {
      const poolPubkey = new PublicKey(poolAddress);

      const analysis = await this.program.methods
        .getPoolGoldAnalysis(poolPubkey)
        .accounts({
          intelligenceState: this.intelligenceStatePDA
        })
        .view();

      if (analysis) {
        return {
          poolAddress: analysis.poolAddress.toString(),
          goldScore: analysis.goldScore,
          profitPotential: analysis.profitPotential,
          entryConfidence: analysis.entryConfidence,
          optimalEntrySize: analysis.optimalEntrySize,
          successProbability: analysis.successProbability,
          recommendedAction: analysis.recommendedAction,
          riskWarning: analysis.riskWarning,
          rank: await this.calculateRank(analysis.goldScore)
        };
      }

      return null;
    } catch (error) {
      console.error(`❌ Error getting pool gold analysis for ${poolAddress}:`, error.message);
      return null;
    }
  }

  // 🧠 GET LEARNING INSIGHTS
  async getHoneyLeaderboard() {
    if (!this.isInitialized) {
      return [];
    }

    try {
      const insights = await this.program.methods
        .getLearningInsights()
        .accounts({
          intelligenceState: this.intelligenceStatePDA
        })
        .view();

      return {
        totalTradesLearned: insights.totalTradesLearned,
        successRate: insights.successRate,
        averageProfitPercentage: insights.averageProfitPercentage,
        mostProfitablePattern: insights.mostProfitablePattern,
        confidenceLevel: insights.confidenceLevel,
        systemStatus: this.getSystemStatus(insights)
      };

    } catch (error) {
      console.error('❌ Error getting learning insights:', error.message);
      return this.getFallbackInsights();
    }
  }

  // 📈 FEED TRADE RESULT TO LEARNING SYSTEM
  async feedTradeResult(tradeResult) {
    if (!this.isInitialized) {
      console.log('⚠️ Smart contract not available, storing trade locally...');
      this.tradeHistory.push(tradeResult);
      return false;
    }

    try {
      // This would require a write transaction with a wallet
      // For now, we'll store locally and batch update later
      this.tradeHistory.push(tradeResult);

      console.log(`📈 Trade result recorded: ${tradeResult.profitPercentage > 0 ? 'PROFIT' : 'LOSS'} of ${(tradeResult.profitPercentage * 100).toFixed(2)}%`);

      return true;
    } catch (error) {
      console.error('❌ Error feeding trade result:', error.message);
      return false;
    }
  }

  // 📊 GET INTELLIGENCE SUMMARY
  async getIntelligenceSummary() {
    if (!this.isInitialized) {
      return this.getFallbackSummary();
    }

    try {
      const state = await this.program.account.intelligenceState.fetch(this.intelligenceStatePDA);
      
      return {
        totalPoolsTracked: state.totalPoolsTracked,
        lastUpdate: new Date(state.lastUpdate * 1000),
        topPoolsCount: state.topPools.length,
        isLive: Date.now() - (state.lastUpdate * 1000) < 10 * 60 * 1000, // Updated within 10 minutes
        dataSource: 'SMART_CONTRACT'
      };
      
    } catch (error) {
      console.error('❌ Error getting intelligence summary:', error.message);
      return this.getFallbackSummary();
    }
  }

  // 🔍 CHECK IF POOL IS PROFITABLE
  async isPoolProfitable(poolAddress, minProfitPerDollar = 0.1) {
    const ranking = await this.getPoolRanking(poolAddress);
    return ranking && ranking.profitPerDollar >= minProfitPerDollar;
  }

  // 🎯 FIND BEST GOLD OPPORTUNITIES BY CRITERIA
  async findBestPools(criteria = {}) {
    const goldPools = await this.getTopPools();

    const defaultCriteria = {
      minGoldScore: 25.0,           // Minimum gold score
      minSuccessProbability: 0.75,  // 75% success rate
      minProfitPotential: 0.5,      // 50% profit potential
      maxRisk: 'MEDIUM',            // Maximum risk level
      minConfidence: 0.6,           // 60% entry confidence
      maxCapitalRisk: 0.05          // 5% max capital risk
    };

    const finalCriteria = { ...defaultCriteria, ...criteria };

    return goldPools.filter(pool => {
      // Gold score filter
      if (pool.goldScore < finalCriteria.minGoldScore) return false;

      // Success probability filter
      if (pool.successProbability < finalCriteria.minSuccessProbability) return false;

      // Profit potential filter
      if (pool.profitPotential < finalCriteria.minProfitPotential) return false;

      // Risk level filter
      if (this.getRiskLevel(pool.riskLevel) > this.getRiskLevel(finalCriteria.maxRisk)) return false;

      // Entry confidence filter
      if (pool.entryConfidence < finalCriteria.minConfidence) return false;

      // Capital risk filter (optimal entry size)
      if (pool.optimalEntrySize > finalCriteria.maxCapitalRisk) return false;

      return true;
    });
  }

  // 💰 GET ABSOLUTE BEST OPPORTUNITY RIGHT NOW
  async getBestOpportunity() {
    const goldPools = await this.getTopPools();

    if (goldPools.length === 0) return null;

    // The first pool is already the highest gold score
    const bestPool = goldPools[0];

    // Get detailed analysis
    const analysis = await this.getPoolRanking(bestPool.poolAddress);

    return {
      ...bestPool,
      analysis,
      recommendation: this.generateRecommendation(bestPool, analysis)
    };
  }

  // 🎯 GENERATE TRADING RECOMMENDATION
  generateRecommendation(pool, analysis) {
    const recommendation = {
      action: 'MONITOR',
      confidence: 'LOW',
      entrySize: 0.01,
      reasoning: [],
      warnings: []
    };

    // Determine action based on gold score
    if (pool.goldScore > 50) {
      recommendation.action = 'STRONG_BUY';
      recommendation.confidence = 'HIGH';
      recommendation.entrySize = Math.min(pool.optimalEntrySize, 0.05);
      recommendation.reasoning.push(`Exceptional gold score: ${pool.goldScore.toFixed(1)}`);
    } else if (pool.goldScore > 25) {
      recommendation.action = 'BUY';
      recommendation.confidence = 'MEDIUM';
      recommendation.entrySize = Math.min(pool.optimalEntrySize, 0.03);
      recommendation.reasoning.push(`Good gold score: ${pool.goldScore.toFixed(1)}`);
    }

    // Add success probability reasoning
    if (pool.successProbability > 0.8) {
      recommendation.reasoning.push(`High success rate: ${(pool.successProbability * 100).toFixed(1)}%`);
    }

    // Add profit potential reasoning
    if (pool.profitPotential > 0.75) {
      recommendation.reasoning.push(`High profit potential: ${(pool.profitPotential * 100).toFixed(1)}%`);
    }

    // Add warnings
    if (pool.riskLevel === 'HIGH' || pool.riskLevel === 'VERY_HIGH') {
      recommendation.warnings.push('High risk detected - consider smaller position');
    }

    if (pool.entryConfidence < 0.7) {
      recommendation.warnings.push('Lower confidence - wait for better setup');
    }

    if (analysis && analysis.riskWarning) {
      recommendation.warnings.push(analysis.riskWarning);
    }

    return recommendation;
  }

  // 🚨 GET ENHANCED REAL-TIME ALERTS
  async getAlerts() {
    const alerts = [];

    try {
      const goldPools = await this.getTopPools();

      // ULTIMATE GOLD OPPORTUNITY ALERT
      const ultimateGold = goldPools.filter(pool =>
        pool.goldScore > 75 &&
        pool.successProbability > 0.9 &&
        pool.profitPotential > 1.0
      );
      ultimateGold.forEach(pool => {
        alerts.push({
          type: 'ULTIMATE_GOLD_OPPORTUNITY',
          message: `💎 ULTIMATE GOLD: ${pool.tokenX}/${pool.tokenY} - ${pool.goldScore.toFixed(1)} gold score, ${(pool.successProbability * 100).toFixed(1)}% success rate!`,
          priority: 'CRITICAL',
          poolAddress: pool.poolAddress,
          data: pool,
          recommendation: 'IMMEDIATE_ACTION'
        });
      });

      // HIGH CONFIDENCE OPPORTUNITIES
      const highConfidence = goldPools.filter(pool =>
        pool.goldScore > 50 &&
        pool.entryConfidence > 0.85 &&
        pool.successProbability > 0.8
      );
      highConfidence.forEach(pool => {
        alerts.push({
          type: 'HIGH_CONFIDENCE_OPPORTUNITY',
          message: `🎯 HIGH CONFIDENCE: ${pool.tokenX}/${pool.tokenY} - ${(pool.entryConfidence * 100).toFixed(1)}% confidence, ${(pool.profitPotential * 100).toFixed(1)}% profit potential`,
          priority: 'HIGH',
          poolAddress: pool.poolAddress,
          data: pool,
          recommendation: 'STRONG_BUY'
        });
      });

      // LEARNING SYSTEM ALERTS
      const insights = await this.getHoneyLeaderboard(); // This now returns learning insights
      if (insights.successRate > 0.8 && insights.totalTradesLearned > 50) {
        alerts.push({
          type: 'SYSTEM_LEARNING_MILESTONE',
          message: `🧠 SYSTEM EVOLVED: ${(insights.successRate * 100).toFixed(1)}% success rate from ${insights.totalTradesLearned} trades learned!`,
          priority: 'MEDIUM',
          data: insights,
          recommendation: 'SYSTEM_READY'
        });
      }

      // RISK WARNINGS
      const highRiskOpportunities = goldPools.filter(pool =>
        pool.goldScore > 30 &&
        (pool.riskLevel === 'HIGH' || pool.riskLevel === 'VERY_HIGH')
      );
      highRiskOpportunities.forEach(pool => {
        alerts.push({
          type: 'HIGH_RISK_WARNING',
          message: `⚠️ HIGH RISK GOLD: ${pool.tokenX}/${pool.tokenY} - Great potential but ${pool.riskLevel} risk`,
          priority: 'MEDIUM',
          poolAddress: pool.poolAddress,
          data: pool,
          recommendation: 'REDUCE_POSITION_SIZE'
        });
      });

      // TOP GOLD POOL UPDATE
      if (goldPools.length > 0) {
        const topGold = goldPools[0];
        alerts.push({
          type: 'TOP_GOLD_UPDATE',
          message: `👑 TOP GOLD: ${topGold.tokenX}/${topGold.tokenY} - ${topGold.goldScore.toFixed(1)} gold score, ${(topGold.profitPotential * 100).toFixed(1)}% profit potential`,
          priority: 'LOW',
          poolAddress: topGold.poolAddress,
          data: topGold,
          recommendation: this.generateRecommendation(topGold, null).action
        });
      }

    } catch (error) {
      console.error('❌ Error generating enhanced alerts:', error.message);
    }

    return alerts;
  }

  // 🔧 HELPER METHODS
  formatRiskLevel(riskLevel) {
    if (typeof riskLevel === 'object') {
      if (riskLevel.low !== undefined) return 'LOW';
      if (riskLevel.medium !== undefined) return 'MEDIUM';
      if (riskLevel.high !== undefined) return 'HIGH';
    }
    return riskLevel.toString().toUpperCase();
  }

  getRiskLevel(riskString) {
    const levels = { 'LOW': 1, 'MEDIUM': 2, 'HIGH': 3 };
    return levels[riskString.toUpperCase()] || 2;
  }

  async calculateRank(goldScore) {
    const goldPools = await this.getTopPools();
    const rank = goldPools.findIndex(pool => pool.goldScore <= goldScore) + 1;
    return rank || goldPools.length + 1;
  }

  // 🎯 GET SYSTEM STATUS
  getSystemStatus(insights) {
    if (insights.confidenceLevel > 0.9 && insights.successRate > 0.8) {
      return 'EXPERT_LEVEL';
    } else if (insights.confidenceLevel > 0.7 && insights.successRate > 0.7) {
      return 'ADVANCED';
    } else if (insights.confidenceLevel > 0.5 && insights.successRate > 0.6) {
      return 'INTERMEDIATE';
    } else if (insights.totalTradesLearned > 10) {
      return 'LEARNING';
    } else {
      return 'INITIALIZING';
    }
  }

  // 📊 GET FALLBACK INSIGHTS
  getFallbackInsights() {
    return {
      totalTradesLearned: 0,
      successRate: 0.0,
      averageProfitPercentage: 0.0,
      mostProfitablePattern: 'Insufficient data',
      confidenceLevel: 0.0,
      systemStatus: 'OFFLINE'
    };
  }

  // 📋 ENHANCED FALLBACK DATA (when smart contract is not available)
  getFallbackTopPools() {
    console.log('📋 Using enhanced fallback data - smart contract not available');
    return [
      {
        rank: 1,
        poolAddress: 'fallback_gold_pool_1',
        tokenX: 'SOL',
        tokenY: 'USDC',
        tvl: 1000000,
        volume24h: 5000000,
        fees24h: 25000,
        goldScore: 85.5,                    // High gold score
        profitPotential: 0.91,              // 91% annual return
        entryConfidence: 0.85,              // 85% confidence
        optimalEntrySize: 0.03,             // 3% of capital
        successProbability: 0.82,           // 82% success rate
        riskLevel: 'MEDIUM',
        lastUpdated: new Date()
      },
      {
        rank: 2,
        poolAddress: 'fallback_gold_pool_2',
        tokenX: 'DEGE',
        tokenY: 'SOL',
        tvl: 50000,
        volume24h: 2000000,
        fees24h: 15000,
        goldScore: 72.3,
        profitPotential: 1.25,              // 125% annual return
        entryConfidence: 0.78,
        optimalEntrySize: 0.025,
        successProbability: 0.75,
        riskLevel: 'HIGH',
        lastUpdated: new Date()
      }
      // More fallback pools would be added here...
    ];
  }

  getFallbackSummary() {
    return {
      totalPoolsTracked: 0,
      lastUpdate: new Date(),
      topPoolsCount: 0,
      isLive: false,
      dataSource: 'FALLBACK'
    };
  }

  // 📄 LOAD PROGRAM IDL (placeholder)
  async loadProgramIDL() {
    // In a real implementation, you'd load the IDL from a file or fetch from network
    return {
      version: "0.1.0",
      name: "meteora_intelligence",
      instructions: [
        // IDL would be generated from the Rust code
      ]
    };
  }
}

export default SmartContractIntelligence;
