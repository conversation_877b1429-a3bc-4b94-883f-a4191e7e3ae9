{"network": "skale_testnet", "chainId": "2043395295", "deployer": "0xE0282D77cF60BA484e13d24fd5686A6618F09A3B", "timestamp": "2025-06-07T13:28:04.271Z", "contracts": {"FeedRegistry": "0x13681A75E3E3b0e51b35201A5692d9BAe5e0fF68", "BandFeedRegistry": "0xfD9e4c97Bc38b56d4b87203087e7450E4D01236D", "GovernanceToken": "0x8DB03A3b8fBa6111835EA62E2a0652EF27224F1A", "RedistributionVault": "0xaB769D90d29d29Dd876a9fe8F978891c85Bb8D58", "AccessVerifier": "0x409DeD0752cE59a834943F999A62001C6c2B9370", "ReparationsDAO": "0xF2FB7e89973aFCb93d2b3F8Edd1B38759045d14e", "OracleHub": "0xfD3f998807c1c6f85B5Aeb7a1d373e5a034c5ee2", "MainZiGT": {"target": "0x5373E7FB40638Db965C85902e52FAf6Ec1A32f9d", "interface": {"fragments": [{"type": "constructor", "inputs": [], "payable": false, "gas": null}, {"type": "error", "inputs": [{"name": "target", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "AddressEmptyCode"}, {"type": "error", "inputs": [{"name": "implementation", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC1967InvalidImplementation"}, {"type": "error", "inputs": [], "name": "ERC1967Non<PERSON>ayable"}, {"type": "error", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "allowance", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "needed", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InsufficientAllowance"}, {"type": "error", "inputs": [{"name": "sender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "balance", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "needed", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InsufficientBalance"}, {"type": "error", "inputs": [{"name": "approver", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidApprover"}, {"type": "error", "inputs": [{"name": "receiver", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidReceiver"}, {"type": "error", "inputs": [{"name": "sender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidSender"}, {"type": "error", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidSpender"}, {"type": "error", "inputs": [], "name": "FailedCall"}, {"type": "error", "inputs": [], "name": "InvalidInitialization"}, {"type": "error", "inputs": [], "name": "NotInitializing"}, {"type": "error", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnableInvalidOwner"}, {"type": "error", "inputs": [{"name": "account", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnableUnauthorizedAccount"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_MulDiv18_Overflow"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "denominator", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_MulDiv_Overflow"}, {"type": "error", "inputs": [], "name": "PRBMath_SD59x18_Div_InputTooSmall"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Div_Overflow"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Exp2_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Exp_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_UD60x18_Exp2_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_UD60x18_Log_InputTooSmall"}, {"type": "error", "inputs": [], "name": "ReentrancyGuardReentrantCall"}, {"type": "error", "inputs": [], "name": "UUPSUnauthorizedCallContext"}, {"type": "error", "inputs": [{"name": "slot", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "UUPSUnsupportedProxiableUUID"}, {"type": "event", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "spender", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Approval", "anonymous": false}, {"type": "event", "inputs": [{"name": "registry", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "BandFeedRegistrySet", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "refund", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Burned", "anonymous": false}, {"type": "event", "inputs": [{"name": "direction", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "ratio", "type": "tuple", "baseType": "tuple", "indexed": false, "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "ConfigurationUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "FeeCharged", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "GovernanceTransferInitiated", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "GovernanceTransferred", "anonymous": false}, {"type": "event", "inputs": [{"name": "version", "type": "uint64", "baseType": "uint64", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Initialized", "anonymous": false}, {"type": "event", "inputs": [{"name": "messageId", "type": "bytes32", "baseType": "bytes32", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "MessageReceived", "anonymous": false}, {"type": "event", "inputs": [{"name": "messageId", "type": "bytes32", "baseType": "bytes32", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "MessageSent", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "cost", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Minted", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "chainlinkFeed", "type": "address", "baseType": "address", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OracleUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousOwner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "new<PERSON>wner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnershipTransferred", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "price", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "timestamp", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PriceCached", "anonymous": false}, {"type": "event", "inputs": [{"name": "symbols", "type": "string[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "prices", "type": "uint256[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "timestamps", "type": "uint256[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}], "name": "PricesRetrieved", "anonymous": false}, {"type": "event", "inputs": [], "name": "Rebalanced", "anonymous": false}, {"type": "event", "inputs": [{"name": "model", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ReparationsModelSet", "anonymous": false}, {"type": "event", "inputs": [{"name": "newStrategy", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "StrategyUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "from", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "to", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Transfer", "anonymous": false}, {"type": "event", "inputs": [{"name": "sigmoidEnd", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "linearEnd", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "UpdatedThresholds", "anonymous": false}, {"type": "event", "inputs": [{"name": "implementation", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Upgraded", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newWeight", "type": "int256", "baseType": "int256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "WeightUpdated", "anonymous": false}, {"type": "function", "inputs": [], "name": "DEST_CHAIN_SELECTOR", "constant": true, "outputs": [{"name": "", "type": "uint64", "baseType": "uint64", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "__governance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "allowance", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "approve", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "assetOracles", "constant": true, "outputs": [{"name": "chainlinkFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "assetWeights", "constant": true, "outputs": [{"name": "", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "account", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "balanceOf", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "bandFeedRegistry", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "basePrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "burn", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "cachedPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "cachedTimestamp", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "calculateZiGTValue", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "destination", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "crossChainMint", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "decimals", "constant": true, "outputs": [{"name": "", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "expBase", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "feePercentage", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "getAllPrices", "constant": false, "outputs": [{"name": "symbols", "type": "string[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "prices", "type": "uint256[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "timestamps", "type": "uint256[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "getPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "getPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "governance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "i_router", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "ccipRouter", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeedRegistry_", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "initialGovernance", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "direction", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "ratio", "type": "tuple", "baseType": "tuple", "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "ccipRouter", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "initialOwner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_governance", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "lastRebalance", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "linearPhaseEnd", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "linearSlope", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "mint", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "mint", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "name", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "owner", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "pendingGovernance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "positions", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "proxiableUUID", "constant": true, "outputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "rebalance", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "rebalanceCooldown", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "renounceOwnership", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "reparationsModel", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "router<PERSON>ddress", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "selectedDirection", "constant": true, "outputs": [{"name": "", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "selectedRatio", "constant": true, "outputs": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "config", "type": "tuple", "baseType": "tuple", "components": [{"name": "chainlinkFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "<PERSON><PERSON><PERSON><PERSON>", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "sigmoidPhaseEnd", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "symbol", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "totalMinted", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "totalSupply", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transfer", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "from", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transferFrom", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "new<PERSON>wner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transferOwnership", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "updateCachedPrice", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKeys", "type": "bytes32[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}}], "name": "updateCachedPrices", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_sigmoidEnd", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "_linearEnd", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "updatePhases", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_newStrategy", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "_newRatio", "type": "tuple", "baseType": "tuple", "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "updateStrategy", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "newImplementation", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "data", "type": "bytes", "baseType": "bytes", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "upgradeToAndCall", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "version", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}], "deploy": {"type": "constructor", "inputs": [], "payable": false, "gas": null}, "fallback": null, "receive": false}, "runner": "<SignerWithAddress 0xE0282D77cF60BA484e13d24fd5686A6618F09A3B>", "filters": {}, "fallback": null}, "ReparationsModel": "0xF65fE3147881D4CcdF7e21FD022502de3812EfCd", "ZiGGovernance": "0xe96935Cf8418BB6D5466C50df1E4c73d7d802664", "ZiGUtilityToken": "0x341CB086C056180cC0bE9Dcb689E67E524f31666", "ZiGMemeToken": "0x865E194b0993681b0f842c71dEFA7e0E7Bcb29aB", "ZiGNFT": "0x94253AA2d03720bb41E25670c8aA8682BEF91b57", "ZiGSoulboundToken": "0xCb55bd7417896584ca4bE4a6E2EE1FDFD4102c2B", "ZiGGameFiToken": "0xEa53a8C2743f350371B74304386C9c87e390468E", "ZiGRWAToken": "0x3cc3889521e8067A8b689d5819C47aFe7e0f8174", "SoulReparationNFT": "0x5a55fA2077BE6Bd25F361f4E98045F76788F4B04"}}