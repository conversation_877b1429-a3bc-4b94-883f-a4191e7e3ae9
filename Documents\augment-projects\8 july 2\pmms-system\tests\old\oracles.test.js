const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("Oracle System", function () {
  it("should return valid price from MultiOracle", async function () {
    const multiOracle = await ethers.getContractAt("MultiOracle", "******************************************");
    const price = await multiOracle.getLatestPrice("USD", "XAU");
    expect(price).to.be.gt(0);
  });
});
