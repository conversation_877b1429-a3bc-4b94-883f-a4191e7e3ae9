// 🧪 SIMPLE API TEST - Direct API call to understand the issue
import axios from 'axios';

async function testDirectAPI() {
  console.log('🧪 Testing direct API call...');
  
  try {
    console.log('📡 Making request to DLMM API...');
    const response = await axios.get('https://dlmm-api.meteora.ag/pair/all', {
      timeout: 30000,
      headers: { 'Content-Type': 'application/json' }
    });
    
    console.log('✅ Response received!');
    console.log('📊 Response status:', response.status);
    console.log('📊 Data type:', typeof response.data);
    console.log('📊 Data length:', Array.isArray(response.data) ? response.data.length : 'Not array');
    
    if (Array.isArray(response.data) && response.data.length > 0) {
      console.log('\n📊 First pool sample:');
      console.log(JSON.stringify(response.data[0], null, 2));
      
      console.log('\n📊 Available fields:');
      console.log(Object.keys(response.data[0]).sort());
      
      // Look for pools with volume
      const poolsWithData = response.data.filter(pool => {
        return pool.volume_24h && parseFloat(pool.volume_24h) > 0;
      });
      
      console.log(`\n📊 Pools with volume_24h > 0: ${poolsWithData.length}`);
      
      if (poolsWithData.length > 0) {
        console.log('\n📊 Sample pool with volume:');
        console.log(JSON.stringify(poolsWithData[0], null, 2));
      }
    }
    
  } catch (error) {
    console.error('❌ Direct API test failed:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

testDirectAPI().catch(console.error);
