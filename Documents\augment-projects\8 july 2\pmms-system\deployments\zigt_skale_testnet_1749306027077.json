{"network": "skale_testnet", "chainId": "2043395295", "deployer": "0xE0282D77cF60BA484e13d24fd5686A6618F09A3B", "timestamp": "2025-06-07T14:20:27.077Z", "contracts": {"FeedRegistry": "0x7e13BDe021D167177ae34E756E9bcA7007e5d564", "BandFeedRegistry": "0xFaCb3836dBde5107eAdB94bc54e5f4759f420FFB", "GovernanceToken": "0x865A0789ecc1c829217f40B7325151462e587F49", "RedistributionVault": "0xa3255A24378BaFf8Be01Cb9dbA8C621a7f04f7F4", "AccessVerifier": "0xfa70A1C51a835343Cef1a1F93750841695677E8c", "ReparationsDAO": "0x840Fc73aafa6Cc7EC7E654FC39EE18fb5B82Ba22", "OracleHub": "0xD58da14029B2bE87b16498f701b776AD03e7291d", "MainZiGT": {"target": "0xFC872CeD8F874261F5B1C5c1e67CBAb3aC1c59A0", "interface": {"fragments": [{"type": "constructor", "inputs": [], "payable": false, "gas": null}, {"type": "error", "inputs": [{"name": "target", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "AddressEmptyCode"}, {"type": "error", "inputs": [{"name": "implementation", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC1967InvalidImplementation"}, {"type": "error", "inputs": [], "name": "ERC1967Non<PERSON>ayable"}, {"type": "error", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "allowance", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "needed", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InsufficientAllowance"}, {"type": "error", "inputs": [{"name": "sender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "balance", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "needed", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InsufficientBalance"}, {"type": "error", "inputs": [{"name": "approver", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidApprover"}, {"type": "error", "inputs": [{"name": "receiver", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidReceiver"}, {"type": "error", "inputs": [{"name": "sender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidSender"}, {"type": "error", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidSpender"}, {"type": "error", "inputs": [], "name": "FailedCall"}, {"type": "error", "inputs": [], "name": "InvalidInitialization"}, {"type": "error", "inputs": [], "name": "NotInitializing"}, {"type": "error", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnableInvalidOwner"}, {"type": "error", "inputs": [{"name": "account", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnableUnauthorizedAccount"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_MulDiv18_Overflow"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "denominator", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_MulDiv_Overflow"}, {"type": "error", "inputs": [], "name": "PRBMath_SD59x18_Div_InputTooSmall"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Div_Overflow"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Exp2_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Exp_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_UD60x18_Exp2_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_UD60x18_Log_InputTooSmall"}, {"type": "error", "inputs": [], "name": "ReentrancyGuardReentrantCall"}, {"type": "error", "inputs": [], "name": "UUPSUnauthorizedCallContext"}, {"type": "error", "inputs": [{"name": "slot", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "UUPSUnsupportedProxiableUUID"}, {"type": "event", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "spender", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Approval", "anonymous": false}, {"type": "event", "inputs": [{"name": "registry", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "BandFeedRegistrySet", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "refund", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Burned", "anonymous": false}, {"type": "event", "inputs": [{"name": "direction", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "ratio", "type": "tuple", "baseType": "tuple", "indexed": false, "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "ConfigurationUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "FeeCharged", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "GovernanceTransferInitiated", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "GovernanceTransferred", "anonymous": false}, {"type": "event", "inputs": [{"name": "version", "type": "uint64", "baseType": "uint64", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Initialized", "anonymous": false}, {"type": "event", "inputs": [{"name": "messageId", "type": "bytes32", "baseType": "bytes32", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "MessageReceived", "anonymous": false}, {"type": "event", "inputs": [{"name": "messageId", "type": "bytes32", "baseType": "bytes32", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "MessageSent", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "cost", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Minted", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "chainlinkFeed", "type": "address", "baseType": "address", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OracleUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousOwner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "new<PERSON>wner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnershipTransferred", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "price", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "timestamp", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PriceCached", "anonymous": false}, {"type": "event", "inputs": [{"name": "symbols", "type": "string[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "prices", "type": "uint256[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "timestamps", "type": "uint256[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}], "name": "PricesRetrieved", "anonymous": false}, {"type": "event", "inputs": [], "name": "Rebalanced", "anonymous": false}, {"type": "event", "inputs": [{"name": "model", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ReparationsModelSet", "anonymous": false}, {"type": "event", "inputs": [{"name": "newStrategy", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "StrategyUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "from", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "to", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Transfer", "anonymous": false}, {"type": "event", "inputs": [{"name": "sigmoidEnd", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "linearEnd", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "UpdatedThresholds", "anonymous": false}, {"type": "event", "inputs": [{"name": "implementation", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Upgraded", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newWeight", "type": "int256", "baseType": "int256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "WeightUpdated", "anonymous": false}, {"type": "function", "inputs": [], "name": "DEST_CHAIN_SELECTOR", "constant": true, "outputs": [{"name": "", "type": "uint64", "baseType": "uint64", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "__governance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "allowance", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "approve", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "assetOracles", "constant": true, "outputs": [{"name": "chainlinkFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "assetWeights", "constant": true, "outputs": [{"name": "", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "account", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "balanceOf", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "bandFeedRegistry", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "basePrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "burn", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "cachedPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "cachedTimestamp", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "calculateZiGTValue", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "destination", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "crossChainMint", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "decimals", "constant": true, "outputs": [{"name": "", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "expBase", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "feePercentage", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "getAllPrices", "constant": false, "outputs": [{"name": "symbols", "type": "string[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "prices", "type": "uint256[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "timestamps", "type": "uint256[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "getPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "getPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "governance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "i_router", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "ccipRouter", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeedRegistry_", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "initialGovernance", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "direction", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "ratio", "type": "tuple", "baseType": "tuple", "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "ccipRouter", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "initialOwner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_governance", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "lastRebalance", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "linearPhaseEnd", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "linearSlope", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "mint", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "mint", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "name", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "owner", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "pendingGovernance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "positions", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "proxiableUUID", "constant": true, "outputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "rebalance", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "rebalanceCooldown", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "renounceOwnership", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "reparationsModel", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "router<PERSON>ddress", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "selectedDirection", "constant": true, "outputs": [{"name": "", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "selectedRatio", "constant": true, "outputs": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "config", "type": "tuple", "baseType": "tuple", "components": [{"name": "chainlinkFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "<PERSON><PERSON><PERSON><PERSON>", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "sigmoidPhaseEnd", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "symbol", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "totalMinted", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "totalSupply", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transfer", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "from", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transferFrom", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "new<PERSON>wner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transferOwnership", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "updateCachedPrice", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKeys", "type": "bytes32[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}}], "name": "updateCachedPrices", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_sigmoidEnd", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "_linearEnd", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "updatePhases", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_newStrategy", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "_newRatio", "type": "tuple", "baseType": "tuple", "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "updateStrategy", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "newImplementation", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "data", "type": "bytes", "baseType": "bytes", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "upgradeToAndCall", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "version", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}], "deploy": {"type": "constructor", "inputs": [], "payable": false, "gas": null}, "fallback": null, "receive": false}, "runner": "<SignerWithAddress 0xE0282D77cF60BA484e13d24fd5686A6618F09A3B>", "filters": {}, "fallback": null}, "ZiG-S": {"target": "0xFd7d04e49ed42ea21279948ab3b1Ea0d58655de7", "interface": {"fragments": [{"type": "constructor", "inputs": [], "payable": false, "gas": null}, {"type": "error", "inputs": [{"name": "target", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "AddressEmptyCode"}, {"type": "error", "inputs": [{"name": "implementation", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC1967InvalidImplementation"}, {"type": "error", "inputs": [], "name": "ERC1967Non<PERSON>ayable"}, {"type": "error", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "allowance", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "needed", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InsufficientAllowance"}, {"type": "error", "inputs": [{"name": "sender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "balance", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "needed", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InsufficientBalance"}, {"type": "error", "inputs": [{"name": "approver", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidApprover"}, {"type": "error", "inputs": [{"name": "receiver", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidReceiver"}, {"type": "error", "inputs": [{"name": "sender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidSender"}, {"type": "error", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidSpender"}, {"type": "error", "inputs": [], "name": "FailedCall"}, {"type": "error", "inputs": [], "name": "InvalidInitialization"}, {"type": "error", "inputs": [], "name": "NotInitializing"}, {"type": "error", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnableInvalidOwner"}, {"type": "error", "inputs": [{"name": "account", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnableUnauthorizedAccount"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_MulDiv18_Overflow"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "denominator", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_MulDiv_Overflow"}, {"type": "error", "inputs": [], "name": "PRBMath_SD59x18_Div_InputTooSmall"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Div_Overflow"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Exp2_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Exp_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_UD60x18_Exp2_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_UD60x18_Log_InputTooSmall"}, {"type": "error", "inputs": [], "name": "ReentrancyGuardReentrantCall"}, {"type": "error", "inputs": [], "name": "UUPSUnauthorizedCallContext"}, {"type": "error", "inputs": [{"name": "slot", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "UUPSUnsupportedProxiableUUID"}, {"type": "event", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "spender", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Approval", "anonymous": false}, {"type": "event", "inputs": [{"name": "registry", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "BandFeedRegistrySet", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "refund", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Burned", "anonymous": false}, {"type": "event", "inputs": [{"name": "direction", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "ratio", "type": "tuple", "baseType": "tuple", "indexed": false, "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "ConfigurationUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "FeeCharged", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "GovernanceTransferInitiated", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "GovernanceTransferred", "anonymous": false}, {"type": "event", "inputs": [{"name": "version", "type": "uint64", "baseType": "uint64", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Initialized", "anonymous": false}, {"type": "event", "inputs": [{"name": "messageId", "type": "bytes32", "baseType": "bytes32", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "MessageReceived", "anonymous": false}, {"type": "event", "inputs": [{"name": "messageId", "type": "bytes32", "baseType": "bytes32", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "MessageSent", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "cost", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Minted", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "chainlinkFeed", "type": "address", "baseType": "address", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OracleUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousOwner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "new<PERSON>wner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnershipTransferred", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "price", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "timestamp", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PriceCached", "anonymous": false}, {"type": "event", "inputs": [{"name": "symbols", "type": "string[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "prices", "type": "uint256[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "timestamps", "type": "uint256[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}], "name": "PricesRetrieved", "anonymous": false}, {"type": "event", "inputs": [], "name": "Rebalanced", "anonymous": false}, {"type": "event", "inputs": [{"name": "model", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ReparationsModelSet", "anonymous": false}, {"type": "event", "inputs": [{"name": "newStrategy", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "StrategyUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "from", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "to", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Transfer", "anonymous": false}, {"type": "event", "inputs": [{"name": "sigmoidEnd", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "linearEnd", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "UpdatedThresholds", "anonymous": false}, {"type": "event", "inputs": [{"name": "implementation", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Upgraded", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newWeight", "type": "int256", "baseType": "int256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "WeightUpdated", "anonymous": false}, {"type": "function", "inputs": [], "name": "DEST_CHAIN_SELECTOR", "constant": true, "outputs": [{"name": "", "type": "uint64", "baseType": "uint64", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "__governance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "allowance", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "approve", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "assetOracles", "constant": true, "outputs": [{"name": "chainlinkFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "assetWeights", "constant": true, "outputs": [{"name": "", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "account", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "balanceOf", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "bandFeedRegistry", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "basePrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "burn", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "cachedPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "cachedTimestamp", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "calculateZiGTValue", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "destination", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "crossChainMint", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "decimals", "constant": true, "outputs": [{"name": "", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "expBase", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "feePercentage", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "getAllPrices", "constant": false, "outputs": [{"name": "symbols", "type": "string[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "prices", "type": "uint256[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "timestamps", "type": "uint256[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "getPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "getPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "governance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "i_router", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "ccipRouter", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeedRegistry_", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "initialGovernance", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "direction", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "ratio", "type": "tuple", "baseType": "tuple", "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "ccipRouter", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "initialOwner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_governance", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "lastRebalance", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "linearPhaseEnd", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "linearSlope", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "mint", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "mint", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "name", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "owner", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "pendingGovernance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "positions", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "proxiableUUID", "constant": true, "outputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "rebalance", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "rebalanceCooldown", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "renounceOwnership", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "reparationsModel", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "router<PERSON>ddress", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "selectedDirection", "constant": true, "outputs": [{"name": "", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "selectedRatio", "constant": true, "outputs": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "config", "type": "tuple", "baseType": "tuple", "components": [{"name": "chainlinkFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "<PERSON><PERSON><PERSON><PERSON>", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "sigmoidPhaseEnd", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "symbol", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "totalMinted", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "totalSupply", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transfer", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "from", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transferFrom", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "new<PERSON>wner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transferOwnership", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "updateCachedPrice", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKeys", "type": "bytes32[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}}], "name": "updateCachedPrices", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_sigmoidEnd", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "_linearEnd", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "updatePhases", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_newStrategy", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "_newRatio", "type": "tuple", "baseType": "tuple", "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "updateStrategy", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "newImplementation", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "data", "type": "bytes", "baseType": "bytes", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "upgradeToAndCall", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "version", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}], "deploy": {"type": "constructor", "inputs": [], "payable": false, "gas": null}, "fallback": null, "receive": false}, "runner": "<SignerWithAddress 0xE0282D77cF60BA484e13d24fd5686A6618F09A3B>", "filters": {}, "fallback": null}, "ZiG-SO": {"target": "0x02B6e518683731a2A339fB38B8A26d9ACBBC903c", "interface": {"fragments": [{"type": "constructor", "inputs": [], "payable": false, "gas": null}, {"type": "error", "inputs": [{"name": "target", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "AddressEmptyCode"}, {"type": "error", "inputs": [{"name": "implementation", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC1967InvalidImplementation"}, {"type": "error", "inputs": [], "name": "ERC1967Non<PERSON>ayable"}, {"type": "error", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "allowance", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "needed", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InsufficientAllowance"}, {"type": "error", "inputs": [{"name": "sender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "balance", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "needed", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InsufficientBalance"}, {"type": "error", "inputs": [{"name": "approver", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidApprover"}, {"type": "error", "inputs": [{"name": "receiver", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidReceiver"}, {"type": "error", "inputs": [{"name": "sender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidSender"}, {"type": "error", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidSpender"}, {"type": "error", "inputs": [], "name": "FailedCall"}, {"type": "error", "inputs": [], "name": "InvalidInitialization"}, {"type": "error", "inputs": [], "name": "NotInitializing"}, {"type": "error", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnableInvalidOwner"}, {"type": "error", "inputs": [{"name": "account", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnableUnauthorizedAccount"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_MulDiv18_Overflow"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "denominator", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_MulDiv_Overflow"}, {"type": "error", "inputs": [], "name": "PRBMath_SD59x18_Div_InputTooSmall"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Div_Overflow"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Exp2_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Exp_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_UD60x18_Exp2_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_UD60x18_Log_InputTooSmall"}, {"type": "error", "inputs": [], "name": "ReentrancyGuardReentrantCall"}, {"type": "error", "inputs": [], "name": "UUPSUnauthorizedCallContext"}, {"type": "error", "inputs": [{"name": "slot", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "UUPSUnsupportedProxiableUUID"}, {"type": "event", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "spender", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Approval", "anonymous": false}, {"type": "event", "inputs": [{"name": "registry", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "BandFeedRegistrySet", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "refund", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Burned", "anonymous": false}, {"type": "event", "inputs": [{"name": "direction", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "ratio", "type": "tuple", "baseType": "tuple", "indexed": false, "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "ConfigurationUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "FeeCharged", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "GovernanceTransferInitiated", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "GovernanceTransferred", "anonymous": false}, {"type": "event", "inputs": [{"name": "version", "type": "uint64", "baseType": "uint64", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Initialized", "anonymous": false}, {"type": "event", "inputs": [{"name": "messageId", "type": "bytes32", "baseType": "bytes32", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "MessageReceived", "anonymous": false}, {"type": "event", "inputs": [{"name": "messageId", "type": "bytes32", "baseType": "bytes32", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "MessageSent", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "cost", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Minted", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "chainlinkFeed", "type": "address", "baseType": "address", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OracleUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousOwner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "new<PERSON>wner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnershipTransferred", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "price", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "timestamp", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PriceCached", "anonymous": false}, {"type": "event", "inputs": [{"name": "symbols", "type": "string[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "prices", "type": "uint256[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "timestamps", "type": "uint256[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}], "name": "PricesRetrieved", "anonymous": false}, {"type": "event", "inputs": [], "name": "Rebalanced", "anonymous": false}, {"type": "event", "inputs": [{"name": "model", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ReparationsModelSet", "anonymous": false}, {"type": "event", "inputs": [{"name": "newStrategy", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "StrategyUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "from", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "to", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Transfer", "anonymous": false}, {"type": "event", "inputs": [{"name": "sigmoidEnd", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "linearEnd", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "UpdatedThresholds", "anonymous": false}, {"type": "event", "inputs": [{"name": "implementation", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Upgraded", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newWeight", "type": "int256", "baseType": "int256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "WeightUpdated", "anonymous": false}, {"type": "function", "inputs": [], "name": "DEST_CHAIN_SELECTOR", "constant": true, "outputs": [{"name": "", "type": "uint64", "baseType": "uint64", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "__governance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "allowance", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "approve", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "assetOracles", "constant": true, "outputs": [{"name": "chainlinkFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "assetWeights", "constant": true, "outputs": [{"name": "", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "account", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "balanceOf", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "bandFeedRegistry", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "basePrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "burn", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "cachedPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "cachedTimestamp", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "calculateZiGTValue", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "destination", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "crossChainMint", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "decimals", "constant": true, "outputs": [{"name": "", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "expBase", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "feePercentage", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "getAllPrices", "constant": false, "outputs": [{"name": "symbols", "type": "string[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "prices", "type": "uint256[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "timestamps", "type": "uint256[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "getPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "getPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "governance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "i_router", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "ccipRouter", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeedRegistry_", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "initialGovernance", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "direction", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "ratio", "type": "tuple", "baseType": "tuple", "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "ccipRouter", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "initialOwner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_governance", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "lastRebalance", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "linearPhaseEnd", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "linearSlope", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "mint", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "mint", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "name", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "owner", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "pendingGovernance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "positions", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "proxiableUUID", "constant": true, "outputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "rebalance", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "rebalanceCooldown", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "renounceOwnership", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "reparationsModel", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "router<PERSON>ddress", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "selectedDirection", "constant": true, "outputs": [{"name": "", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "selectedRatio", "constant": true, "outputs": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "config", "type": "tuple", "baseType": "tuple", "components": [{"name": "chainlinkFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "<PERSON><PERSON><PERSON><PERSON>", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "sigmoidPhaseEnd", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "symbol", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "totalMinted", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "totalSupply", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transfer", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "from", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transferFrom", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "new<PERSON>wner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transferOwnership", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "updateCachedPrice", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKeys", "type": "bytes32[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}}], "name": "updateCachedPrices", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_sigmoidEnd", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "_linearEnd", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "updatePhases", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_newStrategy", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "_newRatio", "type": "tuple", "baseType": "tuple", "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "updateStrategy", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "newImplementation", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "data", "type": "bytes", "baseType": "bytes", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "upgradeToAndCall", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "version", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}], "deploy": {"type": "constructor", "inputs": [], "payable": false, "gas": null}, "fallback": null, "receive": false}, "runner": "<SignerWithAddress 0xE0282D77cF60BA484e13d24fd5686A6618F09A3B>", "filters": {}, "fallback": null}, "ZiG-DF": {"target": "0xCE2B74085948728bB03Ea42734c48dD7a2531bCa", "interface": {"fragments": [{"type": "constructor", "inputs": [], "payable": false, "gas": null}, {"type": "error", "inputs": [{"name": "target", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "AddressEmptyCode"}, {"type": "error", "inputs": [{"name": "implementation", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC1967InvalidImplementation"}, {"type": "error", "inputs": [], "name": "ERC1967Non<PERSON>ayable"}, {"type": "error", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "allowance", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "needed", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InsufficientAllowance"}, {"type": "error", "inputs": [{"name": "sender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "balance", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "needed", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InsufficientBalance"}, {"type": "error", "inputs": [{"name": "approver", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidApprover"}, {"type": "error", "inputs": [{"name": "receiver", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidReceiver"}, {"type": "error", "inputs": [{"name": "sender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidSender"}, {"type": "error", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidSpender"}, {"type": "error", "inputs": [], "name": "FailedCall"}, {"type": "error", "inputs": [], "name": "InvalidInitialization"}, {"type": "error", "inputs": [], "name": "NotInitializing"}, {"type": "error", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnableInvalidOwner"}, {"type": "error", "inputs": [{"name": "account", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnableUnauthorizedAccount"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_MulDiv18_Overflow"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "denominator", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_MulDiv_Overflow"}, {"type": "error", "inputs": [], "name": "PRBMath_SD59x18_Div_InputTooSmall"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Div_Overflow"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Exp2_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Exp_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_UD60x18_Exp2_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_UD60x18_Log_InputTooSmall"}, {"type": "error", "inputs": [], "name": "ReentrancyGuardReentrantCall"}, {"type": "error", "inputs": [], "name": "UUPSUnauthorizedCallContext"}, {"type": "error", "inputs": [{"name": "slot", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "UUPSUnsupportedProxiableUUID"}, {"type": "event", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "spender", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Approval", "anonymous": false}, {"type": "event", "inputs": [{"name": "registry", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "BandFeedRegistrySet", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "refund", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Burned", "anonymous": false}, {"type": "event", "inputs": [{"name": "direction", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "ratio", "type": "tuple", "baseType": "tuple", "indexed": false, "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "ConfigurationUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "FeeCharged", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "GovernanceTransferInitiated", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "GovernanceTransferred", "anonymous": false}, {"type": "event", "inputs": [{"name": "version", "type": "uint64", "baseType": "uint64", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Initialized", "anonymous": false}, {"type": "event", "inputs": [{"name": "messageId", "type": "bytes32", "baseType": "bytes32", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "MessageReceived", "anonymous": false}, {"type": "event", "inputs": [{"name": "messageId", "type": "bytes32", "baseType": "bytes32", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "MessageSent", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "cost", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Minted", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "chainlinkFeed", "type": "address", "baseType": "address", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OracleUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousOwner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "new<PERSON>wner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnershipTransferred", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "price", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "timestamp", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PriceCached", "anonymous": false}, {"type": "event", "inputs": [{"name": "symbols", "type": "string[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "prices", "type": "uint256[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "timestamps", "type": "uint256[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}], "name": "PricesRetrieved", "anonymous": false}, {"type": "event", "inputs": [], "name": "Rebalanced", "anonymous": false}, {"type": "event", "inputs": [{"name": "model", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ReparationsModelSet", "anonymous": false}, {"type": "event", "inputs": [{"name": "newStrategy", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "StrategyUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "from", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "to", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Transfer", "anonymous": false}, {"type": "event", "inputs": [{"name": "sigmoidEnd", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "linearEnd", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "UpdatedThresholds", "anonymous": false}, {"type": "event", "inputs": [{"name": "implementation", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Upgraded", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newWeight", "type": "int256", "baseType": "int256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "WeightUpdated", "anonymous": false}, {"type": "function", "inputs": [], "name": "DEST_CHAIN_SELECTOR", "constant": true, "outputs": [{"name": "", "type": "uint64", "baseType": "uint64", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "__governance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "allowance", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "approve", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "assetOracles", "constant": true, "outputs": [{"name": "chainlinkFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "assetWeights", "constant": true, "outputs": [{"name": "", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "account", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "balanceOf", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "bandFeedRegistry", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "basePrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "burn", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "cachedPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "cachedTimestamp", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "calculateZiGTValue", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "destination", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "crossChainMint", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "decimals", "constant": true, "outputs": [{"name": "", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "expBase", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "feePercentage", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "getAllPrices", "constant": false, "outputs": [{"name": "symbols", "type": "string[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "prices", "type": "uint256[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "timestamps", "type": "uint256[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "getPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "getPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "governance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "i_router", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "ccipRouter", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeedRegistry_", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "initialGovernance", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "direction", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "ratio", "type": "tuple", "baseType": "tuple", "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "ccipRouter", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "initialOwner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_governance", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "lastRebalance", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "linearPhaseEnd", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "linearSlope", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "mint", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "mint", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "name", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "owner", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "pendingGovernance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "positions", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "proxiableUUID", "constant": true, "outputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "rebalance", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "rebalanceCooldown", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "renounceOwnership", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "reparationsModel", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "router<PERSON>ddress", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "selectedDirection", "constant": true, "outputs": [{"name": "", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "selectedRatio", "constant": true, "outputs": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "config", "type": "tuple", "baseType": "tuple", "components": [{"name": "chainlinkFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "<PERSON><PERSON><PERSON><PERSON>", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "sigmoidPhaseEnd", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "symbol", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "totalMinted", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "totalSupply", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transfer", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "from", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transferFrom", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "new<PERSON>wner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transferOwnership", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "updateCachedPrice", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKeys", "type": "bytes32[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}}], "name": "updateCachedPrices", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_sigmoidEnd", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "_linearEnd", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "updatePhases", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_newStrategy", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "_newRatio", "type": "tuple", "baseType": "tuple", "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "updateStrategy", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "newImplementation", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "data", "type": "bytes", "baseType": "bytes", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "upgradeToAndCall", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "version", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}], "deploy": {"type": "constructor", "inputs": [], "payable": false, "gas": null}, "fallback": null, "receive": false}, "runner": "<SignerWithAddress 0xE0282D77cF60BA484e13d24fd5686A6618F09A3B>", "filters": {}, "fallback": null}, "ZiG-AC": {"target": "0xa48620E59EcD4De9498c20F6DaE5bF23F77f3F2c", "interface": {"fragments": [{"type": "constructor", "inputs": [], "payable": false, "gas": null}, {"type": "error", "inputs": [{"name": "target", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "AddressEmptyCode"}, {"type": "error", "inputs": [{"name": "implementation", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC1967InvalidImplementation"}, {"type": "error", "inputs": [], "name": "ERC1967Non<PERSON>ayable"}, {"type": "error", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "allowance", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "needed", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InsufficientAllowance"}, {"type": "error", "inputs": [{"name": "sender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "balance", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "needed", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InsufficientBalance"}, {"type": "error", "inputs": [{"name": "approver", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidApprover"}, {"type": "error", "inputs": [{"name": "receiver", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidReceiver"}, {"type": "error", "inputs": [{"name": "sender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidSender"}, {"type": "error", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ERC20InvalidSpender"}, {"type": "error", "inputs": [], "name": "FailedCall"}, {"type": "error", "inputs": [], "name": "InvalidInitialization"}, {"type": "error", "inputs": [], "name": "NotInitializing"}, {"type": "error", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnableInvalidOwner"}, {"type": "error", "inputs": [{"name": "account", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnableUnauthorizedAccount"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_MulDiv18_Overflow"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "denominator", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_MulDiv_Overflow"}, {"type": "error", "inputs": [], "name": "PRBMath_SD59x18_Div_InputTooSmall"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "y", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Div_Overflow"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Exp2_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_SD59x18_Exp_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_UD60x18_Exp2_InputTooBig"}, {"type": "error", "inputs": [{"name": "x", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PRBMath_UD60x18_Log_InputTooSmall"}, {"type": "error", "inputs": [], "name": "ReentrancyGuardReentrantCall"}, {"type": "error", "inputs": [], "name": "UUPSUnauthorizedCallContext"}, {"type": "error", "inputs": [{"name": "slot", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "UUPSUnsupportedProxiableUUID"}, {"type": "event", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "spender", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Approval", "anonymous": false}, {"type": "event", "inputs": [{"name": "registry", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "BandFeedRegistrySet", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "refund", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Burned", "anonymous": false}, {"type": "event", "inputs": [{"name": "direction", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "ratio", "type": "tuple", "baseType": "tuple", "indexed": false, "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "ConfigurationUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "FeeCharged", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "GovernanceTransferInitiated", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newGovernance", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "GovernanceTransferred", "anonymous": false}, {"type": "event", "inputs": [{"name": "version", "type": "uint64", "baseType": "uint64", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Initialized", "anonymous": false}, {"type": "event", "inputs": [{"name": "messageId", "type": "bytes32", "baseType": "bytes32", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "MessageReceived", "anonymous": false}, {"type": "event", "inputs": [{"name": "messageId", "type": "bytes32", "baseType": "bytes32", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "MessageSent", "anonymous": false}, {"type": "event", "inputs": [{"name": "user", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "cost", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Minted", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "chainlinkFeed", "type": "address", "baseType": "address", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OracleUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "previousOwner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "new<PERSON>wner", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "OwnershipTransferred", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "price", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "timestamp", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "PriceCached", "anonymous": false}, {"type": "event", "inputs": [{"name": "symbols", "type": "string[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "prices", "type": "uint256[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "timestamps", "type": "uint256[]", "baseType": "array", "indexed": false, "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}], "name": "PricesRetrieved", "anonymous": false}, {"type": "event", "inputs": [], "name": "Rebalanced", "anonymous": false}, {"type": "event", "inputs": [{"name": "model", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "ReparationsModelSet", "anonymous": false}, {"type": "event", "inputs": [{"name": "newStrategy", "type": "uint8", "baseType": "uint8", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "StrategyUpdated", "anonymous": false}, {"type": "event", "inputs": [{"name": "from", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "to", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Transfer", "anonymous": false}, {"type": "event", "inputs": [{"name": "sigmoidEnd", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "linearEnd", "type": "uint256", "baseType": "uint256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "UpdatedThresholds", "anonymous": false}, {"type": "event", "inputs": [{"name": "implementation", "type": "address", "baseType": "address", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "Upgraded", "anonymous": false}, {"type": "event", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "indexed": true, "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "newWeight", "type": "int256", "baseType": "int256", "indexed": false, "components": null, "arrayLength": null, "arrayChildren": null}], "name": "WeightUpdated", "anonymous": false}, {"type": "function", "inputs": [], "name": "DEST_CHAIN_SELECTOR", "constant": true, "outputs": [{"name": "", "type": "uint64", "baseType": "uint64", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "UPGRADE_INTERFACE_VERSION", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "__governance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "owner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "allowance", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "spender", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "approve", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "assetOracles", "constant": true, "outputs": [{"name": "chainlinkFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "assetWeights", "constant": true, "outputs": [{"name": "", "type": "int256", "baseType": "int256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "account", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "balanceOf", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "bandFeedRegistry", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "basePrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "burn", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "cachedPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "cachedTimestamp", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "calculateZiGTValue", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "destination", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "crossChainMint", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "decimals", "constant": true, "outputs": [{"name": "", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "expBase", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "feePercentage", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "getAllPrices", "constant": false, "outputs": [{"name": "symbols", "type": "string[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "prices", "type": "uint256[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}, {"name": "timestamps", "type": "uint256[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "getPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "getPrice", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "governance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "i_router", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "ccipRouter", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeedRegistry_", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "initialGovernance", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "direction", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "ratio", "type": "tuple", "baseType": "tuple", "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "ccipRouter", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "initialOwner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_governance", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "initialize", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "lastRebalance", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "linearPhaseEnd", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "linearSlope", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "mint", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "amount", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "mint", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "name", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "owner", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "pendingGovernance", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "positions", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "proxiableUUID", "constant": true, "outputs": [{"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "rebalance", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "rebalanceCooldown", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "renounceOwnership", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "reparationsModel", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "router<PERSON>ddress", "constant": true, "outputs": [{"name": "", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "selectedDirection", "constant": true, "outputs": [{"name": "", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "selectedRatio", "constant": true, "outputs": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "config", "type": "tuple", "baseType": "tuple", "components": [{"name": "chainlinkFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "bandFeed", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "maxPriceAge", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "decimals", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "description", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "<PERSON><PERSON><PERSON><PERSON>", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "sigmoidPhaseEnd", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "symbol", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "totalMinted", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [], "name": "totalSupply", "constant": true, "outputs": [{"name": "", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transfer", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "from", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "to", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "value", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transferFrom", "constant": false, "outputs": [{"name": "", "type": "bool", "baseType": "bool", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "new<PERSON>wner", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "transferOwnership", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKey", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "updateCachedPrice", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "assetKeys", "type": "bytes32[]", "baseType": "array", "components": null, "arrayLength": -1, "arrayChildren": {"name": "", "type": "bytes32", "baseType": "bytes32", "components": null, "arrayLength": null, "arrayChildren": null}}], "name": "updateCachedPrices", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_sigmoidEnd", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "_linearEnd", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "updatePhases", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "_newStrategy", "type": "uint8", "baseType": "uint8", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "_newRatio", "type": "tuple", "baseType": "tuple", "components": [{"name": "metals", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "fiat", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "crypto", "type": "uint256", "baseType": "uint256", "components": null, "arrayLength": null, "arrayChildren": null}], "arrayLength": null, "arrayChildren": null}], "name": "updateStrategy", "constant": false, "outputs": [], "stateMutability": "nonpayable", "payable": false, "gas": null}, {"type": "function", "inputs": [{"name": "newImplementation", "type": "address", "baseType": "address", "components": null, "arrayLength": null, "arrayChildren": null}, {"name": "data", "type": "bytes", "baseType": "bytes", "components": null, "arrayLength": null, "arrayChildren": null}], "name": "upgradeToAndCall", "constant": false, "outputs": [], "stateMutability": "payable", "payable": true, "gas": null}, {"type": "function", "inputs": [], "name": "version", "constant": true, "outputs": [{"name": "", "type": "string", "baseType": "string", "components": null, "arrayLength": null, "arrayChildren": null}], "stateMutability": "view", "payable": false, "gas": null}], "deploy": {"type": "constructor", "inputs": [], "payable": false, "gas": null}, "fallback": null, "receive": false}, "runner": "<SignerWithAddress 0xE0282D77cF60BA484e13d24fd5686A6618F09A3B>", "filters": {}, "fallback": null}, "ReparationsModel": "0x2e511b9d8Bc42B4f775019C42c8E8d6B1AcDCEeD", "ZiGGovernance": "0x8a1c8Cf742b97F2A16Db5CDCc47A2E3b3E5072eD", "ZiGUtilityToken": "0x0600F4932201B28Eac7F64DA3203E99A17992Ca0", "ZiGMemeToken": "0xAd388f1AC304260Ea96A23Bb6245E35135055aD6", "ZiGNFT": "0xa31dF2Ed9d6949a98A23c1B3870A40945cAc835A", "ZiGSoulboundToken": "0xC65DfD5f5EDD454E100A374314A0044526c06aEe", "ZiGGameFiToken": "0xADB0C27b99C35E50DF61F23931c7C77477106cCC", "ZiGRWAToken": "0x4835F188bE814bB9C4f536d27Df3ACC3a181C34c", "SoulReparationNFT": "0xA9dFAE9C9AB230E0ceD9b55cE7594e9D4F2a3400"}}