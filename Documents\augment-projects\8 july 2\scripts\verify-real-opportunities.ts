import { ethers } from 'hardhat';

// VERIFY REAL OPPORTUNITIES WITH ACTUAL DEX DATA
const ROUTERS = {
  QUICKSWAP: '******************************************',
  SUSHISWAP: '******************************************'
};

const TOKENS = {
  WETH: '******************************************',
  WMATIC: '******************************************'
};

async function verifyRealOpportunities() {
  console.log('\n🔍 VERIFYING REAL ARBITRAGE OPPORTUNITIES');
  console.log('=' .repeat(80));

  const [verifier] = await ethers.getSigners();
  const network = await ethers.provider.getNetwork();
  
  console.log(`Network: ${network.name} (${network.chainId})`);
  console.log(`Verifier: ${verifier.address}`);
  console.log(`Time: ${new Date().toLocaleTimeString()}`);
  
  if (network.chainId !== 137) {
    throw new Error(`Wrong network! Expected 137, got ${network.chainId}`);
  }

  // Connect to routers
  const quickswapRouter = new ethers.Contract(
    ROUTERS.QUICKSWAP,
    ['function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)'],
    ethers.provider
  );

  const sushiswapRouter = new ethers.Contract(
    ROUTERS.SUSHISWAP,
    ['function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)'],
    ethers.provider
  );

  console.log('\n💰 TESTING REAL ARBITRAGE OPPORTUNITIES...');

  // Test with small amounts first
  const testAmounts = [
    { amount: ethers.utils.parseEther('0.1'), label: '0.1 WETH' },
    { amount: ethers.utils.parseEther('0.5'), label: '0.5 WETH' },
    { amount: ethers.utils.parseEther('1'), label: '1 WETH' },
    { amount: ethers.utils.parseEther('2'), label: '2 WETH' }
  ];

  for (const testAmount of testAmounts) {
    console.log(`\n🔷 Testing ${testAmount.label} arbitrage:`);
    
    try {
      // Paths
      const pathWETHtoWMATIC = [TOKENS.WETH, TOKENS.WMATIC];
      const pathWMATICtoWETH = [TOKENS.WMATIC, TOKENS.WETH];
      
      // Strategy 1: WETH → WMATIC on QuickSwap, WMATIC → WETH on SushiSwap
      console.log(`   Strategy 1: QuickSwap → SushiSwap`);
      
      const qs_weth_to_wmatic = await quickswapRouter.getAmountsOut(testAmount.amount, pathWETHtoWMATIC);
      const wmaticFromQS = qs_weth_to_wmatic[1];
      console.log(`     QuickSwap: ${testAmount.label} → ${ethers.utils.formatEther(wmaticFromQS)} WMATIC`);
      
      const ss_wmatic_to_weth = await sushiswapRouter.getAmountsOut(wmaticFromQS, pathWMATICtoWETH);
      const finalWETH1 = ss_wmatic_to_weth[1];
      console.log(`     SushiSwap: ${ethers.utils.formatEther(wmaticFromQS)} WMATIC → ${ethers.utils.formatEther(finalWETH1)} WETH`);
      
      const profit1 = finalWETH1.sub(testAmount.amount);
      const profitPercent1 = profit1.mul(10000).div(testAmount.amount).toNumber() / 100;
      console.log(`     Profit: ${ethers.utils.formatEther(profit1)} WETH (${profitPercent1.toFixed(3)}%)`);
      
      // Strategy 2: WETH → WMATIC on SushiSwap, WMATIC → WETH on QuickSwap
      console.log(`   Strategy 2: SushiSwap → QuickSwap`);
      
      const ss_weth_to_wmatic = await sushiswapRouter.getAmountsOut(testAmount.amount, pathWETHtoWMATIC);
      const wmaticFromSS = ss_weth_to_wmatic[1];
      console.log(`     SushiSwap: ${testAmount.label} → ${ethers.utils.formatEther(wmaticFromSS)} WMATIC`);
      
      const qs_wmatic_to_weth = await quickswapRouter.getAmountsOut(wmaticFromSS, pathWMATICtoWETH);
      const finalWETH2 = qs_wmatic_to_weth[1];
      console.log(`     QuickSwap: ${ethers.utils.formatEther(wmaticFromSS)} WMATIC → ${ethers.utils.formatEther(finalWETH2)} WETH`);
      
      const profit2 = finalWETH2.sub(testAmount.amount);
      const profitPercent2 = profit2.mul(10000).div(testAmount.amount).toNumber() / 100;
      console.log(`     Profit: ${ethers.utils.formatEther(profit2)} WETH (${profitPercent2.toFixed(3)}%)`);
      
      // Determine best strategy
      let bestStrategy = null;
      let bestProfit = ethers.BigNumber.from(0);
      let bestPercent = 0;
      
      if (profit1.gt(0) && profit1.gt(profit2)) {
        bestStrategy = "QuickSwap → SushiSwap";
        bestProfit = profit1;
        bestPercent = profitPercent1;
      } else if (profit2.gt(0)) {
        bestStrategy = "SushiSwap → QuickSwap";
        bestProfit = profit2;
        bestPercent = profitPercent2;
      }
      
      if (bestStrategy && bestPercent > 0.05) { // 0.05% minimum
        console.log(`   🎯 PROFITABLE: ${bestStrategy}`);
        console.log(`   💰 Best Profit: ${ethers.utils.formatEther(bestProfit)} WETH (${bestPercent.toFixed(3)}%)`);
        
        // Calculate after flash loan fee (0.09%)
        const flashLoanFee = testAmount.amount.mul(9).div(10000);
        const netProfit = bestProfit.sub(flashLoanFee);
        const netProfitUSD = parseFloat(ethers.utils.formatEther(netProfit)) * 3000;
        
        console.log(`   📊 After flash loan fee: ${ethers.utils.formatEther(netProfit)} WETH (~$${netProfitUSD.toFixed(2)})`);
        
        if (netProfit.gt(0) && netProfitUSD > 5) {
          console.log(`   ✅ EXECUTABLE! Net profit > $5`);
        } else {
          console.log(`   ⚠️  Too small after fees`);
        }
      } else {
        console.log(`   ❌ Not profitable (best: ${bestPercent.toFixed(3)}%)`);
      }
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message.substring(0, 50)}...`);
    }
  }

  console.log('\n🔍 VERIFICATION COMPLETE');
  console.log('💡 Look for opportunities with >0.1% profit after fees');
  console.log('🚀 Execute immediately when found - opportunities expire quickly!');
}

// Execute verification
if (require.main === module) {
  verifyRealOpportunities()
    .then(() => {
      console.log('\n🎉 OPPORTUNITY VERIFICATION COMPLETED!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 VERIFICATION FAILED:', error);
      process.exit(1);
    });
}

export { verifyRealOpportunities };
