const { ethers } = require('hardhat');

/**
 * 🔍 TEST ULTIMATE DEX ARBITRAGE SYSTEM
 * Diagnose what's failing in the execution
 */

async function testUltimateDexArbitrage() {
  console.log('\n🔍 TESTING ULTIMATE DEX ARBITRAGE SYSTEM');
  console.log('💡 DIAGNOSING EXECUTION FAILURE');
  console.log('🎯 CONTRACT: ******************************************');
  console.log('=' .repeat(80));

  try {
    const [deployer] = await ethers.getSigners();
    
    console.log(`👤 Deployer: ${deployer.address}`);
    console.log(`💰 Balance: ${ethers.utils.formatEther(await deployer.getBalance())} POL`);

    // Connect to deployed contract
    const UltimateDexArbitrageSystem = await ethers.getContractFactory('UltimateDexArbitrageSystem');
    const ultimateArb = UltimateDexArbitrageSystem.attach('******************************************');
    
    console.log(`✅ Connected to contract: ${ultimateArb.address}`);

    // Test individual DEX calls first
    console.log('\n🔍 TESTING INDIVIDUAL DEX CALLS...');
    
    const testAmount = ethers.utils.parseUnits('1000', 6); // $1K test
    const usdcAddress = await ultimateArb.USDC();
    const wethAddress = await ultimateArb.WETH();
    
    console.log(`📊 Testing with ${ethers.utils.formatUnits(testAmount, 6)} USDC`);
    console.log(`   USDC: ${usdcAddress}`);
    console.log(`   WETH: ${wethAddress}`);
    
    // Test QuickSwap
    try {
      const quickswap = new ethers.Contract(
        await ultimateArb.QUICKSWAP(),
        ['function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)'],
        deployer
      );
      
      const path = [usdcAddress, wethAddress];
      const quickswapAmounts = await quickswap.getAmountsOut(testAmount, path);
      console.log(`✅ QuickSwap: ${ethers.utils.formatEther(quickswapAmounts[1])} WETH`);
    } catch (error) {
      console.log(`❌ QuickSwap failed: ${error.message}`);
    }
    
    // Test SushiSwap
    try {
      const sushiswap = new ethers.Contract(
        await ultimateArb.SUSHISWAP(),
        ['function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)'],
        deployer
      );
      
      const path = [usdcAddress, wethAddress];
      const sushiswapAmounts = await sushiswap.getAmountsOut(testAmount, path);
      console.log(`✅ SushiSwap: ${ethers.utils.formatEther(sushiswapAmounts[1])} WETH`);
    } catch (error) {
      console.log(`❌ SushiSwap failed: ${error.message}`);
    }
    
    // Test Balancer flash loan capability
    console.log('\n🔍 TESTING BALANCER FLASH LOAN...');
    
    try {
      const balancer = new ethers.Contract(
        await ultimateArb.BALANCER(),
        ['function flashLoan(address recipient, address[] memory tokens, uint256[] memory amounts, bytes memory userData) external'],
        deployer
      );
      
      console.log(`✅ Balancer contract accessible: ${balancer.address}`);
      
      // Test with smaller amount first
      const smallAmount = ethers.utils.parseUnits('100', 6); // $100 test
      
      console.log(`\n🚀 TESTING WITH SMALLER AMOUNT: ${ethers.utils.formatUnits(smallAmount, 6)} USDC`);
      
      // Try to estimate gas for smaller flash loan
      try {
        const gasEstimate = await ultimateArb.estimateGas.executeUltimateDexArbitrage();
        console.log(`⛽ Gas estimate: ${gasEstimate.toLocaleString()}`);
      } catch (gasError) {
        console.log(`❌ Gas estimation failed: ${gasError.reason || gasError.message}`);
        
        // Try to call the function statically to see what fails
        try {
          await ultimateArb.callStatic.executeUltimateDexArbitrage();
          console.log(`✅ Static call succeeded - execution should work`);
        } catch (staticError) {
          console.log(`❌ Static call failed: ${staticError.reason || staticError.message}`);
          
          if (staticError.reason) {
            console.log(`💡 Specific revert reason: ${staticError.reason}`);
          }
          
          // Check if it's a gas issue
          if (staticError.message.includes('gas')) {
            console.log(`🔧 SOLUTION: Increase gas limit`);
          }
          
          // Check if it's a DEX issue
          if (staticError.message.includes('INSUFFICIENT_OUTPUT_AMOUNT') || 
              staticError.message.includes('UniswapV2: INSUFFICIENT_LIQUIDITY')) {
            console.log(`🔧 SOLUTION: DEX liquidity issue - need to handle failed swaps`);
          }
          
          // Check if it's a flash loan issue
          if (staticError.message.includes('flash') || staticError.message.includes('Balancer')) {
            console.log(`🔧 SOLUTION: Flash loan configuration issue`);
          }
        }
      }
      
    } catch (error) {
      console.log(`❌ Balancer test failed: ${error.message}`);
    }
    
    // Check current gas price
    const currentGasPrice = await ethers.provider.getGasPrice();
    const maxGasPrice = await ultimateArb.MAX_GAS_PRICE();
    
    console.log(`\n📊 GAS ANALYSIS:`);
    console.log(`   Current gas price: ${ethers.utils.formatUnits(currentGasPrice, 'gwei')} gwei`);
    console.log(`   Max gas price: ${ethers.utils.formatUnits(maxGasPrice, 'gwei')} gwei`);
    console.log(`   Gas check: ${currentGasPrice.lte(maxGasPrice) ? '✅ PASS' : '❌ FAIL'}`);
    
    // Get contract results
    const results = await ultimateArb.getResults();
    console.log(`\n📊 CONTRACT STATE:`);
    console.log(`   Last profit: ${ethers.utils.formatUnits(results.profit, 6)} USDC`);
    console.log(`   Last success: ${results.success}`);
    console.log(`   Total executions: ${results.executions}`);
    console.log(`   Success rate: ${results.successRate / 100}%`);
    
    console.log('\n💡 DIAGNOSIS COMPLETE!');
    console.log('🔧 Check the error messages above for specific fixes needed');
    
    return {
      success: true,
      diagnosed: true,
      address: ultimateArb.address
    };
    
  } catch (error) {
    console.error('\n💥 DIAGNOSIS FAILED:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

// Execute diagnosis
if (require.main === module) {
  testUltimateDexArbitrage()
    .then((result) => {
      console.log('\n🎉 DIAGNOSIS COMPLETED!');
      if (result.success) {
        console.log('✅ Found specific issues to fix');
      } else {
        console.log('❌ Need to investigate further');
      }
    })
    .catch((error) => {
      console.error('Diagnosis failed:', error.message);
    });
}

module.exports = { testUltimateDexArbitrage };
