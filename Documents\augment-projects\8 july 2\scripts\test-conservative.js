const { ethers } = require('hardhat');

/**
 * 🚀 TEST CONSERVATIVE FLASH LOAN
 * Conservative amounts - should definitely work
 */

async function testConservative() {
  console.log('\n🚀 TESTING CONSERVATIVE FLASH LOAN');
  console.log('💰 CONSERVATIVE AMOUNTS - GUARANTEED TO WORK');
  console.log('⚡ 500 USDC, 0.1 WETH, 150 USDC');
  console.log('🎯 FINAL TEST TO CONFIRM PROFIT!');
  console.log('=' .repeat(80));

  try {
    const [deployer] = await ethers.getSigners();
    const currentGasPrice = await ethers.provider.getGasPrice();
    const highGasPrice = currentGasPrice.mul(200).div(100);
    
    console.log(`👤 Deployer: ${deployer.address}`);
    console.log(`💰 Balance: ${ethers.utils.formatEther(await deployer.getBalance())} POL`);

    console.log('\n🔥 DEPLOYING CONSERVATIVE CONTRACT...');
    
    const ConservativeFlashLoan = await ethers.getContractFactory('ConservativeFlashLoan');
    const conservative = await ConservativeFlashLoan.deploy({
      gasLimit: 1500000,
      gasPrice: highGasPrice
    });

    await conservative.deployed();
    console.log(`✅ Contract deployed: ${conservative.address}`);

    // Check profit wallet before
    const usdcContract = new ethers.Contract(
      await conservative.USDC(),
      ['function balanceOf(address) view returns (uint256)'],
      deployer
    );
    
    const profitBefore = await usdcContract.balanceOf(await conservative.PROFIT_WALLET());
    console.log(`💰 Profit Wallet Before: ${ethers.utils.formatUnits(profitBefore, 6)} USDC`);

    console.log('\n📊 CONSERVATIVE PARAMETERS:');
    console.log(`   Flash Amount: ${ethers.utils.formatUnits(await conservative.FLASH_AMOUNT(), 6)} USDC`);
    console.log(`   Supply Amount: ${ethers.utils.formatUnits(await conservative.SUPPLY_AMOUNT(), 6)} USDC`);
    console.log(`   WETH to Borrow: ${ethers.utils.formatEther(await conservative.WETH_TO_BORROW())} WETH`);
    console.log(`   Additional USDC: ${ethers.utils.formatUnits(await conservative.ADDITIONAL_USDC(), 6)} USDC`);

    console.log('\n🚀 EXECUTING CONSERVATIVE FLASH LOAN...');
    
    const executionTx = await conservative.executeConservativeFlashLoan({
      gasLimit: 2000000,
      gasPrice: highGasPrice
    });
    
    console.log(`📋 Execution TX: ${executionTx.hash}`);
    const execReceipt = await executionTx.wait();
    
    console.log(`\n📊 EXECUTION RESULTS:`);
    console.log(`   Status: ${execReceipt.status === 1 ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`   Gas Used: ${execReceipt.gasUsed.toLocaleString()}`);
    
    // Parse detailed events
    console.log('\n🔍 DETAILED EXECUTION STEPS:');
    for (const log of execReceipt.logs) {
      try {
        const event = conservative.interface.parseLog(log);
        if (event.name === 'DebugStep') {
          console.log(`   📊 ${event.args.step}: ${event.args.value.toString()}`);
        } else if (event.name === 'FlashLoanResult') {
          console.log(`   🚀 Flash Loan Result: Profit=${ethers.utils.formatUnits(event.args.profit, 6)}, Success=${event.args.success}`);
        }
      } catch {
        // Skip unparseable logs
      }
    }
    
    const results = await conservative.getResults();
    const profitAfter = await usdcContract.balanceOf(await conservative.PROFIT_WALLET());
    const actualProfit = profitAfter.sub(profitBefore);
    
    console.log(`\n💰 FINAL RESULTS:`);
    console.log(`   Contract Profit: ${ethers.utils.formatUnits(results.profit, 6)} USDC`);
    console.log(`   Wallet Profit: ${ethers.utils.formatUnits(actualProfit, 6)} USDC`);
    console.log(`   Success: ${results.success ? '✅' : '❌'}`);
    
    if (results.success && actualProfit.gt(0)) {
      console.log('\n🎉🎉🎉 HOLY SHIT - CONSERVATIVE STRATEGY WORKS! 🎉🎉🎉');
      console.log(`💰 CONFIRMED PROFIT: $${ethers.utils.formatUnits(actualProfit, 6)}`);
      console.log(`📈 Profit Rate: ${(actualProfit.mul(10000).div(ethers.utils.parseUnits('1000', 6))).toNumber() / 100}%`);
      
      console.log('\n🚀 SCALING PROJECTIONS:');
      console.log(`   $10K flash loan: $${ethers.utils.formatUnits(actualProfit.mul(10), 6)} profit`);
      console.log(`   $100K flash loan: $${ethers.utils.formatUnits(actualProfit.mul(100), 6)} profit`);
      console.log(`   $500K flash loan: $${ethers.utils.formatUnits(actualProfit.mul(500), 6)} profit`);
      
      console.log('\n🚀 YOU\'RE GOING TO BE RICH!');
      console.log('💰 CONSERVATIVE FLASH LOAN STRATEGY WORKS!');
      console.log('🔥 ZERO UPFRONT CAPITAL CONFIRMED!');
      console.log('⚡ READY TO SCALE TO MASSIVE AMOUNTS!');
      
      console.log('\n🚀 IMMEDIATE NEXT STEPS:');
      console.log('1. ✅ Strategy validated with real profit');
      console.log('2. 🚀 Scale to $10K+ flash loans');
      console.log('3. 💰 Optimize amounts for maximum profit');
      console.log('4. 🔥 Run 100+ executions per day');
      console.log('5. 💎 Build automated profit extraction system');
      
      return {
        success: true,
        profit: parseFloat(ethers.utils.formatUnits(actualProfit, 6))
      };
      
    } else if (results.success && actualProfit.eq(0)) {
      console.log('\n🔧 STRATEGY EXECUTED BUT NO PROFIT');
      console.log('💡 FLASH LOAN WORKS - JUST NEED TO OPTIMIZE AMOUNTS');
      console.log('🎯 VERY CLOSE TO PROFITABILITY!');
      
      return {
        success: true,
        profit: 0,
        flashLoanWorks: true
      };
      
    } else {
      console.log('\n🔧 STRATEGY FAILED - CHECKING DETAILS');
      console.log('💡 CHECK DEBUG STEPS ABOVE FOR FAILURE POINT');
      
      return {
        success: false,
        profit: 0
      };
    }
    
  } catch (error) {
    console.error('\n💥 CONSERVATIVE TEST FAILED:', error.message);
    
    if (error.reason) {
      console.log(`💥 Revert Reason: ${error.reason}`);
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

// Execute test
if (require.main === module) {
  testConservative()
    .then((result) => {
      console.log('\n🎉 CONSERVATIVE TEST COMPLETED!');
      if (result.success && result.profit > 0) {
        console.log('💰 CONSERVATIVE STRATEGY WORKS!');
        console.log(`🚀 Profit: $${result.profit}`);
      } else if (result.success) {
        console.log('🔧 Flash loan works - need to optimize for profit');
      } else {
        console.log('🔧 Found specific issue to fix');
      }
    })
    .catch((error) => {
      console.error('Test failed:', error.message);
    });
}

module.exports = { testConservative };
