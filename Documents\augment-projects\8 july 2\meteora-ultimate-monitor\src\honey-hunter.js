// 🍯 HONEY HUNTER - The ultimate DLMM pool hunter
import chalk from 'chalk';
import Table from 'cli-table3';
import MeteoraAPIClient from './api-client.js';
import HoneyAnalyzer from './honey-analyzer.js';
import EarlyDetector from './early-detector.js';
import UltimateBlockchainIntelligence from './ultimate-blockchain-intelligence.js';
// import SmartContractIntelligence from './smart-contract-client.js'; // Optional - requires Anchor
import { CONFIG } from './config.js';

class HoneyHunter {
  constructor() {
    this.apiClient = new MeteoraAPIClient();
    this.analyzer = new HoneyAnalyzer();
    this.earlyDetector = new EarlyDetector();
    this.ultimateIntelligence = new UltimateBlockchainIntelligence();
    // this.smartContract = new SmartContractIntelligence(); // Optional
    this.isRunning = false;
    this.topPools = [];
    this.emergingPools = [];
    this.smartContractPools = [];
    this.ultimateTop20 = [];
    this.alerts = [];
    this.scanCount = 0;
    this.useSmartContract = false;
    this.useUltimateIntelligence = true; // 🧠 ULTIMATE BLOCKCHAIN INTELLIGENCE ACTIVE
  }

  // 🚀 START THE HUNT
  async startHunt() {
    console.log(chalk.yellow.bold('🧠 METEORA ULTIMATE WIZARD STARTING...'));
    console.log(chalk.cyan('🎯 Deep blockchain analysis for TOP 20 pools with ROBUST criteria'));

    // 🧠 ULTIMATE BLOCKCHAIN INTELLIGENCE
    console.log(chalk.magenta.bold('🧠 ULTIMATE BLOCKCHAIN INTELLIGENCE ACTIVE'));
    console.log(chalk.green('✅ Deep analysis of ALL Meteora DLMM pools - FREE!'));
    console.log(chalk.cyan('🎯 Applying ROBUST criteria to find only the BEST opportunities'));
    console.log(chalk.yellow('💰 Comprehensive transaction analysis, wallet tracking, risk assessment'));

    // Initialize ultimate intelligence
    console.log(chalk.blue('🔍 Initializing ultimate blockchain intelligence system...'));
    console.log(chalk.green('📊 Multi-layer analysis: Volume, Fees, Wallets, Consistency, Risk'));
    console.log(chalk.magenta('🏆 Goal: Identify TOP 20 pools with highest profit potential'));

    this.isRunning = true;

    // Initial scan
    await this.performHoneyScan();

    // Set up continuous monitoring
    setInterval(async () => {
      if (this.isRunning) {
        await this.performHoneyScan();
      }
    }, CONFIG.MONITORING.UPDATE_INTERVAL);

    // Display dashboard
    this.startDashboard();
  }

  // 🧠 PERFORM ULTIMATE INTELLIGENCE SCAN
  async performHoneyScan() {
    try {
      console.log(chalk.blue('🧠 Performing ultimate blockchain intelligence scan...'));

      // 🧠 ULTIMATE BLOCKCHAIN INTELLIGENCE (Primary source - DEEP ANALYSIS!)
      if (this.useUltimateIntelligence) {
        await this.performUltimateIntelligenceScan();
      }

      // 📊 API INTELLIGENCE (Validation source)
      await this.performAPIScan();

    } catch (error) {
      console.error(chalk.red('❌ Error in ultimate intelligence scan:'), error.message);
    }
  }

  // 🧠 ULTIMATE INTELLIGENCE SCAN
  async performUltimateIntelligenceScan() {
    try {
      console.log(chalk.magenta('🧠 Performing deep blockchain analysis with robust criteria...'));

      // Get TOP 20 pools with comprehensive analysis
      this.ultimateTop20 = await this.ultimateIntelligence.performDeepBlockchainScan();

      console.log(chalk.green(`✅ Ultimate Intelligence: Found ${this.ultimateTop20.length} TOP pools with robust criteria`));

      // Generate ultimate alerts
      this.generateUltimateAlerts();

    } catch (error) {
      console.error(chalk.red('❌ Ultimate intelligence scan failed:'), error.message);
      this.useUltimateIntelligence = false;
    }
  }

  // 🚨 GENERATE ULTIMATE ALERTS
  generateUltimateAlerts() {
    if (this.ultimateTop20.length === 0) return;

    // Top scoring pool alert
    const topPool = this.ultimateTop20[0];
    this.alerts.push(`🏆 TOP POOL: Score ${topPool.ultimateScore.toFixed(2)} - $${topPool.transactionAnalysis.totalVolume24h.toFixed(0)} volume!`);

    // High consistency pools
    const consistentPools = this.ultimateTop20.filter(pool => pool.profitabilityMetrics.profitConsistency > 0.8);
    if (consistentPools.length > 0) {
      this.alerts.push(`🎯 HIGH CONSISTENCY: ${consistentPools.length} pools with 80%+ profit consistency!`);
    }

    // Low risk high reward pools
    const lowRiskHighReward = this.ultimateTop20.filter(pool =>
      pool.riskAssessment.overallRisk === 'LOW' && pool.ultimateScore > 100
    );
    if (lowRiskHighReward.length > 0) {
      this.alerts.push(`💎 LOW RISK HIGH REWARD: ${lowRiskHighReward.length} pools with low risk and high scores!`);
    }

    // High wallet diversity
    const diversePools = this.ultimateTop20.filter(pool => pool.transactionAnalysis.uniqueWalletCount > 50);
    if (diversePools.length > 0) {
      this.alerts.push(`👥 HIGH DIVERSITY: ${diversePools.length} pools with 50+ unique wallets!`);
    }
  }

  // 🔥 DIRECT BLOCKCHAIN SCAN (THE ULTIMATE SOURCE)
  async performDirectBlockchainScan() {
    try {
      console.log(chalk.magenta('🔥 Reading directly from Meteora DLMM blockchain...'));

      // Get concentration intelligence directly from blockchain
      this.blockchainPools = await this.blockchainReader.getConcentrationIntelligence();

      console.log(chalk.green(`✅ Blockchain Direct: Found ${this.blockchainPools.length} concentration opportunities`));

      // Generate alerts from blockchain data
      this.generateBlockchainAlerts();

    } catch (error) {
      console.error(chalk.red('❌ Direct blockchain scan failed:'), error.message);
      this.useBlockchainDirect = false;
    }
  }

  // 🚨 GENERATE BLOCKCHAIN ALERTS
  generateBlockchainAlerts() {
    if (this.blockchainPools.length === 0) return;

    // Ultra concentration alert
    const ultraConcentrated = this.blockchainPools.filter(pool => pool.concentration > 100);
    ultraConcentrated.forEach(pool => {
      this.alerts.push(`🔥 ULTRA CONCENTRATION: ${pool.concentration.toFixed(2)} concentration score!`);
    });

    // Smart money alert
    const smartMoney = this.blockchainPools.filter(pool => pool.walletActivity.activeWallets > 10);
    smartMoney.forEach(pool => {
      this.alerts.push(`👥 SMART MONEY: ${pool.walletActivity.activeWallets} profitable wallets active!`);
    });

    // Ultimate score alert
    const ultimateOpportunities = this.blockchainPools.filter(pool => pool.ultimateScore > 200);
    ultimateOpportunities.forEach(pool => {
      this.alerts.push(`🏆 ULTIMATE OPPORTUNITY: ${pool.ultimateScore.toFixed(2)} ultimate score!`);
    });
  }

  // 🧠 SMART CONTRACT SCAN
  async performSmartContractScan() {
    try {
      console.log(chalk.magenta('🧠 Querying Smart Contract Intelligence...'));

      // Get top pools from smart contract
      this.smartContractPools = await this.smartContract.getTopPools();

      // Get smart contract alerts
      const scAlerts = await this.smartContract.getAlerts();
      this.alerts.push(...scAlerts);

      console.log(chalk.green(`✅ Smart Contract: Found ${this.smartContractPools.length} top pools`));

    } catch (error) {
      console.error(chalk.red('❌ Smart contract scan failed:'), error.message);
      this.useSmartContract = false;
    }
  }

  // 📊 API SCAN
  async performAPIScan() {
    try {
      // Get all qualifying pools
      const pools = await this.apiClient.scanForConcentration();

      if (!pools.length) {
        console.log(chalk.red('❌ No qualifying pools found'));
        return;
      }
      
      // Analyze each pool for honey potential AND early signals
      const analyzedPools = [];
      const emergingPools = [];

      for (const pool of pools) {
        // Regular honey analysis
        const analysis = this.analyzer.analyzePool(pool);
        if (analysis && analysis.honeyScore > 0) {
          analyzedPools.push(analysis);

          // Check for alerts
          const alerts = this.analyzer.checkAlerts(analysis);
          this.alerts.push(...alerts);
        }

        // EARLY DETECTION ANALYSIS - This is the magic!
        const earlySignals = this.earlyDetector.analyzeEarlySignals(pool);
        if (earlySignals.isEmerging) {
          emergingPools.push(earlySignals);

          // Create early detection alerts
          const earlyAlerts = this.createEarlyAlerts(earlySignals);
          this.alerts.push(...earlyAlerts);
        }
      }
      
      // Sort by honey score
      this.topPools = analyzedPools
        .sort((a, b) => b.honeyScore - a.honeyScore)
        .slice(0, CONFIG.DISPLAY.TOP_POOLS_COUNT);

      // Sort emerging pools by confidence
      this.emergingPools = emergingPools
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, 5); // Top 5 emerging pools

      this.scanCount++;
      console.log(chalk.green(`✅ Scan #${this.scanCount}: Analyzed ${analyzedPools.length} honey pools, found ${this.emergingPools.length} emerging opportunities`));
      
      // Process alerts
      this.processAlerts();
      
    } catch (error) {
      console.error(chalk.red('❌ Error in honey scan:'), error.message);
    }
  }

  // 🚀 CREATE EARLY ALERTS
  createEarlyAlerts(earlySignals) {
    const alerts = [];

    earlySignals.signals.forEach(signal => {
      alerts.push({
        type: 'EARLY_DETECTION',
        message: `🚀 EMERGING: ${earlySignals.poolName} - ${signal.message} (${earlySignals.confidence.toFixed(1)}% confidence)`,
        priority: signal.priority,
        timeToExplosion: earlySignals.timeToExplosion
      });
    });

    return alerts;
  }

  // 🚨 PROCESS ALERTS
  processAlerts() {
    const newAlerts = this.alerts.slice(-15); // Last 15 alerts

    for (const alert of newAlerts) {
      let color = chalk.blue;
      let icon = '🔔';

      if (alert.type === 'EARLY_DETECTION') {
        color = chalk.magenta;
        icon = '🚀';
      } else if (alert.priority === 'HIGH') {
        color = chalk.red;
        icon = '🔥';
      } else if (alert.priority === 'MEDIUM') {
        color = chalk.yellow;
        icon = '⚡';
      }

      console.log(color.bold(`${icon} ${alert.message}`));
      if (alert.timeToExplosion) {
        console.log(color(`   ⏰ Expected explosion: ${alert.timeToExplosion}`));
      }
    }
  }

  // 📊 START DASHBOARD
  startDashboard() {
    setInterval(() => {
      this.displayDashboard();
    }, CONFIG.DISPLAY.REFRESH_RATE);
  }

  // 🎨 DISPLAY DASHBOARD
  displayDashboard() {
    console.clear();
    
    // Header
    console.log(chalk.yellow.bold('🍯 METEORA ULTIMATE WIZARD DASHBOARD 🍯'));
    console.log(chalk.cyan(`Last Update: ${new Date().toLocaleTimeString()}`));

    // Data source indicator
    if (this.useSmartContract) {
      console.log(chalk.green.bold('🧠 SMART CONTRACT INTELLIGENCE ACTIVE'));
    } else {
      console.log(chalk.yellow.bold('📊 API FALLBACK MODE'));
    }

    console.log(chalk.gray('═'.repeat(80)));
    
    // 🧠 SMART CONTRACT POOLS (Priority display)
    if (this.useSmartContract && this.smartContractPools.length > 0) {
      console.log(chalk.magenta.bold('🧠 SMART CONTRACT TOP POOLS (Profit per $1)'));

      const scTable = new Table({
        head: [
          chalk.magenta('Rank'),
          chalk.magenta('Pool'),
          chalk.magenta('Profit per $1'),
          chalk.magenta('Honey Score'),
          chalk.magenta('Annual Return'),
          chalk.magenta('Risk'),
          chalk.magenta('TVL')
        ],
        colWidths: [6, 18, 14, 12, 14, 12, 12]
      });

      this.smartContractPools.slice(0, 10).forEach((pool, index) => {
        const rank = (index + 1).toString();
        const name = `${pool.tokenX}/${pool.tokenY}`.length > 16 ?
          `${pool.tokenX}/${pool.tokenY}`.substring(0, 13) + '...' :
          `${pool.tokenX}/${pool.tokenY}`;
        const profitPerDollar = chalk.green.bold(`$${pool.profitPerDollar.toFixed(3)}`);
        const honeyScore = chalk.yellow(pool.honeyScore.toFixed(1));
        const annualReturn = chalk.cyan(`${(pool.profitPerDollar * 100).toFixed(1)}%`);
        const risk = this.getRiskColor(pool.riskLevel);
        const tvl = chalk.blue('$' + this.formatNumber(pool.tvl));

        scTable.push([rank, name, profitPerDollar, honeyScore, annualReturn, risk, tvl]);
      });

      console.log(scTable.toString());
      console.log(chalk.gray('═'.repeat(80)));
    }

    if (!this.topPools.length && (!this.useSmartContract || !this.smartContractPools.length)) {
      console.log(chalk.red('❌ No honey pools found yet...'));
      return;
    }
    
    // Top Pools Table
    const table = new Table({
      head: [
        chalk.yellow('Rank'),
        chalk.yellow('Pool'),
        chalk.yellow('Category'),
        chalk.yellow('Honey Score'),
        chalk.yellow('Fee/TVL %'),
        chalk.yellow('Volume 24h'),
        chalk.yellow('Fees/Hour'),
        chalk.yellow('Risk')
      ],
      colWidths: [6, 20, 18, 12, 12, 15, 12, 12]
    });
    
    this.topPools.forEach((pool, index) => {
      const rank = (index + 1).toString();
      const name = pool.name.length > 18 ? pool.name.substring(0, 15) + '...' : pool.name;
      const category = this.getCategoryColor(pool.category);
      const score = chalk.green.bold(pool.honeyScore.toFixed(1));
      const feeRatio = chalk.cyan(pool.feeToTvlRatio.toFixed(3) + '%');
      const volume = chalk.blue('$' + this.formatNumber(pool.volume24h));
      const feesPerHour = chalk.green('$' + this.formatNumber(pool.feeGenerationRate));
      const risk = this.getRiskColor(pool.riskLevel);
      
      table.push([rank, name, category, score, feeRatio, volume, feesPerHour, risk]);
    });
    
    console.log(table.toString());

    // EMERGING POOLS SECTION - The early detection magic!
    if (this.emergingPools.length > 0) {
      console.log(chalk.gray('═'.repeat(80)));
      console.log(chalk.magenta.bold('🚀 EMERGING OPPORTUNITIES (EARLY DETECTION)'));

      const emergingTable = new Table({
        head: [
          chalk.magenta('Pool'),
          chalk.magenta('Confidence'),
          chalk.magenta('Signals'),
          chalk.magenta('Time to Explosion'),
          chalk.magenta('Trend')
        ],
        colWidths: [20, 12, 30, 18, 15]
      });

      this.emergingPools.forEach(pool => {
        const name = pool.poolName.length > 18 ? pool.poolName.substring(0, 15) + '...' : pool.poolName;
        const confidence = chalk.cyan.bold(pool.confidence.toFixed(1) + '%');
        const signalCount = chalk.yellow(pool.signals.length + ' signals');
        const timeToExplosion = chalk.green(pool.timeToExplosion || 'Unknown');
        const trend = this.getTrendColor(pool.trend);

        emergingTable.push([name, confidence, signalCount, timeToExplosion, trend]);
      });

      console.log(emergingTable.toString());
    }

    // Summary Stats
    console.log(chalk.gray('═'.repeat(80)));
    this.displaySummaryStats();
    
    // Recent Alerts
    if (this.alerts.length > 0) {
      console.log(chalk.gray('═'.repeat(80)));
      console.log(chalk.yellow.bold('🚨 RECENT ALERTS:'));
      
      const recentAlerts = this.alerts.slice(-5);
      recentAlerts.forEach(alert => {
        const color = alert.priority === 'HIGH' ? chalk.red : 
                     alert.priority === 'MEDIUM' ? chalk.yellow : chalk.blue;
        console.log(color(`• ${alert.message}`));
      });
    }
  }

  // 📈 DISPLAY SUMMARY STATS
  displaySummaryStats() {
    if (!this.topPools.length) return;
    
    const totalVolume = this.topPools.reduce((sum, pool) => sum + pool.volume24h, 0);
    const totalFees = this.topPools.reduce((sum, pool) => sum + (pool.feeGenerationRate * 24), 0);
    const avgHoneyScore = this.topPools.reduce((sum, pool) => sum + pool.honeyScore, 0) / this.topPools.length;
    const honeyPools = this.topPools.filter(pool => pool.category !== 'NO HONEY 🚫').length;
    
    const emergingOpportunities = this.emergingPools.length;
    const avgConfidence = emergingOpportunities > 0 ?
      this.emergingPools.reduce((sum, pool) => sum + pool.confidence, 0) / emergingOpportunities : 0;

    console.log(chalk.cyan.bold('📊 ULTIMATE WIZARD INTELLIGENCE SUMMARY:'));

    // Smart Contract Stats
    if (this.useSmartContract && this.smartContractPools.length > 0) {
      const topSCPool = this.smartContractPools[0];
      const scTotalTVL = this.smartContractPools.reduce((sum, pool) => sum + pool.tvl, 0);
      const avgProfitPerDollar = this.smartContractPools.reduce((sum, pool) => sum + pool.profitPerDollar, 0) / this.smartContractPools.length;

      console.log(chalk.magenta.bold('🧠 SMART CONTRACT INTELLIGENCE:'));
      console.log(chalk.white(`• Top Pool Profit: ${chalk.green('$' + topSCPool.profitPerDollar.toFixed(3))} per $1 (${chalk.cyan((topSCPool.profitPerDollar * 100).toFixed(1))}% annual)`));
      console.log(chalk.white(`• Smart Contract TVL: ${chalk.green('$' + this.formatNumber(scTotalTVL))}`));
      console.log(chalk.white(`• Average Profit/Dollar: ${chalk.yellow('$' + avgProfitPerDollar.toFixed(3))}`));
      console.log(chalk.white(`• On-Chain Pools Tracked: ${chalk.blue(this.smartContractPools.length)}`));
    }

    // API Stats
    if (this.topPools.length > 0) {
      console.log(chalk.blue.bold('📊 API INTELLIGENCE:'));
      console.log(chalk.white(`• Total Volume (Top 10): ${chalk.green('$' + this.formatNumber(totalVolume))}`));
      console.log(chalk.white(`• Total Daily Fees: ${chalk.green('$' + this.formatNumber(totalFees))}`));
      console.log(chalk.white(`• Average Honey Score: ${chalk.yellow(avgHoneyScore.toFixed(1))}`));
      console.log(chalk.white(`• Honey Pools Found: ${chalk.yellow(honeyPools)} / ${this.topPools.length}`));
    }

    // General Stats
    console.log(chalk.cyan.bold('🎯 SYSTEM STATUS:'));
    console.log(chalk.white(`• Emerging Opportunities: ${chalk.magenta(emergingOpportunities)} (${chalk.cyan(avgConfidence.toFixed(1))}% avg confidence)`));
    console.log(chalk.white(`• Scans Completed: ${chalk.blue(this.scanCount)}`));
    console.log(chalk.white(`• Intelligence Mode: ${this.useSmartContract ? chalk.green('SMART CONTRACT') : chalk.yellow('API FALLBACK')}`));
  }

  // 🎨 GET CATEGORY COLOR
  getCategoryColor(category) {
    if (category.includes('ULTRA')) return chalk.red.bold(category);
    if (category.includes('PREMIUM')) return chalk.magenta.bold(category);
    if (category.includes('GOOD')) return chalk.yellow.bold(category);
    if (category.includes('POTENTIAL')) return chalk.blue.bold(category);
    return chalk.gray(category);
  }

  // 🎨 GET RISK COLOR
  getRiskColor(risk) {
    if (risk.includes('HIGH')) return chalk.red.bold(risk);
    if (risk.includes('MEDIUM')) return chalk.yellow.bold(risk);
    if (risk.includes('LOW')) return chalk.green.bold(risk);
    return chalk.gray(risk);
  }

  // 🎨 GET TREND COLOR
  getTrendColor(trend) {
    switch(trend) {
      case 'STRONG_UPTREND': return chalk.green.bold('📈 STRONG UP');
      case 'UPTREND': return chalk.green('📈 UP');
      case 'SIDEWAYS': return chalk.yellow('➡️ SIDEWAYS');
      case 'DOWNTREND': return chalk.red('📉 DOWN');
      case 'STRONG_DOWNTREND': return chalk.red.bold('📉 STRONG DOWN');
      default: return chalk.gray('❓ UNKNOWN');
    }
  }

  // 🔢 FORMAT NUMBER
  formatNumber(num) {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toFixed(0);
  }

  // 🛑 STOP HUNT
  stopHunt() {
    this.isRunning = false;
    console.log(chalk.red.bold('🛑 Honey hunt stopped'));
  }
}

// 🚀 START THE HUNT
const hunter = new HoneyHunter();
hunter.startHunt().catch(console.error);

// Handle graceful shutdown
process.on('SIGINT', () => {
  hunter.stopHunt();
  process.exit(0);
});

export default HoneyHunter;
