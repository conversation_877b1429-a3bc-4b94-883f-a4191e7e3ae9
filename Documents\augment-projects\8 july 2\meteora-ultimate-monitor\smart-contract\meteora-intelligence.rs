// 🧠 METEORA INTELLIGENCE SMART CONTRACT
// The ultimate on-chain DLMM pool ranking system

use anchor_lang::prelude::*;
use anchor_spl::token::{Token, TokenAccount};
use std::collections::BTreeMap;

declare_id!("MeteoraIntelligenceSystemProgramId111111111111");

#[program]
pub mod meteora_intelligence {
    use super::*;

    // 🚀 INITIALIZE THE INTELLIGENCE SYSTEM
    pub fn initialize(ctx: Context<Initialize>) -> Result<()> {
        let intelligence_state = &mut ctx.accounts.intelligence_state;
        intelligence_state.authority = ctx.accounts.authority.key();
        intelligence_state.total_pools_tracked = 0;
        intelligence_state.last_update = Clock::get()?.unix_timestamp;
        intelligence_state.top_pools = Vec::new();
        
        msg!("🧠 Meteora Intelligence System Initialized!");
        Ok(())
    }

    // 📊 UPDATE POOL RANKINGS (Called by crank/keeper)
    pub fn update_rankings(
        ctx: Context<UpdateRankings>,
        pool_data: Vec<PoolMetrics>
    ) -> Result<()> {
        let intelligence_state = &mut ctx.accounts.intelligence_state;
        
        // Calculate profitability scores for each pool
        let mut ranked_pools: Vec<RankedPool> = pool_data
            .into_iter()
            .map(|pool| {
                let profit_per_dollar = calculate_profit_per_dollar(&pool);
                let honey_score = calculate_honey_score(&pool);
                let risk_level = assess_risk_level(&pool);
                
                RankedPool {
                    pool_address: pool.pool_address,
                    token_x: pool.token_x,
                    token_y: pool.token_y,
                    tvl: pool.tvl,
                    volume_24h: pool.volume_24h,
                    fees_24h: pool.fees_24h,
                    profit_per_dollar,
                    honey_score,
                    risk_level,
                    last_updated: Clock::get()?.unix_timestamp,
                }
            })
            .collect();

        // Sort by profit per dollar (highest first)
        ranked_pools.sort_by(|a, b| {
            b.profit_per_dollar.partial_cmp(&a.profit_per_dollar).unwrap()
        });

        // Keep only top 20
        ranked_pools.truncate(20);

        // Update state
        intelligence_state.top_pools = ranked_pools;
        intelligence_state.total_pools_tracked = pool_data.len() as u32;
        intelligence_state.last_update = Clock::get()?.unix_timestamp;

        msg!("📊 Rankings updated! Top pool: {} with {:.4} profit per $1", 
             intelligence_state.top_pools[0].pool_address,
             intelligence_state.top_pools[0].profit_per_dollar);

        Ok(())
    }

    // 🔍 GET TOP POOLS (Public query function)
    pub fn get_top_pools(ctx: Context<GetTopPools>) -> Result<Vec<RankedPool>> {
        let intelligence_state = &ctx.accounts.intelligence_state;
        Ok(intelligence_state.top_pools.clone())
    }

    // 🎯 GET SPECIFIC POOL RANKING
    pub fn get_pool_ranking(
        ctx: Context<GetPoolRanking>,
        pool_address: Pubkey
    ) -> Result<Option<RankedPool>> {
        let intelligence_state = &ctx.accounts.intelligence_state;
        
        let pool = intelligence_state.top_pools
            .iter()
            .find(|p| p.pool_address == pool_address)
            .cloned();
            
        Ok(pool)
    }

    // 🏆 GET HONEY LEADERBOARD
    pub fn get_honey_leaderboard(ctx: Context<GetHoneyLeaderboard>) -> Result<Vec<RankedPool>> {
        let intelligence_state = &ctx.accounts.intelligence_state;
        
        let mut honey_sorted = intelligence_state.top_pools.clone();
        honey_sorted.sort_by(|a, b| {
            b.honey_score.partial_cmp(&a.honey_score).unwrap()
        });
        
        Ok(honey_sorted)
    }
}

// 💰 CALCULATE PROFIT PER DOLLAR
fn calculate_profit_per_dollar(pool: &PoolMetrics) -> f64 {
    if pool.tvl == 0.0 {
        return 0.0;
    }
    
    // Daily fees / TVL = daily return rate
    let daily_return = pool.fees_24h / pool.tvl;
    
    // Annualized return (365 days)
    let annual_return = daily_return * 365.0;
    
    // Adjust for volume velocity (higher velocity = higher fees)
    let volume_velocity = pool.volume_24h / pool.tvl;
    let velocity_multiplier = (volume_velocity / 10.0).min(2.0).max(0.1);
    
    annual_return * velocity_multiplier
}

// 🍯 CALCULATE HONEY SCORE
fn calculate_honey_score(pool: &PoolMetrics) -> f64 {
    let fee_concentration = if pool.tvl > 0.0 { 
        (pool.fees_24h / pool.tvl) * 100.0 
    } else { 
        0.0 
    };
    
    let volume_efficiency = if pool.volume_24h > 0.0 { 
        pool.fees_24h / pool.volume_24h * 10000.0 
    } else { 
        0.0 
    };
    
    let liquidity_utilization = if pool.tvl > 0.0 { 
        pool.volume_24h / pool.tvl 
    } else { 
        0.0 
    };
    
    // Weighted score
    (fee_concentration * 0.4 + volume_efficiency * 0.3 + liquidity_utilization * 0.3)
        .min(100.0)
        .max(0.0)
}

// ⚠️ ASSESS RISK LEVEL
fn assess_risk_level(pool: &PoolMetrics) -> RiskLevel {
    let volume_to_tvl = if pool.tvl > 0.0 { 
        pool.volume_24h / pool.tvl 
    } else { 
        0.0 
    };
    
    if volume_to_tvl > 10.0 {
        RiskLevel::High
    } else if volume_to_tvl > 3.0 {
        RiskLevel::Medium
    } else {
        RiskLevel::Low
    }
}

// 📊 DATA STRUCTURES
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PoolMetrics {
    pub pool_address: Pubkey,
    pub token_x: Pubkey,
    pub token_y: Pubkey,
    pub tvl: f64,
    pub volume_24h: f64,
    pub fees_24h: f64,
    pub bin_step: u16,
    pub active_bin: i32,
}

#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RankedPool {
    pub pool_address: Pubkey,
    pub token_x: Pubkey,
    pub token_y: Pubkey,
    pub tvl: f64,
    pub volume_24h: f64,
    pub fees_24h: f64,
    pub profit_per_dollar: f64,  // 🎯 THE MAGIC METRIC
    pub honey_score: f64,
    pub risk_level: RiskLevel,
    pub last_updated: i64,
}

#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
}

#[account]
pub struct IntelligenceState {
    pub authority: Pubkey,
    pub total_pools_tracked: u32,
    pub last_update: i64,
    pub top_pools: Vec<RankedPool>,  // Top 20 pools by profit per dollar
}

// 🔧 INSTRUCTION CONTEXTS
#[derive(Accounts)]
pub struct Initialize<'info> {
    #[account(
        init,
        payer = authority,
        space = 8 + 32 + 4 + 8 + 4 + (20 * 200), // Rough estimate for top 20 pools
        seeds = [b"intelligence"],
        bump
    )]
    pub intelligence_state: Account<'info, IntelligenceState>,
    
    #[account(mut)]
    pub authority: Signer<'info>,
    
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct UpdateRankings<'info> {
    #[account(
        mut,
        seeds = [b"intelligence"],
        bump,
        has_one = authority
    )]
    pub intelligence_state: Account<'info, IntelligenceState>,
    
    pub authority: Signer<'info>,
}

#[derive(Accounts)]
pub struct GetTopPools<'info> {
    #[account(
        seeds = [b"intelligence"],
        bump
    )]
    pub intelligence_state: Account<'info, IntelligenceState>,
}

#[derive(Accounts)]
pub struct GetPoolRanking<'info> {
    #[account(
        seeds = [b"intelligence"],
        bump
    )]
    pub intelligence_state: Account<'info, IntelligenceState>,
}

#[derive(Accounts)]
pub struct GetHoneyLeaderboard<'info> {
    #[account(
        seeds = [b"intelligence"],
        bump
    )]
    pub intelligence_state: Account<'info, IntelligenceState>,
}

// 🚨 CUSTOM ERRORS
#[error_code]
pub enum IntelligenceError {
    #[msg("Unauthorized access")]
    Unauthorized,
    #[msg("Invalid pool data")]
    InvalidPoolData,
    #[msg("Calculation overflow")]
    CalculationOverflow,
}
