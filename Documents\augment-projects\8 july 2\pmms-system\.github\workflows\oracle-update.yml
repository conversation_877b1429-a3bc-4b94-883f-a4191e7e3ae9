# Original content you provided:
name: Oracle Updater

on:
  schedule:
    - cron: '*/15 * * * *'  # Every 15 minutes (UTC)
  workflow_dispatch:      # Allow manual runs

jobs:
  update-oracle:
    runs-on: ubuntu-latest
    environment: Oracle Update
    steps:
      - name: 📥 Checkout repository
        uses: actions/checkout@v3
        with:
          ref: main

      - name: 🐍 Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: 📦 Install dependencies
        run: |
          cd contracts/ZiGEcocash
          ls -al
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: 🛠 Run Oracle Update Script
        env:
          POLYGON_ZKEVM_RPC_URL: ${{ secrets.POLYGON_ZKEVM_RPC_URL }}
          PRIVATE_KEY: ${{ secrets.PRIVATE_KEY }}
        run: |
          cd contracts/ZiGEcocash
          ls -al
          python update_oracle.py
