{"proxyAddress": "0x4732c9Fa54937d4F8bBAcD7b2f415992a41C7938", "implementationAddress": "0x144b03f82D0585A88af057649DcD39A0a5dbbD6f", "network": "skale_testnet", "abi": "[{\"type\":\"constructor\",\"stateMutability\":\"undefined\",\"payable\":false,\"inputs\":[]},{\"type\":\"error\",\"name\":\"AddressEmptyCode\",\"inputs\":[{\"type\":\"address\",\"name\":\"target\"}]},{\"type\":\"error\",\"name\":\"ERC1967InvalidImplementation\",\"inputs\":[{\"type\":\"address\",\"name\":\"implementation\"}]},{\"type\":\"error\",\"name\":\"ERC1967NonPayable\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"FailedCall\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"InvalidInitialization\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"NotInitializing\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"OwnableInvalidOwner\",\"inputs\":[{\"type\":\"address\",\"name\":\"owner\"}]},{\"type\":\"error\",\"name\":\"OwnableUnauthorizedAccount\",\"inputs\":[{\"type\":\"address\",\"name\":\"account\"}]},{\"type\":\"error\",\"name\":\"UUPSUnauthorizedCallContext\",\"inputs\":[]},{\"type\":\"error\",\"name\":\"UUPSUnsupportedProxiableUUID\",\"inputs\":[{\"type\":\"bytes32\",\"name\":\"slot\"}]},{\"type\":\"event\",\"anonymous\":false,\"name\":\"Initialized\",\"inputs\":[{\"type\":\"uint64\",\"name\":\"version\",\"indexed\":false}]},{\"type\":\"event\",\"anonymous\":false,\"name\":\"OwnershipTransferred\",\"inputs\":[{\"type\":\"address\",\"name\":\"previousOwner\",\"indexed\":true},{\"type\":\"address\",\"name\":\"newOwner\",\"indexed\":true}]},{\"type\":\"event\",\"anonymous\":false,\"name\":\"Upgraded\",\"inputs\":[{\"type\":\"address\",\"name\":\"implementation\",\"indexed\":true}]},{\"type\":\"event\",\"anonymous\":false,\"name\":\"WalletBlocked\",\"inputs\":[{\"type\":\"address\",\"name\":\"wallet\",\"indexed\":true},{\"type\":\"bool\",\"name\":\"isBlocked\",\"indexed\":false}]},{\"type\":\"event\",\"anonymous\":false,\"name\":\"WalletChecked\",\"inputs\":[{\"type\":\"address\",\"name\":\"wallet\",\"indexed\":true},{\"type\":\"bool\",\"name\":\"isBlocked\",\"indexed\":false},{\"type\":\"bool\",\"name\":\"canInteract\",\"indexed\":false}]},{\"type\":\"function\",\"name\":\"UPGRADE_INTERFACE_VERSION\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"string\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"checkWalletStatus\",\"constant\":false,\"payable\":false,\"inputs\":[{\"type\":\"address\",\"name\":\"_wallet\"}],\"outputs\":[{\"type\":\"bool\",\"name\":\"isBlocked\"},{\"type\":\"bool\",\"name\":\"canInteract\"}]},{\"type\":\"function\",\"name\":\"initialize\",\"constant\":false,\"payable\":false,\"inputs\":[{\"type\":\"address\",\"name\":\"_owner\"}],\"outputs\":[]},{\"type\":\"function\",\"name\":\"isWalletBlocked\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[{\"type\":\"address\",\"name\":\"_wallet\"}],\"outputs\":[{\"type\":\"bool\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"owner\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"address\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"proxiableUUID\",\"constant\":true,\"stateMutability\":\"view\",\"payable\":false,\"inputs\":[],\"outputs\":[{\"type\":\"bytes32\",\"name\":\"\"}]},{\"type\":\"function\",\"name\":\"renounceOwnership\",\"constant\":false,\"payable\":false,\"inputs\":[],\"outputs\":[]},{\"type\":\"function\",\"name\":\"setWalletBlockStatus\",\"constant\":false,\"payable\":false,\"inputs\":[{\"type\":\"address\",\"name\":\"_wallet\"},{\"type\":\"bool\",\"name\":\"_isBlocked\"}],\"outputs\":[]},{\"type\":\"function\",\"name\":\"transferOwnership\",\"constant\":false,\"payable\":false,\"inputs\":[{\"type\":\"address\",\"name\":\"newOwner\"}],\"outputs\":[]},{\"type\":\"function\",\"name\":\"upgradeToAndCall\",\"constant\":false,\"stateMutability\":\"payable\",\"payable\":true,\"inputs\":[{\"type\":\"address\",\"name\":\"newImplementation\"},{\"type\":\"bytes\",\"name\":\"data\"}],\"outputs\":[]}]"}