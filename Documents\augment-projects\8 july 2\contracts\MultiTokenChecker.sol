// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * 🔍 MULTI-TOKEN PROFITABILITY CHECKER
 * Check arbitrage opportunities across multiple token pairs
 */

interface IERC20 {
    function balanceOf(address account) external view returns (uint256);
}

interface IUniswapV2Router {
    function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts);
}

contract MultiTokenChecker {
    
    // Tokens
    IERC20 public constant USDC = IERC20(******************************************);
    IERC20 public constant WETH = IERC20(******************************************);
    IERC20 public constant WMATIC = IERC20(******************************************);
    IERC20 public constant WBTC = IERC20(******************************************);
    IERC20 public constant DAI = IERC20(******************************************);
    IERC20 public constant USDT = IERC20(******************************************);
    
    // DEX Routers
    IUniswapV2Router public constant QUICKSWAP = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant SUSHISWAP = IUniswapV2Router(******************************************);
    
    uint256 public constant TEST_AMOUNT = 1000e6; // $1K USDC
    
    event TokenPairCheck(
        string tokenPair,
        string dexPair,
        uint256 amountIn,
        uint256 amountOut,
        int256 profit,
        bool profitable
    );
    
    event BestOpportunity(
        string tokenPair,
        string strategy,
        int256 profit,
        uint256 profitPercent
    );
    
    /**
     * 🔍 CHECK ALL TOKEN PAIR OPPORTUNITIES
     */
    function checkAllTokenPairs() external returns (bool anyProfitable) {
        uint256 amount = TEST_AMOUNT;
        bool foundProfitable = false;
        int256 bestProfit = -1000000; // Start with very negative
        string memory bestStrategy = "";
        
        // USDC-based pairs
        foundProfitable = checkTokenPair("USDC-WETH", address(USDC), address(WETH), amount, 6, 18) || foundProfitable;
        foundProfitable = checkTokenPair("USDC-WMATIC", address(USDC), address(WMATIC), amount, 6, 18) || foundProfitable;
        foundProfitable = checkTokenPair("USDC-WBTC", address(USDC), address(WBTC), amount, 6, 8) || foundProfitable;
        foundProfitable = checkTokenPair("USDC-DAI", address(USDC), address(DAI), amount, 6, 18) || foundProfitable;
        foundProfitable = checkTokenPair("USDC-USDT", address(USDC), address(USDT), amount, 6, 6) || foundProfitable;
        
        // WETH-based pairs
        uint256 wethAmount = 0.5e18; // 0.5 WETH
        foundProfitable = checkTokenPair("WETH-WMATIC", address(WETH), address(WMATIC), wethAmount, 18, 18) || foundProfitable;
        foundProfitable = checkTokenPair("WETH-WBTC", address(WETH), address(WBTC), wethAmount, 18, 8) || foundProfitable;
        
        // WMATIC-based pairs
        uint256 maticAmount = 1000e18; // 1000 MATIC
        foundProfitable = checkTokenPair("WMATIC-WBTC", address(WMATIC), address(WBTC), maticAmount, 18, 8) || foundProfitable;
        
        return foundProfitable;
    }
    
    /**
     * 🔄 CHECK SPECIFIC TOKEN PAIR
     */
    function checkTokenPair(
        string memory pairName,
        address tokenA,
        address tokenB,
        uint256 amount,
        uint8 decimalsA,
        uint8 decimalsB
    ) public returns (bool profitable) {
        
        bool anyProfitable = false;
        
        // QuickSwap round-trip
        int256 quickProfit = checkRoundTrip(pairName, "QuickSwap", QUICKSWAP, tokenA, tokenB, amount, decimalsA, decimalsB);
        if (quickProfit > 0) anyProfitable = true;
        
        // SushiSwap round-trip
        int256 sushiProfit = checkRoundTrip(pairName, "SushiSwap", SUSHISWAP, tokenA, tokenB, amount, decimalsA, decimalsB);
        if (sushiProfit > 0) anyProfitable = true;
        
        // Cross-DEX arbitrage
        int256 crossProfit1 = checkCrossArbitrage(pairName, "Quick-to-Sushi", QUICKSWAP, SUSHISWAP, tokenA, tokenB, amount, decimalsA, decimalsB);
        if (crossProfit1 > 0) anyProfitable = true;
        
        int256 crossProfit2 = checkCrossArbitrage(pairName, "Sushi-to-Quick", SUSHISWAP, QUICKSWAP, tokenA, tokenB, amount, decimalsA, decimalsB);
        if (crossProfit2 > 0) anyProfitable = true;
        
        // Find best for this pair
        int256 bestProfit = quickProfit;
        string memory bestStrategy = "QuickSwap";
        
        if (sushiProfit > bestProfit) {
            bestProfit = sushiProfit;
            bestStrategy = "SushiSwap";
        }
        if (crossProfit1 > bestProfit) {
            bestProfit = crossProfit1;
            bestStrategy = "Quick-to-Sushi";
        }
        if (crossProfit2 > bestProfit) {
            bestProfit = crossProfit2;
            bestStrategy = "Sushi-to-Quick";
        }
        
        if (bestProfit > 0) {
            uint256 profitPercent = uint256(bestProfit * 10000 / int256(amount));
            emit BestOpportunity(pairName, bestStrategy, bestProfit, profitPercent);
        }
        
        return anyProfitable;
    }
    
    /**
     * 🔄 CHECK ROUND-TRIP ARBITRAGE
     */
    function checkRoundTrip(
        string memory pairName,
        string memory dexName,
        IUniswapV2Router router,
        address tokenA,
        address tokenB,
        uint256 amount,
        uint8 decimalsA,
        uint8 decimalsB
    ) internal returns (int256 profit) {
        
        try this.executeRoundTripCheck(router, tokenA, tokenB, amount) returns (uint256 finalAmount) {
            profit = int256(finalAmount) - int256(amount);
            
            string memory strategy = string(abi.encodePacked(pairName, "-", dexName));
            emit TokenPairCheck(pairName, strategy, amount, finalAmount, profit, profit > 0);
            
            return profit;
        } catch {
            // Return large negative if pair doesn't exist
            return -1000000;
        }
    }
    
    /**
     * 🔀 CHECK CROSS-DEX ARBITRAGE
     */
    function checkCrossArbitrage(
        string memory pairName,
        string memory strategyName,
        IUniswapV2Router router1,
        IUniswapV2Router router2,
        address tokenA,
        address tokenB,
        uint256 amount,
        uint8 decimalsA,
        uint8 decimalsB
    ) internal returns (int256 profit) {
        
        try this.executeCrossCheck(router1, router2, tokenA, tokenB, amount) returns (uint256 finalAmount) {
            profit = int256(finalAmount) - int256(amount);
            
            string memory strategy = string(abi.encodePacked(pairName, "-", strategyName));
            emit TokenPairCheck(pairName, strategy, amount, finalAmount, profit, profit > 0);
            
            return profit;
        } catch {
            // Return large negative if pair doesn't exist
            return -1000000;
        }
    }
    
    /**
     * 🔄 EXECUTE ROUND-TRIP CHECK (EXTERNAL FOR TRY-CATCH)
     */
    function executeRoundTripCheck(
        IUniswapV2Router router,
        address tokenA,
        address tokenB,
        uint256 amount
    ) external view returns (uint256 finalAmount) {
        require(msg.sender == address(this), "Internal only");
        
        // Step 1: tokenA → tokenB
        address[] memory path1 = new address[](2);
        path1[0] = tokenA;
        path1[1] = tokenB;
        
        uint256[] memory amounts1 = router.getAmountsOut(amount, path1);
        uint256 tokenBAmount = amounts1[1];
        
        // Step 2: tokenB → tokenA
        address[] memory path2 = new address[](2);
        path2[0] = tokenB;
        path2[1] = tokenA;
        
        uint256[] memory amounts2 = router.getAmountsOut(tokenBAmount, path2);
        finalAmount = amounts2[1];
        
        return finalAmount;
    }
    
    /**
     * 🔀 EXECUTE CROSS-DEX CHECK (EXTERNAL FOR TRY-CATCH)
     */
    function executeCrossCheck(
        IUniswapV2Router router1,
        IUniswapV2Router router2,
        address tokenA,
        address tokenB,
        uint256 amount
    ) external view returns (uint256 finalAmount) {
        require(msg.sender == address(this), "Internal only");
        
        // Step 1: tokenA → tokenB on router1
        address[] memory path1 = new address[](2);
        path1[0] = tokenA;
        path1[1] = tokenB;
        
        uint256[] memory amounts1 = router1.getAmountsOut(amount, path1);
        uint256 tokenBAmount = amounts1[1];
        
        // Step 2: tokenB → tokenA on router2
        address[] memory path2 = new address[](2);
        path2[0] = tokenB;
        path2[1] = tokenA;
        
        uint256[] memory amounts2 = router2.getAmountsOut(tokenBAmount, path2);
        finalAmount = amounts2[1];
        
        return finalAmount;
    }
    
    /**
     * 📊 GET QUICK OVERVIEW
     */
    function getQuickOverview() external view returns (
        uint256 usdcWethQuick,
        uint256 usdcWethSushi,
        uint256 usdcMaticQuick,
        uint256 usdcMaticSushi
    ) {
        uint256 amount = 1000e6; // $1K USDC
        
        // USDC-WETH prices
        address[] memory path1 = new address[](2);
        path1[0] = address(USDC);
        path1[1] = address(WETH);
        
        try QUICKSWAP.getAmountsOut(amount, path1) returns (uint256[] memory amounts1) {
            path1[0] = address(WETH);
            path1[1] = address(USDC);
            try QUICKSWAP.getAmountsOut(amounts1[1], path1) returns (uint256[] memory amounts2) {
                usdcWethQuick = amounts2[1];
            } catch {}
        } catch {}
        
        try SUSHISWAP.getAmountsOut(amount, path1) returns (uint256[] memory amounts3) {
            path1[0] = address(WETH);
            path1[1] = address(USDC);
            try SUSHISWAP.getAmountsOut(amounts3[1], path1) returns (uint256[] memory amounts4) {
                usdcWethSushi = amounts4[1];
            } catch {}
        } catch {}
        
        // USDC-MATIC prices
        path1[0] = address(USDC);
        path1[1] = address(WMATIC);
        
        try QUICKSWAP.getAmountsOut(amount, path1) returns (uint256[] memory amounts5) {
            path1[0] = address(WMATIC);
            path1[1] = address(USDC);
            try QUICKSWAP.getAmountsOut(amounts5[1], path1) returns (uint256[] memory amounts6) {
                usdcMaticQuick = amounts6[1];
            } catch {}
        } catch {}
        
        try SUSHISWAP.getAmountsOut(amount, path1) returns (uint256[] memory amounts7) {
            path1[0] = address(WMATIC);
            path1[1] = address(USDC);
            try SUSHISWAP.getAmountsOut(amounts7[1], path1) returns (uint256[] memory amounts8) {
                usdcMaticSushi = amounts8[1];
            } catch {}
        } catch {}
        
        return (usdcWethQuick, usdcWethSushi, usdcMaticQuick, usdcMaticSushi);
    }
}
