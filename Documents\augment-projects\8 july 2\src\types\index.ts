import { BigNumberish } from 'ethers';

// Network and Chain Types
export enum ChainId {
  ETHEREUM = 1,
  OPTIMISM = 10,
  POLYGON = 137,
  ARBITRUM = 42161,
  BASE = 8453,
  SEPOLIA = 11155111,
  OPTIMISM_SEPOLIA = 11155420,
  ARBITRUM_SEPOLIA = 421614,
  BASE_SEPOLIA = 84532,
  POLYGON_MUMBAI = 80001
}

export interface NetworkConfig {
  chainId: ChainId;
  name: string;
  rpcUrl: string;
  blockTime: number; // in seconds
  gasLimit: number;
  isTestnet: boolean;
}

// Transaction Types
export interface FlashLoanTransaction {
  hash: string;
  blockNumber: number;
  timestamp: number;
  from: string;
  to: string;
  value: bigint;
  gasPrice: bigint;
  gasUsed: bigint;
  status: number;
  chainId: ChainId;
  flashLoanProvider: FlashLoanProvider;
  borrowedAssets: BorrowedAsset[];
  strategy: TransactionStrategy;
  profitUSD: number;
  gasFeesUSD: number;
  netProfitUSD: number;
}

export interface BorrowedAsset {
  token: string;
  amount: bigint;
  symbol: string;
  decimals: number;
  priceUSD: number;
}

export enum FlashLoanProvider {
  AAVE = 'AAVE',
  BALANCER = 'BALANCER',
  DYDX = 'DYDX',
  UNISWAP_V3 = 'UNISWAP_V3',
  MAKER = 'MAKER'
}

// Strategy Types
export enum StrategyType {
  ARBITRAGE = 'ARBITRAGE',
  LIQUIDATION = 'LIQUIDATION',
  FLASH_LOAN = 'FLASH_LOAN',
  MEV = 'MEV',
  UNKNOWN = 'UNKNOWN'
}

export interface TransactionStrategy {
  type: StrategyType;
  confidence: number;
  description: string;
  estimatedCapital: number;
  riskLevel: RiskLevel;
}

export interface SwapStep {
  protocol: string;
  tokenIn: string;
  tokenOut: string;
  amountIn: bigint;
  amountOut: bigint;
  poolAddress?: string;
  fee?: number;
}

export enum RiskLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// DEX and Protocol Types
export enum DEXProtocol {
  UNISWAP_V2 = 'UNISWAP_V2',
  UNISWAP_V3 = 'UNISWAP_V3',
  SUSHISWAP = 'SUSHISWAP',
  BALANCER = 'BALANCER',
  CURVE = 'CURVE',
  PANCAKESWAP = 'PANCAKESWAP',
  QUICKSWAP = 'QUICKSWAP'
}

export interface PoolInfo {
  address: string;
  protocol: DEXProtocol;
  token0: string;
  token1: string;
  fee: number;
  liquidity: bigint;
  reserve0: bigint;
  reserve1: bigint;
  price: number;
  chainId: ChainId;
}

// Monitoring and Analysis Types
export interface MonitoringConfig {
  chains: ChainId[];
  minProfitThreshold: number;
  maxGasPrice: number;
  blockConfirmations: number;
  mempoolMonitoring: boolean;
  protocols: DEXProtocol[];
}

export interface AnalysisResult {
  transactionHash: string;
  isFlashLoan: boolean;
  confidence: number; // 0-1 scale
  strategy: TransactionStrategy;
  profitability: ProfitabilityAnalysis;
  replicationFeasibility: FeasibilityAnalysis;
  timestamp: number;
}

export interface ProfitabilityAnalysis {
  grossProfitUSD: number;
  gasFeesUSD: number;
  netProfitUSD: number;
  roi: number; // Return on investment percentage
  profitMargin: number;
}

export interface FeasibilityAnalysis {
  canReplicate: boolean;
  reasons: string[];
  liquidityAvailable: boolean;
  gasEstimate: bigint;
  slippageImpact: number;
  competitionLevel: number; // 1-10 scale
  timeWindow: number; // seconds
}

// Database Types
export interface DatabaseTransaction {
  id: string;
  hash: string;
  block_number: number;
  timestamp: Date;
  chain_id: number;
  from_address: string;
  to_address: string;
  value: string;
  gas_price: string;
  gas_used: string;
  status: number;
  flash_loan_provider: string;
  strategy_type: string;
  profit_usd: number;
  gas_fees_usd: number;
  net_profit_usd: number;
  analysis_data: any;
  created_at: Date;
  updated_at: Date;
}

// API Response Types
export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Configuration Types
export interface AppConfig {
  database: {
    url: string;
    maxConnections: number;
  };
  redis: {
    url: string;
  };
  monitoring: MonitoringConfig;
  riskManagement: {
    maxPositionSize: number;
    riskTolerance: RiskLevel;
    stopLossPercentage: number;
  };
  notifications: {
    discord?: string;
    telegram?: {
      botToken: string;
      chatId: string;
    };
  };
}

// Error Types
export class FlashLoanError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'FlashLoanError';
  }
}

export enum ErrorCode {
  NETWORK_ERROR = 'NETWORK_ERROR',
  INSUFFICIENT_LIQUIDITY = 'INSUFFICIENT_LIQUIDITY',
  HIGH_SLIPPAGE = 'HIGH_SLIPPAGE',
  GAS_ESTIMATION_FAILED = 'GAS_ESTIMATION_FAILED',
  TRANSACTION_FAILED = 'TRANSACTION_FAILED',
  ANALYSIS_FAILED = 'ANALYSIS_FAILED',
  DATABASE_ERROR = 'DATABASE_ERROR'
}

export interface FlashLoanProviderInfo {
  name: string;
  chainId: ChainId;
  feeRate: number;
  maxLoanAmount: string;
  address: string;
}

export interface SmartContractCapability {
  name: string;
  chainId: ChainId;
  address: string;
  capabilities: string[];
}

export interface AnalyzedTransaction {
  hash: string;
  blockNumber: number;
  from: string;
  to: string;
  value: string;
  gasUsed: number;
  gasPrice: string;
  status: number;
  flashLoanDetails?: any;
  arbitrageDetails?: any;
  liquidationDetails?: any;
  mevDetails?: any;
  profitAnalysis?: any;
  timestamp: number;
}


