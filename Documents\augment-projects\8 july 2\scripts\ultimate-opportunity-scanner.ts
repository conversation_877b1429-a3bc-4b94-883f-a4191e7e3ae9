import { ethers } from 'hardhat';
import axios from 'axios';

// ULTIMATE MULTI-DEX OPPORTUNITY SCANNER - REAL PROFITS ONLY
const DEX_ADDRESSES = {
  QUICKSWAP_V2: '******************************************',
  QUICKSWAP_V3: '******************************************',
  SUSHISWAP: '******************************************',
  UNISWAP_V3: '******************************************',
  UNISWAP_V3_QUOTER: '******************************************'
};

const TOKENS = {
  WETH: { address: '******************************************', decimals: 18, symbol: 'WETH', priceUSD: 3000 },
  WMATIC: { address: '******************************************', decimals: 18, symbol: 'WMATIC', priceUSD: 0.5 },
  USDC: { address: '******************************************', decimals: 6, symbol: 'USDC', priceUSD: 1 },
  USDT: { address: '******************************************', decimals: 6, symbol: 'USDT', priceUSD: 1 },
  DAI: { address: '******************************************', decimals: 18, symbol: 'DAI', priceUSD: 1 }
};

interface ArbitrageOpportunity {
  tokenIn: string;
  tokenOut: string;
  tokenInSymbol: string;
  tokenOutSymbol: string;
  amountIn: ethers.BigNumber;
  amountInFormatted: string;
  path: string[];
  dexes: string[];
  estimatedProfit: ethers.BigNumber;
  profitUSD: number;
  profitPercent: number;
  gasEstimate: number;
  netProfitUSD: number;
  executable: boolean;
  priority: number;
}

class UltimateOpportunityScanner {
  private provider: ethers.providers.Provider;
  private routers: { [key: string]: ethers.Contract } = {};
  private quoter: ethers.Contract;
  
  constructor(provider: ethers.providers.Provider) {
    this.provider = provider;
    this.initializeContracts();
  }
  
  private initializeContracts() {
    const routerABI = [
      'function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)'
    ];
    
    const quoterABI = [
      'function quoteExactInputSingle(address tokenIn, address tokenOut, uint24 fee, uint256 amountIn, uint160 sqrtPriceLimitX96) external returns (uint256 amountOut)'
    ];
    
    this.routers.QUICKSWAP_V2 = new ethers.Contract(DEX_ADDRESSES.QUICKSWAP_V2, routerABI, this.provider);
    this.routers.SUSHISWAP = new ethers.Contract(DEX_ADDRESSES.SUSHISWAP, routerABI, this.provider);
    this.quoter = new ethers.Contract(DEX_ADDRESSES.UNISWAP_V3_QUOTER, quoterABI, this.provider);
  }
  
  /**
   * Get quote from V2 DEX
   */
  private async getV2Quote(dexName: string, tokenIn: string, tokenOut: string, amountIn: ethers.BigNumber): Promise<ethers.BigNumber> {
    try {
      const path = [tokenIn, tokenOut];
      const amounts = await this.routers[dexName].getAmountsOut(amountIn, path);
      return amounts[1].mul(995).div(1000); // 0.5% slippage
    } catch {
      return ethers.BigNumber.from(0);
    }
  }
  
  /**
   * Get quote from V3 DEX
   */
  private async getV3Quote(tokenIn: string, tokenOut: string, amountIn: ethers.BigNumber): Promise<ethers.BigNumber> {
    try {
      const quote = await this.quoter.callStatic.quoteExactInputSingle(tokenIn, tokenOut, 3000, amountIn, 0);
      return quote.mul(995).div(1000); // 0.5% slippage
    } catch {
      return ethers.BigNumber.from(0);
    }
  }
  
  /**
   * Scan 2-way arbitrage opportunities
   */
  private async scan2WayArbitrage(tokenA: any, tokenB: any, amountIn: ethers.BigNumber): Promise<ArbitrageOpportunity[]> {
    const opportunities: ArbitrageOpportunity[] = [];
    
    const dexPairs = [
      { buy: 'QUICKSWAP_V2', sell: 'SUSHISWAP', buyV3: false, sellV3: false },
      { buy: 'SUSHISWAP', sell: 'QUICKSWAP_V2', buyV3: false, sellV3: false },
      { buy: 'QUICKSWAP_V2', sell: 'UNISWAP_V3', buyV3: false, sellV3: true },
      { buy: 'UNISWAP_V3', sell: 'QUICKSWAP_V2', buyV3: true, sellV3: false },
      { buy: 'SUSHISWAP', sell: 'UNISWAP_V3', buyV3: false, sellV3: true },
      { buy: 'UNISWAP_V3', sell: 'SUSHISWAP', buyV3: true, sellV3: false }
    ];
    
    for (const pair of dexPairs) {
      try {
        // Step 1: tokenA → tokenB on buy DEX
        let tokenBAmount: ethers.BigNumber;
        if (pair.buyV3) {
          tokenBAmount = await this.getV3Quote(tokenA.address, tokenB.address, amountIn);
        } else {
          tokenBAmount = await this.getV2Quote(pair.buy, tokenA.address, tokenB.address, amountIn);
        }
        
        if (tokenBAmount.eq(0)) continue;
        
        // Step 2: tokenB → tokenA on sell DEX
        let finalAmount: ethers.BigNumber;
        if (pair.sellV3) {
          finalAmount = await this.getV3Quote(tokenB.address, tokenA.address, tokenBAmount);
        } else {
          finalAmount = await this.getV2Quote(pair.sell, tokenB.address, tokenA.address, tokenBAmount);
        }
        
        if (finalAmount.lte(amountIn)) continue;
        
        // Calculate profit
        const grossProfit = finalAmount.sub(amountIn);
        const flashLoanFee = amountIn.mul(9).div(10000); // 0.09%
        const gasEstimate = 400000;
        const gasCostUSD = gasEstimate * 50e-9 * 0.5; // 50 gwei * POL price
        
        const profitPercent = grossProfit.mul(10000).div(amountIn).toNumber() / 100;
        const grossProfitUSD = parseFloat(ethers.utils.formatUnits(grossProfit, tokenA.decimals)) * tokenA.priceUSD;
        const flashLoanFeeUSD = parseFloat(ethers.utils.formatUnits(flashLoanFee, tokenA.decimals)) * tokenA.priceUSD;
        const netProfitUSD = grossProfitUSD - flashLoanFeeUSD - gasCostUSD;
        
        if (netProfitUSD > 50) { // $50 minimum
          opportunities.push({
            tokenIn: tokenA.address,
            tokenOut: tokenB.address,
            tokenInSymbol: tokenA.symbol,
            tokenOutSymbol: tokenB.symbol,
            amountIn,
            amountInFormatted: ethers.utils.formatUnits(amountIn, tokenA.decimals),
            path: [tokenA.address, tokenB.address, tokenA.address],
            dexes: [pair.buy, pair.sell],
            estimatedProfit: grossProfit.sub(flashLoanFee),
            profitUSD: grossProfitUSD,
            profitPercent,
            gasEstimate,
            netProfitUSD,
            executable: netProfitUSD > 50,
            priority: Math.floor(netProfitUSD)
          });
        }
      } catch (error) {
        // Silent fail for individual pair
      }
    }
    
    return opportunities;
  }
  
  /**
   * Scan 3-way triangular arbitrage
   */
  private async scan3WayArbitrage(tokenA: any, tokenB: any, tokenC: any, amountIn: ethers.BigNumber): Promise<ArbitrageOpportunity[]> {
    const opportunities: ArbitrageOpportunity[] = [];
    
    try {
      // A → B → C → A
      const amountB = await this.getV2Quote('QUICKSWAP_V2', tokenA.address, tokenB.address, amountIn);
      if (amountB.eq(0)) return opportunities;
      
      const amountC = await this.getV2Quote('SUSHISWAP', tokenB.address, tokenC.address, amountB);
      if (amountC.eq(0)) return opportunities;
      
      const finalAmount = await this.getV3Quote(tokenC.address, tokenA.address, amountC);
      if (finalAmount.lte(amountIn)) return opportunities;
      
      const grossProfit = finalAmount.sub(amountIn);
      const flashLoanFee = amountIn.mul(9).div(10000);
      const gasEstimate = 600000; // Higher for 3-way
      const gasCostUSD = gasEstimate * 50e-9 * 0.5;
      
      const grossProfitUSD = parseFloat(ethers.utils.formatUnits(grossProfit, tokenA.decimals)) * tokenA.priceUSD;
      const flashLoanFeeUSD = parseFloat(ethers.utils.formatUnits(flashLoanFee, tokenA.decimals)) * tokenA.priceUSD;
      const netProfitUSD = grossProfitUSD - flashLoanFeeUSD - gasCostUSD;
      
      if (netProfitUSD > 75) { // Higher threshold for 3-way
        opportunities.push({
          tokenIn: tokenA.address,
          tokenOut: tokenC.address,
          tokenInSymbol: tokenA.symbol,
          tokenOutSymbol: tokenC.symbol,
          amountIn,
          amountInFormatted: ethers.utils.formatUnits(amountIn, tokenA.decimals),
          path: [tokenA.address, tokenB.address, tokenC.address, tokenA.address],
          dexes: ['QUICKSWAP_V2', 'SUSHISWAP', 'UNISWAP_V3'],
          estimatedProfit: grossProfit.sub(flashLoanFee),
          profitUSD: grossProfitUSD,
          profitPercent: grossProfit.mul(10000).div(amountIn).toNumber() / 100,
          gasEstimate,
          netProfitUSD,
          executable: netProfitUSD > 75,
          priority: Math.floor(netProfitUSD * 1.5) // Higher priority for 3-way
        });
      }
    } catch (error) {
      // Silent fail
    }
    
    return opportunities;
  }
  
  /**
   * Comprehensive opportunity scan
   */
  async scanAllOpportunities(): Promise<ArbitrageOpportunity[]> {
    console.log('🔍 Scanning all arbitrage opportunities...');
    
    const allOpportunities: ArbitrageOpportunity[] = [];
    const tokenList = Object.values(TOKENS);
    
    // Test amounts for each token
    const getTestAmounts = (token: any) => {
      switch (token.symbol) {
        case 'WETH':
          return [
            ethers.utils.parseEther('0.5'),
            ethers.utils.parseEther('1'),
            ethers.utils.parseEther('2')
          ];
        case 'WMATIC':
          return [
            ethers.utils.parseEther('5000'),
            ethers.utils.parseEther('10000'),
            ethers.utils.parseEther('20000')
          ];
        case 'USDC':
        case 'USDT':
          return [
            ethers.utils.parseUnits('5000', 6),
            ethers.utils.parseUnits('10000', 6),
            ethers.utils.parseUnits('25000', 6)
          ];
        case 'DAI':
          return [
            ethers.utils.parseEther('5000'),
            ethers.utils.parseEther('10000'),
            ethers.utils.parseEther('25000')
          ];
        default:
          return [ethers.utils.parseEther('1000')];
      }
    };
    
    // 2-way arbitrage scan
    for (let i = 0; i < tokenList.length; i++) {
      for (let j = i + 1; j < tokenList.length; j++) {
        const tokenA = tokenList[i];
        const tokenB = tokenList[j];
        
        const testAmounts = getTestAmounts(tokenA);
        
        for (const amount of testAmounts) {
          const opportunities = await this.scan2WayArbitrage(tokenA, tokenB, amount);
          allOpportunities.push(...opportunities);
        }
      }
    }
    
    // 3-way arbitrage scan (limited to most liquid pairs)
    const liquidTokens = [TOKENS.WETH, TOKENS.WMATIC, TOKENS.USDC];
    for (let i = 0; i < liquidTokens.length; i++) {
      for (let j = 0; j < liquidTokens.length; j++) {
        for (let k = 0; k < liquidTokens.length; k++) {
          if (i !== j && j !== k && i !== k) {
            const testAmounts = getTestAmounts(liquidTokens[i]);
            
            for (const amount of testAmounts) {
              const opportunities = await this.scan3WayArbitrage(liquidTokens[i], liquidTokens[j], liquidTokens[k], amount);
              allOpportunities.push(...opportunities);
            }
          }
        }
      }
    }
    
    // Sort by priority (net profit USD)
    allOpportunities.sort((a, b) => b.priority - a.priority);
    
    return allOpportunities.filter(opp => opp.executable);
  }
}

async function scanUltimateOpportunities() {
  console.log('\n🚀 ULTIMATE MULTI-DEX ARBITRAGE OPPORTUNITY SCANNER');
  console.log('=' .repeat(80));

  const [scanner] = await ethers.getSigners();
  const network = await ethers.provider.getNetwork();
  
  console.log(`Network: ${network.name} (${network.chainId})`);
  console.log(`Scanner: ${scanner.address}`);
  console.log(`Time: ${new Date().toLocaleTimeString()}`);
  
  if (network.chainId !== 137) {
    throw new Error(`Wrong network! Expected 137, got ${network.chainId}`);
  }

  const opportunityScanner = new UltimateOpportunityScanner(ethers.provider);
  
  console.log('\n💰 SCANNING ALL DEX COMBINATIONS...');
  console.log('🔍 2-way arbitrage: QuickSwap, SushiSwap, Uniswap V3');
  console.log('🔍 3-way arbitrage: Triangular opportunities');
  console.log('🔍 Multi-path optimization: Best profit routes');
  
  const startTime = Date.now();
  const opportunities = await opportunityScanner.scanAllOpportunities();
  const scanTime = (Date.now() - startTime) / 1000;
  
  console.log(`\n📊 SCAN COMPLETED IN ${scanTime.toFixed(1)}s`);
  console.log('=' .repeat(80));
  
  if (opportunities.length > 0) {
    console.log(`🎯 FOUND ${opportunities.length} EXECUTABLE OPPORTUNITIES:`);
    
    opportunities.slice(0, 10).forEach((opp, index) => {
      console.log(`\n${index + 1}. ${opp.tokenInSymbol}/${opp.tokenOutSymbol} - ${opp.amountInFormatted} ${opp.tokenInSymbol}`);
      console.log(`   Path: ${opp.dexes.join(' → ')}`);
      console.log(`   Gross Profit: ${opp.profitPercent.toFixed(3)}% (~$${opp.profitUSD.toFixed(2)})`);
      console.log(`   Net Profit: $${opp.netProfitUSD.toFixed(2)} (after fees & gas)`);
      console.log(`   Gas Estimate: ${opp.gasEstimate.toLocaleString()}`);
      console.log(`   Priority: ${opp.priority} ⭐`);
      console.log(`   Status: ${opp.executable ? 'READY FOR EXECUTION ✅' : 'Below threshold ❌'}`);
    });
    
    const bestOpp = opportunities[0];
    console.log(`\n🏆 BEST OPPORTUNITY:`);
    console.log(`   Pair: ${bestOpp.tokenInSymbol}/${bestOpp.tokenOutSymbol}`);
    console.log(`   Amount: ${bestOpp.amountInFormatted} ${bestOpp.tokenInSymbol}`);
    console.log(`   Strategy: ${bestOpp.dexes.join(' → ')}`);
    console.log(`   Net Profit: $${bestOpp.netProfitUSD.toFixed(2)}`);
    console.log(`\n🚀 EXECUTE THIS OPPORTUNITY IMMEDIATELY!`);
    
    return opportunities;
    
  } else {
    console.log(`❌ NO EXECUTABLE OPPORTUNITIES FOUND`);
    console.log(`\n💡 MARKET CONDITIONS:`);
    console.log(`   • All DEXes currently have aligned prices`);
    console.log(`   • Trading fees exceed potential profits`);
    console.log(`   • Wait for market volatility to create opportunities`);
    console.log(`\n🔄 NEXT ACTIONS:`);
    console.log(`   • Continue monitoring every 10-15 seconds`);
    console.log(`   • Watch for major market movements`);
    console.log(`   • Check during high volume trading periods`);
    
    return [];
  }
}

// Execute scan
if (require.main === module) {
  scanUltimateOpportunities()
    .then((opportunities) => {
      console.log('\n🎉 ULTIMATE OPPORTUNITY SCAN COMPLETED!');
      if (opportunities.length > 0) {
        console.log(`Found ${opportunities.length} executable opportunities!`);
        process.exit(0);
      } else {
        console.log('No opportunities found this round.');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 SCAN FAILED:', error);
      process.exit(1);
    });
}

export { scanUltimateOpportunities, UltimateOpportunityScanner };
