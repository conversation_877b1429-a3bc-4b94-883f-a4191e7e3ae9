PS C:\Users\<USER>\Documents\augment-projects\8 july 2> node PRODUCTION-MEV-BOT.js
🚀 PRODUCTION MEV ARBITRAGE BOT
================================
Contract: 0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d
Networks: polygon, arbitrum
Min Profit: $50
MEV Protection: Enabled
================================

🔧 Creating MEV bot instance...
🔧 Starting MEV bot...
[INFO] 2025-07-17T12:35:38.197Z - 🚀 Starting Production MEV Arbitrage Bot {"contract":"0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d","networks":["polygon","arbitrum"],"minProfit":50}
🔧 Setting up provider and wallet...
🔧 Provider created, creating wallet...
🔧 Wallet created: ******************************************
[INFO] 2025-07-17T12:35:38.244Z - ✅ Contract initialized {"contract":"0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d","wallet":"******************************************"}
[INFO] 2025-07-17T12:35:38.247Z - 👁️ Starting mempool monitoring
[INFO] 2025-07-17T12:35:38.250Z - 💹 Starting price aggregation
[INFO] 2025-07-17T12:35:38.251Z - 🔄 Starting opportunity processor
[INFO] 2025-07-17T12:35:38.252Z - 📊 Starting stats reporter 
[INFO] 2025-07-17T12:35:38.253Z - ✅ All systems started successfully
✅ Bot is running. Press Ctrl+C to stop.

@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:35:54.923Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:36:01.002Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
[ERROR] 2025-07-17T12:36:01.003Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
[ERROR] 2025-07-17T12:36:01.142Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:36:07.786Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:36:15.888Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:36:22.262Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:36:23.109Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:36:30.149Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
[ERROR] 2025-07-17T12:36:30.150Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:36:33.722Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[INFO] 2025-07-17T12:36:38.263Z - 📈 Bot Statistics {"runtime":"1.00 minutes","opportunitiesDetected":0,"executionsAttempted":0,"successfulExecutions":0,"successRate":"0%","totalProfit":"$0.0","mempoolTransactions":0,"priceUpdates":29}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:36:42.486Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:36:51.351Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:36:53.821Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:36:57.874Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:37:05.393Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
[ERROR] 2025-07-17T12:37:05.731Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[ERROR] 2025-07-17T12:37:18.493Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
[ERROR] 2025-07-17T12:37:18.741Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
[ERROR] 2025-07-17T12:37:18.742Z - Error detecting opportunities {"error":"could not decode result data (value=\"0x\", info={ \"method\": \"resolver\", \"signature\": \"resolver(bytes32)\" }, code=BAD_DATA, version=6.15.0)"}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 2, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x90e151a3503af87bc017dcb47f255251caf974ea70d8ec873a40c8533d648b1e" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 2,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
@TODO Error: could not coalesce error (error={ "code": -32075, "message": "Method disabled" }, payload={ "id": 3, "jsonrpc": "2.0", "method": "eth_newFilter", "params": [ { "address": [ "0x820a58a91229637305ddbba88f897b8c39134aec7d584e30577c1fa8dad4614d" ], "topics": [ "0x61268d74931c41c9775dffb6169a5208aab1d09cb2eb6aabe8b97162801e5dfa" ] } ] }, code=UNKNOWN_ERROR, version=6.15.0)
    at makeError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\utils\errors.js:137:21)
    at JsonRpcProvider.getRpcError (C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:749:41)
    at C:\Users\<USER>\Documents\augment-projects\8 july 2\node_modules\ethers\lib.commonjs\providers\provider-jsonrpc.js:302:45
    at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
  code: 'UNKNOWN_ERROR',
  error: { code: -32075, message: 'Method disabled' },
  payload: {
    method: 'eth_newFilter',
    params: [ [Object] ],
    id: 3,
    jsonrpc: '2.0'
  },
  shortMessage: 'could not coalesce error'
}
[INFO] 2025-07-17T12:37:28.409Z - Received SIGINT, shutting down gracefully 
[INFO] 2025-07-17T12:37:28.412Z - 🛑 Stopping MEV Arbitrage Bot 
[INFO] 2025-07-17T12:37:28.414Z - 📊 Final Statistics {"runtime":"1.84 minutes","totalOpportunities":0,"totalExecutions":0,"successfulExecutions":0,"totalProfit":"$0.0"}