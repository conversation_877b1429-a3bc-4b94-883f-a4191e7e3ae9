// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.19;

interface IStrategy {
    function execute(bytes memory executionData, uint256 amount, uint256 premium) external returns (bool success, bytes memory result, uint256 profit);
    function checkOpportunity(address asset, uint256 amount) external view returns (bool profitable, uint256 expectedProfit);
    function getStrategyName() external pure returns (string memory);
}
