// 🚀 METEORA API CLIENT - The data collection powerhouse
import axios from 'axios';
import { CONFIG } from './config.js';

class MeteoraAPIClient {
  constructor() {
    this.clients = this.initializeClients();
    this.cache = new Map();
    this.rateLimits = new Map();
    this.debugShown = false;
  }

  initializeClients() {
    return {
      dlmm: axios.create({
        baseURL: CONFIG.APIS.DLMM_MAIN,
        timeout: 10000,
        headers: { 'Content-Type': 'application/json' }
      }),
      dlmmStaging: axios.create({
        baseURL: CONFIG.APIS.DLMM_STAGING,
        timeout: 10000,
        headers: { 'Content-Type': 'application/json' }
      }),
      universalSearch: axios.create({
        baseURL: CONFIG.APIS.UNIVERSAL_SEARCH,
        timeout: 10000,
        headers: { 'Content-Type': 'application/json' }
      }),
      dexscreener: axios.create({
        baseURL: CONFIG.APIS.DEXSCREENER_ADAPTER,
        timeout: 10000,
        headers: { 'Content-Type': 'application/json' }
      }),
      defillama: axios.create({
        baseURL: CONFIG.APIS.DEFILLAMA,
        timeout: 15000,
        headers: { 'Content-Type': 'application/json' }
      })
    };
  }

  // 🎯 GET ALL DLMM POOLS - The main honey source
  async getAllDLMMPools() {
    try {
      console.log('🔍 Fetching ALL DLMM pools...');
      const response = await this.clients.dlmm.get('/pair/all');
      
      if (response.data && Array.isArray(response.data)) {
        console.log(`✅ Found ${response.data.length} DLMM pools`);
        return response.data;
      }
      
      return [];
    } catch (error) {
      console.error('❌ Error fetching DLMM pools:', error.message);
      
      // Try staging API as backup
      try {
        console.log('🔄 Trying staging API...');
        const backupResponse = await this.clients.dlmmStaging.get('/pair/all');
        return backupResponse.data || [];
      } catch (backupError) {
        console.error('❌ Staging API also failed:', backupError.message);
        return [];
      }
    }
  }

  // 🔥 GET POOL DETAILS - Deep dive into specific pools
  async getPoolDetails(poolAddress) {
    try {
      const response = await this.clients.dlmm.get(`/pair/${poolAddress}`);
      return response.data;
    } catch (error) {
      console.error(`❌ Error fetching pool ${poolAddress}:`, error.message);
      return null;
    }
  }

  // 📊 GET POOL STATISTICS - The money metrics
  async getPoolStats(poolAddress) {
    try {
      const response = await this.clients.dlmm.get(`/pair/${poolAddress}/stats`);
      return response.data;
    } catch (error) {
      console.error(`❌ Error fetching stats for ${poolAddress}:`, error.message);
      return null;
    }
  }

  // 🎯 UNIVERSAL SEARCH - Find pools by criteria
  async searchPools(query = 'sol', sortBy = 'tvl:desc,volume_24h:desc') {
    try {
      const params = {
        q: query,
        query_by: 'pool_mint,pool_name,token_mints',
        sort_by: sortBy,
        facet_by: 'pool_type',
        per_page: 250 // Get more results
      };
      
      const response = await this.clients.universalSearch.get('/pool/search', { params });
      return response.data;
    } catch (error) {
      console.error('❌ Error in universal search:', error.message);
      return null;
    }
  }

  // 🚀 GET DEXSCREENER DATA - Real-time trading data
  async getDexScreenerData() {
    try {
      const response = await this.clients.dexscreener.get('/latest-block');
      return response.data;
    } catch (error) {
      console.error('❌ Error fetching DexScreener data:', error.message);
      return null;
    }
  }

  // 💰 GET DEFILLAMA DATA - Cross-reference with DeFiLlama
  async getDefiLlamaData() {
    try {
      const response = await this.clients.defillama.get('/protocols');
      const meteoraData = response.data.find(protocol => 
        protocol.name.toLowerCase().includes('meteora')
      );
      return meteoraData;
    } catch (error) {
      console.error('❌ Error fetching DeFiLlama data:', error.message);
      return null;
    }
  }

  // 🔍 BATCH POOL ANALYSIS - Analyze multiple pools efficiently
  async batchAnalyzePools(poolAddresses) {
    const results = [];
    const batchSize = 10; // Process 10 pools at a time
    
    for (let i = 0; i < poolAddresses.length; i += batchSize) {
      const batch = poolAddresses.slice(i, i + batchSize);
      const batchPromises = batch.map(async (address) => {
        const [details, stats] = await Promise.all([
          this.getPoolDetails(address),
          this.getPoolStats(address)
        ]);
        
        return {
          address,
          details,
          stats,
          timestamp: Date.now()
        };
      });
      
      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults.map(result => 
        result.status === 'fulfilled' ? result.value : null
      ).filter(Boolean));
      
      // Small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    return results;
  }

  // 🎯 CONCENTRATION SCANNER - Find the honey pools
  async scanForConcentration() {
    try {
      console.log('🍯 SCANNING FOR HONEY POOLS...');

      // Try universal search first for faster results
      const searchResults = await this.searchPools('', 'volume_24h:desc,tvl:desc');

      if (searchResults && searchResults.hits && searchResults.hits.length > 0) {
        console.log(`🔍 Found ${searchResults.hits.length} pools from search`);

        // Debug: Show first pool structure (only on first run)
        if (searchResults.hits.length > 0 && !this.debugShown) {
          console.log('🔍 DEBUG: First pool structure:', JSON.stringify(searchResults.hits[0], null, 2));
          console.log('🔍 DEBUG: Available fields:', Object.keys(searchResults.hits[0]));
          this.debugShown = true;
        }

        // Filter pools with minimum criteria - access nested document data
        const filteredPools = searchResults.hits.filter(pool => {
          const doc = pool.document || pool;
          const tvl = parseFloat(doc.tvl || doc.liquidity_usd || doc.total_liquidity || 0);
          const volume24h = parseFloat(doc.volume_24h || doc.volume || doc.daily_volume || 0);
          const fees24h = parseFloat(doc.fee_24h || doc.fees_24h || 0);
          const poolName = doc.pool_name || doc.name || 'Unknown';

          // Only log significant pools to reduce noise
          if (fees24h > 1000 || volume24h > 100000) {
            console.log(`🔍 Pool ${poolName} - TVL: $${tvl.toFixed(2)}, Volume: $${volume24h.toFixed(2)}, Fees: $${fees24h.toFixed(2)}`);
          }

          return tvl >= CONFIG.MONITORING.MIN_TVL &&
                 volume24h >= CONFIG.MONITORING.MIN_VOLUME_24H;
        });

        console.log(`🔍 ${filteredPools.length} pools meet minimum criteria`);
        return filteredPools.slice(0, CONFIG.MONITORING.MAX_POOLS_TO_TRACK);
      }

      // Fallback to direct API call
      const allPools = await this.getAllDLMMPools();
      if (!allPools.length) {
        console.log('❌ No pools found');
        return [];
      }

      // Filter and sort by potential
      const filteredPools = allPools.filter(pool => {
        const tvl = parseFloat(pool.liquidity_usd || pool.tvl || 0);
        const volume24h = parseFloat(pool.volume_24h || 0);

        return tvl >= CONFIG.MONITORING.MIN_TVL &&
               volume24h >= CONFIG.MONITORING.MIN_VOLUME_24H;
      });

      console.log(`🔍 Analyzing ${filteredPools.length} qualifying pools...`);

      // Get detailed analysis for top pools
      const topPools = filteredPools
        .sort((a, b) => parseFloat(b.volume_24h || 0) - parseFloat(a.volume_24h || 0))
        .slice(0, CONFIG.MONITORING.MAX_POOLS_TO_TRACK);

      return topPools;
    } catch (error) {
      console.error('❌ Error in concentration scan:', error.message);
      return [];
    }
  }
}

export default MeteoraAPIClient;
