/**
 * 🔍 TEST BALANCER FLASH LOAN MECHANISM
 * Test minimal flash loan to verify Balancer works
 */

const hre = require("hardhat");

async function main() {
    console.log("🔍 TESTING BALANCER FLASH LOAN MECHANISM...");
    
    const contractAddress = "******************************************";
    const [deployer] = await hre.ethers.getSigners();
    
    console.log("📝 Testing with account:", deployer.address);
    console.log("📍 Contract address:", contractAddress);
    
    // Get contract instance
    const BalancerFlashTest = await hre.ethers.getContractFactory("BalancerFlashTest");
    const contract = BalancerFlashTest.attach(contractAddress);
    
    // Check initial state
    console.log("\n📊 INITIAL STATE:");
    const initialResults = await contract.getLastResults();
    console.log("💰 Last flash amount:", hre.ethers.utils.formatUnits(initialResults[0], 6), "USDC");
    console.log("💸 Last fee amount:", hre.ethers.utils.formatUnits(initialResults[1], 6), "USDC");
    console.log("💳 Last balance:", hre.ethers.utils.formatUnits(initialResults[2], 6), "USDC");
    console.log("✅ Last success:", initialResults[3]);
    console.log("❌ Last error:", initialResults[4]);
    
    const currentBalance = await contract.getCurrentBalance();
    console.log("💳 Current balance:", hre.ethers.utils.formatUnits(currentBalance, 6), "USDC");
    
    // Test different flash loan amounts
    const testAmounts = [
        hre.ethers.utils.parseUnits("100", 6),   // $100 USDC
        hre.ethers.utils.parseUnits("1000", 6),  // $1K USDC
        hre.ethers.utils.parseUnits("10000", 6)  // $10K USDC
    ];
    
    for (let i = 0; i < testAmounts.length; i++) {
        const amount = testAmounts[i];
        const amountStr = hre.ethers.utils.formatUnits(amount, 6);
        
        console.log(`\n🧪 TEST ${i + 1}: FLASH LOAN ${amountStr} USDC`);
        
        try {
            const tx = await contract.testBalancerFlashLoan(amount, {
                gasLimit: 1500000,
                gasPrice: hre.ethers.utils.parseUnits("50", "gwei")
            });
            
            console.log("📝 Transaction hash:", tx.hash);
            console.log("🔗 Polygonscan:", `https://polygonscan.com/tx/${tx.hash}`);
            
            console.log("⏳ Waiting for confirmation...");
            const receipt = await tx.wait();
            
            console.log("✅ TRANSACTION CONFIRMED!");
            console.log("⛽ Gas used:", receipt.gasUsed.toString());
            console.log("💰 Gas cost:", hre.ethers.utils.formatEther(receipt.gasUsed.mul(tx.gasPrice)), "MATIC");
            
            // Parse events
            console.log("\n📊 EVENTS:");
            for (const log of receipt.logs) {
                try {
                    const parsed = contract.interface.parseLog(log);
                    console.log(`🎯 ${parsed.name}:`, parsed.args);
                } catch (e) {
                    // Skip unparseable logs
                }
            }
            
            // Check results
            console.log("\n📊 RESULTS:");
            const results = await contract.getLastResults();
            console.log("💰 Flash amount:", hre.ethers.utils.formatUnits(results[0], 6), "USDC");
            console.log("💸 Fee amount:", hre.ethers.utils.formatUnits(results[1], 6), "USDC");
            console.log("💳 Balance received:", hre.ethers.utils.formatUnits(results[2], 6), "USDC");
            console.log("✅ Success:", results[3]);
            console.log("❌ Error:", results[4]);
            
            if (results[3]) {
                console.log(`🎉 FLASH LOAN ${amountStr} USDC SUCCESS!`);
                console.log("💡 Fee:", hre.ethers.utils.formatUnits(results[1], 6), "USDC");
                
                // Calculate fee percentage
                if (results[0].gt(0)) {
                    const feePercent = results[1].mul(10000).div(results[0]).toNumber() / 100;
                    console.log("📊 Fee percentage:", feePercent.toFixed(4) + "%");
                }
            } else {
                console.log(`❌ FLASH LOAN ${amountStr} USDC FAILED!`);
                console.log("❌ Error:", results[4]);
                
                // If this amount fails, don't test larger amounts
                if (results[4].includes("Insufficient") || results[4].includes("balance")) {
                    console.log("💡 Stopping tests due to balance issue");
                    break;
                }
            }
            
        } catch (error) {
            console.error(`❌ TEST ${i + 1} FAILED:`, error.message);
            
            if (error.message.includes("revert")) {
                console.log("💡 Contract reverted - this reveals the exact issue");
            }
            
            if (error.message.includes("gas")) {
                console.log("⛽ Gas issue - try increasing gas limit");
            }
            
            // Continue with next test
        }
        
        console.log("\n" + "=".repeat(60));
    }
    
    console.log("\n🎯 BALANCER FLASH LOAN MECHANISM TEST COMPLETE!");
    console.log("💡 This test reveals the exact issue with Balancer flash loans");
}

main()
    .then(() => {
        console.log("✅ TEST COMPLETE");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ TEST FAILED:", error);
        process.exit(1);
    });
