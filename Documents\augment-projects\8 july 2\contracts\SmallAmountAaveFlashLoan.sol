// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * 🧪 SMALL AMOUNT AAVE FLASH LOAN TEST
 * Test with $100 instead of $10,000 to validate the strategy
 */

interface IERC20 {
    function balanceOf(address account) external view returns (uint256);
    function transfer(address to, uint256 amount) external returns (bool);
    function approve(address spender, uint256 amount) external returns (bool);
}

interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external;
}

interface IAavePool {
    function supply(address asset, uint256 amount, address onBehalfOf, uint16 referralCode) external;
    function borrow(address asset, uint256 amount, uint256 interestRateMode, uint16 referralCode, address onBehalfOf) external;
    function withdraw(address asset, uint256 amount, address to) external returns (uint256);
    function repay(address asset, uint256 amount, uint256 interestRateMode, address onBehalfOf) external returns (uint256);
    function setUserEMode(uint8 categoryId) external;
    function getUserAccountData(address user) external view returns (
        uint256 totalCollateralBase,
        uint256 totalDebtBase,
        uint256 availableBorrowsBase,
        uint256 currentLiquidationThreshold,
        uint256 ltv,
        uint256 healthFactor
    );
}

contract SmallAmountAaveFlashLoan is IFlashLoanRecipient {
    
    // 🎯 ARBITRUM ADDRESSES (VERIFIED ACTIVE)
    IERC20 public constant USDC = IERC20(******************************************);
    IERC20 public constant WETH = IERC20(******************************************);
    IBalancerVault public constant BALANCER = IBalancerVault(******************************************);
    IAavePool public constant AAVE_POOL = IAavePool(******************************************);
    
    address public constant PROFIT_WALLET = ******************************************;
    
    // 🧪 SMALL AMOUNT TEST PARAMETERS
    uint256 public constant FLASH_AMOUNT = 100e6;     // $100 USDC (small test)
    uint256 public constant SUPPLY_AMOUNT = 80e6;     // $80 USDC supply
    uint8 public constant EMODE_CATEGORY = 1;         // Stablecoins eMode
    uint256 public constant SAFETY_MARGIN = 85;       // 85% safety margin
    
    // 📊 TRACKING
    uint256 public totalExecutions;
    uint256 public lastProfit;
    bool public lastSuccess;
    uint256 public totalProfit;
    
    // 🎯 EVENTS
    event DebugStep(string step, uint256 value);
    event FlashLoanReceived(uint256 amount, uint256 fee);
    event EmodeSet(uint8 category);
    event SupplyCompleted(address asset, uint256 amount);
    event BorrowCompleted(address asset, uint256 amount);
    event WithdrawCompleted(address asset, uint256 amount);
    event RepayCompleted(address asset, uint256 amount);
    event ProfitExtracted(uint256 profit, address wallet, uint256 execution);
    
    /**
     * 🚀 EXECUTE SMALL AMOUNT FLASH LOAN
     */
    function executeSmallAmountFlashLoan() external {
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        
        tokens[0] = address(USDC);
        amounts[0] = FLASH_AMOUNT;
        
        BALANCER.flashLoan(address(this), tokens, amounts, "");
    }
    
    /**
     * 🎯 FLASH LOAN CALLBACK - FIXED VERSION
     */
    function receiveFlashLoan(
        address[] memory,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory
    ) external override {
        require(msg.sender == address(BALANCER), "Only Balancer");
        
        uint256 flashAmount = amounts[0];
        uint256 initialBalance = USDC.balanceOf(address(this));
        
        emit DebugStep("Flash loan received", initialBalance);
        emit FlashLoanReceived(flashAmount, feeAmounts[0]);
        
        // Verify flash loan (WORKING MECHANISM)
        require(initialBalance >= flashAmount, "Flash loan not received");
        // Note: Balancer may charge small fees, so we accept any fee amount
        
        // Execute SIMPLE AAVE STRATEGY (reduced complexity)
        executeSimpleAaveStrategy();
        
        // Repay flash loan (including any fees)
        uint256 totalRepayment = flashAmount + feeAmounts[0];
        USDC.transfer(address(BALANCER), totalRepayment);
        emit DebugStep("Flash loan repaid", totalRepayment);
        
        // Extract profit
        uint256 finalBalance = USDC.balanceOf(address(this));
        totalExecutions++;
        
        if (finalBalance > 0) {
            lastProfit = finalBalance;
            totalProfit += finalBalance;
            USDC.transfer(PROFIT_WALLET, finalBalance);
            lastSuccess = true;
            emit ProfitExtracted(finalBalance, PROFIT_WALLET, totalExecutions);
        } else {
            lastProfit = 0;
            lastSuccess = false;
        }
        
        emit DebugStep("Small amount strategy completed", finalBalance);
    }
    
    /**
     * 💰 SIMPLE AAVE STRATEGY - REDUCED COMPLEXITY
     */
    function executeSimpleAaveStrategy() internal {
        emit DebugStep("Starting simple Aave strategy", FLASH_AMOUNT);
        
        // Step 1: Enable eMode for maximum leverage (97% LTV)
        AAVE_POOL.setUserEMode(EMODE_CATEGORY);
        emit EmodeSet(EMODE_CATEGORY);
        emit DebugStep("eMode enabled", EMODE_CATEGORY);
        
        // Step 2: Supply USDC as collateral
        USDC.approve(address(AAVE_POOL), SUPPLY_AMOUNT);
        AAVE_POOL.supply(address(USDC), SUPPLY_AMOUNT, address(this), 0);
        emit SupplyCompleted(address(USDC), SUPPLY_AMOUNT);
        emit DebugStep("USDC supplied", SUPPLY_AMOUNT);
        
        // Step 3: Get current account data
        (uint256 totalCollateral, uint256 totalDebt, uint256 availableBorrows,,,) = 
            AAVE_POOL.getUserAccountData(address(this));
        
        emit DebugStep("Total collateral", totalCollateral);
        emit DebugStep("Available borrows", availableBorrows);
        
        // Step 4: Calculate safe borrow amount (conservative)
        uint256 safeBorrowAmount = (availableBorrows * SAFETY_MARGIN) / 100;
        
        // Convert from base currency to USDC (FIXED: Skip oracle, assume $1 USDC)
        uint256 usdcBorrowAmount = safeBorrowAmount / 1e2; // Convert from 8 decimals to 6 decimals
        
        emit DebugStep("Safe borrow amount", safeBorrowAmount);
        emit DebugStep("USDC borrow amount", usdcBorrowAmount);
        
        // Step 5: Borrow USDC (only if reasonable amount)
        if (usdcBorrowAmount > 1e6 && usdcBorrowAmount <= availableBorrows) { // At least $1
            AAVE_POOL.borrow(address(USDC), usdcBorrowAmount, 2, 0, address(this)); // Variable rate
            emit BorrowCompleted(address(USDC), usdcBorrowAmount);
            emit DebugStep("USDC borrowed", usdcBorrowAmount);
            
            // Step 6: Immediately repay to close position (simple strategy)
            USDC.approve(address(AAVE_POOL), usdcBorrowAmount);
            AAVE_POOL.repay(address(USDC), usdcBorrowAmount, 2, address(this));
            emit RepayCompleted(address(USDC), usdcBorrowAmount);
            emit DebugStep("USDC repaid", usdcBorrowAmount);
        }
        
        // Step 7: Withdraw all collateral
        AAVE_POOL.withdraw(address(USDC), SUPPLY_AMOUNT, address(this));
        emit WithdrawCompleted(address(USDC), SUPPLY_AMOUNT);
        emit DebugStep("USDC withdrawn", SUPPLY_AMOUNT);
        
        uint256 finalBalance = USDC.balanceOf(address(this));
        emit DebugStep("Simple Aave strategy completed", finalBalance);
    }
    
    /**
     * 📊 GET EXECUTION STATS
     */
    function getExecutionStats() external view returns (
        uint256 lastProfitAmount,
        bool lastExecutionSuccess,
        uint256 totalExecutionCount,
        uint256 totalProfits
    ) {
        return (lastProfit, lastSuccess, totalExecutions, totalProfit);
    }
    
    function getArbitrumInfo() external view returns (
        address usdc,
        address weth,
        address balancer,
        address aavePool,
        address oracle
    ) {
        return (
            address(USDC),
            address(WETH),
            address(BALANCER),
            address(AAVE_POOL),
            address(0) // No oracle used
        );
    }
}
