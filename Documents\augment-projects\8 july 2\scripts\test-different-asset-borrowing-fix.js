/**
 * 🔬 TEST DIFFERENT ASSET BORROWING FIX
 * Deploy and test borrowing WETH instead of USDC
 */

const { ethers } = require('hardhat');

async function testDifferentAssetBorrowingFix() {
    console.log('🔬 TEST DIFFERENT ASSET BORROWING FIX');
    console.log('🎯 HYPOTHESIS: Borrowing WETH instead of USDC fixes the issue');
    console.log('💡 THEORY: Aave restricts same-asset borrowing (USDC → USDC)');
    console.log('🔧 TEST 2 OF 5 BORROWING RESEARCH TESTS');
    console.log('=' .repeat(80));
    
    const [deployer] = await ethers.getSigners();
    const network = await ethers.provider.getNetwork();
    
    console.log(`👤 Deployer: ${deployer.address}`);
    console.log(`🌐 Network: ${network.name} (${network.chainId})`);
    
    const ethBalance = await deployer.getBalance();
    console.log(`💰 ETH Balance: ${ethers.utils.formatEther(ethBalance)} ETH`);
    
    try {
        console.log('\n🚀 DEPLOYING DIFFERENT ASSET BORROWING FIX...');
        
        const gasPrice = await ethers.provider.getGasPrice();
        const adjustedGasPrice = gasPrice.mul(120).div(100); // 20% higher
        console.log(`⛽ Adjusted Gas Price: ${ethers.utils.formatUnits(adjustedGasPrice, 'gwei')} gwei`);
        
        const DifferentAssetBorrowingFix = await ethers.getContractFactory('DifferentAssetBorrowingFix');
        
        const differentAssetContract = await DifferentAssetBorrowingFix.deploy({
            gasPrice: adjustedGasPrice
        });
        
        console.log(`📝 Deployment TX: ${differentAssetContract.deployTransaction.hash}`);
        console.log('⏳ Waiting for confirmation...');
        
        await differentAssetContract.deployed();
        const receipt = await differentAssetContract.deployTransaction.wait();
        
        console.log(`🎉 DIFFERENT ASSET CONTRACT DEPLOYED: ${differentAssetContract.address}`);
        console.log(`⛽ Gas Used: ${receipt.gasUsed.toLocaleString()}`);
        console.log(`💸 Deployment Cost: ${ethers.utils.formatEther(receipt.gasUsed.mul(adjustedGasPrice))} ETH`);
        
        // Test contract info
        console.log('\n🔧 TESTING CONTRACT INFO...');
        
        try {
            const contractInfo = await differentAssetContract.getContractInfo();
            console.log('✅ DIFFERENT ASSET CONTRACT CONFIGURATION:');
            console.log(`   USDC: ${contractInfo.usdc}`);
            console.log(`   WETH: ${contractInfo.weth}`);
            console.log(`   Balancer: ${contractInfo.balancer}`);
            console.log(`   Aave Pool: ${contractInfo.aavePool}`);
            console.log(`   Profit Wallet: ${contractInfo.profitWallet}`);
            
        } catch (error) {
            console.log(`⚠️ Contract info test failed: ${error.message.substring(0, 50)}...`);
        }
        
        // Execute the different asset borrowing test
        console.log('\n🔬 EXECUTING DIFFERENT ASSET BORROWING TEST...');
        console.log('🎯 THIS TESTS IF BORROWING WETH INSTEAD OF USDC WORKS!');
        console.log('💸 Flash loan: $10,000 USDC (0% fee)');
        console.log('🏦 Supply: $8,000 USDC to Aave as collateral');
        console.log('💎 Borrow: ~0.8 WETH (~$2,080) instead of USDC');
        console.log('🔄 Swap: WETH → USDC via Uniswap');
        console.log('💸 Repay: ~0.8 WETH to Aave');
        console.log('💸 Withdraw: $8,000 USDC collateral');
        console.log('🎯 Expected: WETH BORROWING SUCCESS!');
        
        try {
            const tx = await differentAssetContract.executeDifferentAssetTest({
                gasLimit: 6000000,
                gasPrice: adjustedGasPrice
            });
            
            console.log(`📝 DIFFERENT ASSET TEST TX Hash: ${tx.hash}`);
            console.log('⏳ Waiting for confirmation...');
            
            const differentAssetReceipt = await tx.wait();
            
            console.log(`📊 DIFFERENT ASSET TEST STATUS: ${differentAssetReceipt.status === 1 ? '🎉 SUCCESS!' : '❌ FAILED'}`);
            console.log(`⛽ Gas Used: ${differentAssetReceipt.gasUsed.toLocaleString()}`);
            console.log(`💸 Execution Cost: ${ethers.utils.formatEther(differentAssetReceipt.gasUsed.mul(adjustedGasPrice))} ETH`);
            
            if (differentAssetReceipt.status === 1) {
                console.log('\n🎉🎉🎉 DIFFERENT ASSET TEST SUCCESS! 🎉🎉🎉');
                
                // Check execution stats
                const stats = await differentAssetContract.getExecutionStats();
                console.log('\n📊 DIFFERENT ASSET TEST STATS:');
                console.log(`   Last Profit: $${ethers.utils.formatUnits(stats.lastProfitAmount, 6)}`);
                console.log(`   Last Success: ${stats.lastExecutionSuccess}`);
                console.log(`   Total Executions: ${stats.totalExecutionCount}`);
                console.log(`   Total Profit: $${ethers.utils.formatUnits(stats.totalProfits, 6)}`);
                
                // Analyze events to see if WETH borrowing worked
                console.log('\n📋 ANALYZING DIFFERENT ASSET TEST EVENTS...');
                if (differentAssetReceipt.logs && differentAssetReceipt.logs.length > 0) {
                    
                    let wethBorrowingWorked = false;
                    let wethBorrowAmount = '0';
                    let profitGenerated = false;
                    let profitAmount = '0';
                    let differentAssetSteps = [];
                    
                    for (const log of differentAssetReceipt.logs) {
                        try {
                            const decoded = differentAssetContract.interface.parseLog(log);
                            
                            if (decoded.name === 'DifferentAssetTest') {
                                const step = decoded.args.step;
                                const value = decoded.args.value.toString();
                                differentAssetSteps.push(`${step}: ${value}`);
                                
                            } else if (decoded.name === 'WethBorrowingSuccess') {
                                wethBorrowAmount = ethers.utils.formatEther(decoded.args.amount);
                                console.log(`   🎉 WETH BORROWING SUCCESS: ${wethBorrowAmount} WETH`);
                                wethBorrowingWorked = true;
                                
                            } else if (decoded.name === 'WethBorrowingFailed') {
                                const reason = decoded.args.reason;
                                console.log(`   ❌ WETH BORROWING FAILED: ${reason}`);
                                
                            } else if (decoded.name === 'ProfitExtracted') {
                                profitAmount = ethers.utils.formatUnits(decoded.args.profit, 6);
                                console.log(`   💰 PROFIT EXTRACTED: $${profitAmount} USDC`);
                                profitGenerated = true;
                            }
                        } catch (error) {
                            // Skip unparseable events
                        }
                    }
                    
                    // Show key different asset steps
                    console.log('\n💎 DIFFERENT ASSET EXECUTION STEPS:');
                    differentAssetSteps.slice(0, 12).forEach((step, index) => {
                        console.log(`   ${index + 1}. ${step}`);
                    });
                    if (differentAssetSteps.length > 12) {
                        console.log(`   ... and ${differentAssetSteps.length - 12} more steps`);
                    }
                    
                    console.log('\n🎯 DIFFERENT ASSET TEST RESULTS:');
                    console.log(`   WETH Borrowing Worked: ${wethBorrowingWorked ? '✅ YES' : '❌ NO'}`);
                    console.log(`   Profit Generated: ${profitGenerated ? '✅ YES' : '❌ NO'}`);
                    
                    if (wethBorrowingWorked) {
                        console.log('\n🎉🎉🎉 DIFFERENT ASSET FIX SUCCESSFUL! 🎉🎉🎉');
                        console.log(`💎 WETH Borrowed: ${wethBorrowAmount} WETH`);
                        console.log(`💰 Profit Generated: $${profitAmount} USDC`);
                        console.log('🔧 BORROWING ISSUE IS FIXED!');
                        console.log('💡 SOLUTION: Borrow different asset than collateral!');
                        
                        console.log('\n🎯 NEXT STEPS:');
                        console.log('1. 🚀 Deploy production version with WETH borrowing');
                        console.log('2. 💰 Execute multiple times daily for profits');
                        console.log('3. 📈 Optimize WETH amounts for maximum profit');
                        console.log('4. 🔧 Consider other asset combinations');
                        
                    } else {
                        console.log('\n💡 DIFFERENT ASSET DID NOT FIX BORROWING');
                        console.log('🔬 Need to test next hypothesis');
                        console.log('🎯 Moving to TEST 3: Larger Borrow Amounts');
                        
                        console.log('\n🔬 RESEARCH STATUS:');
                        console.log('   ❌ Test 1: Timing Delay - FAILED');
                        console.log('   ❌ Test 2: Different Asset (WETH) - FAILED');
                        console.log('   ⏳ Test 3: Larger Borrow Amounts - NEXT');
                        console.log('   ⏳ Test 4: Health Factor Validation - PENDING');
                        console.log('   ⏳ Test 5: Non-Flash Loan Context - PENDING');
                    }
                }
                
            } else {
                console.log('\n❌ DIFFERENT ASSET TEST FAILED');
                console.log('💡 Check transaction details for failure reason');
            }
            
        } catch (error) {
            console.log('❌ DIFFERENT ASSET TEST EXECUTION FAILED');
            console.log(`Error: ${error.message}`);
            
            if (error.message.includes('revert')) {
                console.log('💡 Transaction reverted - check contract logic');
            } else if (error.message.includes('insufficient')) {
                console.log('💡 Insufficient funds for execution');
            }
        }
        
        console.log('\n🎯 DIFFERENT ASSET BORROWING TEST COMPLETE');
        console.log(`🔬 Test Contract: ${differentAssetContract.address}`);
        console.log('📊 This validates whether borrowing different asset fixes the issue');
        
        return {
            contract: differentAssetContract,
            address: differentAssetContract.address,
            testType: 'Different Asset Borrowing Fix (WETH)'
        };
        
    } catch (error) {
        console.error('💥 DIFFERENT ASSET BORROWING TEST FAILED:', error.message);
        throw error;
    }
}

// Execute different asset borrowing test
if (require.main === module) {
    testDifferentAssetBorrowingFix()
        .then((result) => {
            console.log('\n🎉 DIFFERENT ASSET BORROWING TEST COMPLETED!');
            console.log(`🔬 Test Contract: ${result.address}`);
            console.log(`🧪 Test Type: ${result.testType}`);
            console.log('🔬 BORROWING RESEARCH CONTINUES!');
        })
        .catch((error) => {
            console.error('Different asset borrowing test failed:', error.message);
        });
}

module.exports = { testDifferentAssetBorrowingFix };
