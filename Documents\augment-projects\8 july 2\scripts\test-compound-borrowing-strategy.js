/**
 * 🎯 TEST OPTION 1: COMPOUND BORROWING STRATEGY
 * Deploy and test Compound V3 borrowing within flash loan context
 */

const { ethers } = require('hardhat');

async function testCompoundBorrowingStrategy() {
    console.log('🎯 TEST OPTION 1: COMPOUND BORROWING STRATEGY');
    console.log('💡 HYPOTHESIS: Compound V3 allows borrowing within flash loan context');
    console.log('🔧 STRATEGY: Flash loan → Supply to Aave → Borrow from Compound → Profit');
    console.log('🎯 TESTING IF COMPOUND BYPASSES AAVE FLASH LOAN RESTRICTION');
    console.log('=' .repeat(80));
    
    const [deployer] = await ethers.getSigners();
    const network = await ethers.provider.getNetwork();
    
    console.log(`👤 Deployer: ${deployer.address}`);
    console.log(`🌐 Network: ${network.name} (${network.chainId})`);
    
    const ethBalance = await deployer.getBalance();
    console.log(`💰 ETH Balance: ${ethers.utils.formatEther(ethBalance)} ETH`);
    
    try {
        console.log('\n🚀 DEPLOYING COMPOUND BORROWING STRATEGY...');
        
        const gasPrice = await ethers.provider.getGasPrice();
        const adjustedGasPrice = gasPrice.mul(120).div(100); // 20% higher
        console.log(`⛽ Adjusted Gas Price: ${ethers.utils.formatUnits(adjustedGasPrice, 'gwei')} gwei`);
        
        const CompoundBorrowingStrategy = await ethers.getContractFactory('CompoundBorrowingStrategy');
        
        const compoundContract = await CompoundBorrowingStrategy.deploy({
            gasPrice: adjustedGasPrice
        });
        
        console.log(`📝 Deployment TX: ${compoundContract.deployTransaction.hash}`);
        console.log('⏳ Waiting for confirmation...');
        
        await compoundContract.deployed();
        const receipt = await compoundContract.deployTransaction.wait();
        
        console.log(`🎉 COMPOUND BORROWING CONTRACT DEPLOYED: ${compoundContract.address}`);
        console.log(`⛽ Gas Used: ${receipt.gasUsed.toLocaleString()}`);
        console.log(`💸 Deployment Cost: ${ethers.utils.formatEther(receipt.gasUsed.mul(adjustedGasPrice))} ETH`);
        
        // Test contract info
        console.log('\n🔧 TESTING CONTRACT INFO...');
        
        try {
            const contractInfo = await compoundContract.getContractInfo();
            console.log('✅ COMPOUND BORROWING CONTRACT CONFIGURATION:');
            console.log(`   USDC: ${contractInfo.usdc}`);
            console.log(`   Balancer: ${contractInfo.balancer}`);
            console.log(`   Aave Pool: ${contractInfo.aavePool}`);
            console.log(`   Compound USDC: ${contractInfo.compoundUsdc}`);
            console.log(`   Profit Wallet: ${contractInfo.profitWallet}`);
            
        } catch (error) {
            console.log(`⚠️ Contract info test failed: ${error.message.substring(0, 50)}...`);
        }
        
        // Check Compound status
        console.log('\n🔍 CHECKING COMPOUND V3 STATUS...');
        
        try {
            const compoundStatus = await compoundContract.checkCompoundStatus();
            console.log('📊 COMPOUND V3 STATUS:');
            console.log(`   Base Token: ${compoundStatus.baseToken}`);
            console.log(`   Supply Rate: ${compoundStatus.supplyRate.toString()}`);
            console.log(`   Borrow Rate: ${compoundStatus.borrowRate.toString()}`);
            console.log(`   Compound Active: ${compoundStatus.compoundActive ? '✅ YES' : '❌ NO'}`);
            
            if (!compoundStatus.compoundActive) {
                console.log('⚠️ WARNING: Compound V3 may not be active on Arbitrum');
                console.log('💡 Continuing with test anyway...');
            }
            
        } catch (error) {
            console.log(`⚠️ Compound status check failed: ${error.message.substring(0, 50)}...`);
        }
        
        // Execute the Compound borrowing strategy
        console.log('\n🎯 EXECUTING COMPOUND BORROWING STRATEGY...');
        console.log('🔥 THIS IS THE CRITICAL TEST!');
        console.log('💸 Flash loan: $10,000 USDC (0% fee)');
        console.log('🏦 Supply: $8,000 USDC to Aave (collateral)');
        console.log('🏦 Supply: $1,500 USDC to Compound (borrowing power)');
        console.log('💳 Borrow: $750 USDC from Compound (KEY TEST!)');
        console.log('💸 Repay: $750 USDC to Compound');
        console.log('💸 Withdraw: From both Aave and Compound');
        console.log('🎯 Expected: COMPOUND BORROWING SUCCESS!');
        
        try {
            const tx = await compoundContract.executeCompoundBorrowingStrategy({
                gasLimit: 6000000,
                gasPrice: adjustedGasPrice
            });
            
            console.log(`📝 COMPOUND BORROWING TEST TX Hash: ${tx.hash}`);
            console.log('⏳ Waiting for confirmation...');
            
            const compoundReceipt = await tx.wait();
            
            console.log(`📊 COMPOUND BORROWING TEST STATUS: ${compoundReceipt.status === 1 ? '🎉 SUCCESS!' : '❌ FAILED'}`);
            console.log(`⛽ Gas Used: ${compoundReceipt.gasUsed.toLocaleString()}`);
            console.log(`💸 Execution Cost: ${ethers.utils.formatEther(compoundReceipt.gasUsed.mul(adjustedGasPrice))} ETH`);
            
            if (compoundReceipt.status === 1) {
                console.log('\n🎉🎉🎉 COMPOUND BORROWING TEST SUCCESS! 🎉🎉🎉');
                
                // Check execution stats
                const stats = await compoundContract.getExecutionStats();
                console.log('\n📊 COMPOUND BORROWING TEST STATS:');
                console.log(`   Last Profit: $${ethers.utils.formatUnits(stats.lastProfitAmount, 6)}`);
                console.log(`   Last Success: ${stats.lastExecutionSuccess}`);
                console.log(`   Total Executions: ${stats.totalExecutionCount}`);
                console.log(`   Total Profit: $${ethers.utils.formatUnits(stats.totalProfits, 6)}`);
                
                // Analyze events to see if Compound borrowing worked
                console.log('\n📋 ANALYZING COMPOUND BORROWING TEST EVENTS...');
                if (compoundReceipt.logs && compoundReceipt.logs.length > 0) {
                    
                    let compoundBorrowingWorked = false;
                    let compoundBorrowAmount = '0';
                    let profitGenerated = false;
                    let profitAmount = '0';
                    let compoundSteps = [];
                    
                    for (const log of compoundReceipt.logs) {
                        try {
                            const decoded = compoundContract.interface.parseLog(log);
                            
                            if (decoded.name === 'CompoundBorrowingTest') {
                                const step = decoded.args.step;
                                const value = decoded.args.value.toString();
                                compoundSteps.push(`${step}: ${value}`);
                                
                            } else if (decoded.name === 'CompoundBorrowingSuccess') {
                                compoundBorrowAmount = ethers.utils.formatUnits(decoded.args.amount, 6);
                                console.log(`   🎉 COMPOUND BORROWING SUCCESS: $${compoundBorrowAmount} USDC`);
                                compoundBorrowingWorked = true;
                                
                            } else if (decoded.name === 'CompoundBorrowingFailed') {
                                const reason = decoded.args.reason;
                                console.log(`   ❌ COMPOUND BORROWING FAILED: ${reason}`);
                                
                            } else if (decoded.name === 'ProfitExtracted') {
                                profitAmount = ethers.utils.formatUnits(decoded.args.profit, 6);
                                console.log(`   💰 PROFIT EXTRACTED: $${profitAmount} USDC`);
                                profitGenerated = true;
                            }
                        } catch (error) {
                            // Skip unparseable events
                        }
                    }
                    
                    // Show key Compound steps
                    console.log('\n🏦 COMPOUND BORROWING EXECUTION STEPS:');
                    compoundSteps.slice(0, 12).forEach((step, index) => {
                        console.log(`   ${index + 1}. ${step}`);
                    });
                    if (compoundSteps.length > 12) {
                        console.log(`   ... and ${compoundSteps.length - 12} more steps`);
                    }
                    
                    console.log('\n🎯 COMPOUND BORROWING TEST RESULTS:');
                    console.log(`   Compound Borrowing Worked: ${compoundBorrowingWorked ? '✅ YES' : '❌ NO'}`);
                    console.log(`   Profit Generated: ${profitGenerated ? '✅ YES' : '❌ NO'}`);
                    
                    if (compoundBorrowingWorked) {
                        console.log('\n🎉🎉🎉 OPTION 1 SUCCESSFUL! 🎉🎉🎉');
                        console.log(`💰 Compound Borrowed: $${compoundBorrowAmount} USDC`);
                        console.log(`💰 Profit Generated: $${profitAmount} USDC`);
                        console.log('🔧 COMPOUND BYPASSES AAVE FLASH LOAN RESTRICTION!');
                        console.log('💡 SOLUTION FOUND: Use Compound for borrowing!');
                        
                        console.log('\n🎯 NEXT STEPS:');
                        console.log('1. ✅ OPTION 1 WORKS - Cross off from list');
                        console.log('2. 🚀 Scale up Compound borrowing amounts');
                        console.log('3. 💰 Deploy production version');
                        console.log('4. 📈 Execute multiple times daily for profits');
                        
                    } else {
                        console.log('\n💡 OPTION 1: COMPOUND BORROWING FAILED');
                        console.log('❌ Cross off OPTION 1 from list');
                        console.log('🔬 Moving to OPTION 2: Multi-Protocol Leverage');
                        
                        console.log('\n🔬 STRATEGY TEST STATUS:');
                        console.log('   ❌ OPTION 1: Compound Borrowing - FAILED');
                        console.log('   ⏳ OPTION 2: Multi-Protocol Leverage - NEXT');
                        console.log('   ⏳ OPTION 3: Pure Aave Rewards Farming - PENDING');
                    }
                }
                
            } else {
                console.log('\n❌ COMPOUND BORROWING TEST FAILED');
                console.log('💡 Check transaction details for failure reason');
            }
            
        } catch (error) {
            console.log('❌ COMPOUND BORROWING TEST EXECUTION FAILED');
            console.log(`Error: ${error.message}`);
            
            if (error.message.includes('revert')) {
                console.log('💡 Transaction reverted - check contract logic');
            } else if (error.message.includes('insufficient')) {
                console.log('💡 Insufficient funds for execution');
            }
        }
        
        console.log('\n🎯 COMPOUND BORROWING STRATEGY TEST COMPLETE');
        console.log(`🔬 Test Contract: ${compoundContract.address}`);
        console.log('📊 This validates whether Compound allows borrowing in flash loan context');
        
        return {
            contract: compoundContract,
            address: compoundContract.address,
            testType: 'OPTION 1: Compound Borrowing Strategy'
        };
        
    } catch (error) {
        console.error('💥 COMPOUND BORROWING STRATEGY TEST FAILED:', error.message);
        throw error;
    }
}

// Execute Compound borrowing strategy test
if (require.main === module) {
    testCompoundBorrowingStrategy()
        .then((result) => {
            console.log('\n🎉 COMPOUND BORROWING STRATEGY TEST COMPLETED!');
            console.log(`🔬 Test Contract: ${result.address}`);
            console.log(`🧪 Test Type: ${result.testType}`);
            console.log('🔬 SYSTEMATIC STRATEGY TESTING IN PROGRESS!');
        })
        .catch((error) => {
            console.error('Compound borrowing strategy test failed:', error.message);
        });
}

module.exports = { testCompoundBorrowingStrategy };
