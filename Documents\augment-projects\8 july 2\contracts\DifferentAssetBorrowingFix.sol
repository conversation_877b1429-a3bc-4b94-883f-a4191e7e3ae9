// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * 🔬 DIFFERENT ASSET BORROWING FIX
 * Test if borrowing WETH instead of USDC fixes the issue
 * Hypothesis: <PERSON><PERSON> restricts same-asset borrowing (USDC collateral → USDC borrow)
 */

interface IERC20 {
    function balanceOf(address account) external view returns (uint256);
    function transfer(address to, uint256 amount) external returns (bool);
    function approve(address spender, uint256 amount) external returns (bool);
}

interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external;
}

interface IAavePool {
    function supply(address asset, uint256 amount, address onBehalfOf, uint16 referralCode) external;
    function borrow(address asset, uint256 amount, uint256 interestRateMode, uint16 referralCode, address onBehalfOf) external;
    function withdraw(address asset, uint256 amount, address to) external returns (uint256);
    function repay(address asset, uint256 amount, uint256 interestRateMode, address onBehalfOf) external returns (uint256);
    function setUserEMode(uint8 categoryId) external;
    function getUserAccountData(address user) external view returns (
        uint256 totalCollateralBase,
        uint256 totalDebtBase,
        uint256 availableBorrowsBase,
        uint256 currentLiquidationThreshold,
        uint256 ltv,
        uint256 healthFactor
    );
}

interface IUniswapV3Router {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }
    
    function exactInputSingle(ExactInputSingleParams calldata params) external payable returns (uint256 amountOut);
}

contract DifferentAssetBorrowingFix is IFlashLoanRecipient {
    
    // 🎯 ARBITRUM ADDRESSES (VERIFIED WORKING)
    IERC20 public constant USDC = IERC20(******************************************);
    IERC20 public constant WETH = IERC20(******************************************);
    IBalancerVault public constant BALANCER = IBalancerVault(******************************************);
    IAavePool public constant AAVE_POOL = IAavePool(******************************************);
    IUniswapV3Router public constant UNISWAP_ROUTER = IUniswapV3Router(******************************************);
    
    address public constant PROFIT_WALLET = ******************************************;
    
    // 💰 STRATEGY PARAMETERS (PROVEN WORKING)
    uint256 public constant FLASH_AMOUNT = 10000e6;   // $10,000 USDC
    uint256 public constant SUPPLY_AMOUNT = 8000e6;   // $8,000 USDC supply
    uint8 public constant EMODE_CATEGORY = 1;         // Stablecoins eMode
    uint256 public constant SAFETY_MARGIN = 85;       // 85% safety margin
    
    // 📊 TRACKING
    uint256 public totalExecutions;
    uint256 public lastProfit;
    bool public lastSuccess;
    uint256 public totalProfit;
    
    // 🎯 EVENTS
    event DifferentAssetTest(string step, uint256 value);
    event WethBorrowingSuccess(uint256 amount);
    event WethBorrowingFailed(string reason);
    event ProfitExtracted(uint256 profit, address wallet, uint256 execution);
    
    /**
     * 🚀 EXECUTE DIFFERENT ASSET BORROWING TEST
     */
    function executeDifferentAssetTest() external {
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        
        tokens[0] = address(USDC);
        amounts[0] = FLASH_AMOUNT;
        
        emit DifferentAssetTest("Starting different asset test", block.timestamp);
        
        // Execute flash loan
        BALANCER.flashLoan(address(this), tokens, amounts, "");
    }
    
    /**
     * 🎯 FLASH LOAN CALLBACK WITH DIFFERENT ASSET BORROWING
     */
    function receiveFlashLoan(
        address[] memory,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory
    ) external override {
        require(msg.sender == address(BALANCER), "Only Balancer");
        
        uint256 flashAmount = amounts[0];
        uint256 initialBalance = USDC.balanceOf(address(this));
        
        emit DifferentAssetTest("Flash loan received", flashAmount);
        
        // Verify flash loan received
        require(initialBalance >= flashAmount, "Flash loan not received");
        
        // Execute different asset Aave strategy
        _executeDifferentAssetAaveStrategy();
        
        // Repay flash loan (including any fees)
        uint256 totalRepayment = flashAmount + feeAmounts[0];
        USDC.transfer(address(BALANCER), totalRepayment);
        
        emit DifferentAssetTest("Flash loan repaid", totalRepayment);
        
        // Extract profit
        uint256 finalBalance = USDC.balanceOf(address(this));
        totalExecutions++;
        
        if (finalBalance > 0) {
            lastProfit = finalBalance;
            totalProfit += finalBalance;
            USDC.transfer(PROFIT_WALLET, finalBalance);
            lastSuccess = true;
            emit ProfitExtracted(finalBalance, PROFIT_WALLET, totalExecutions);
        } else {
            lastProfit = 0;
            lastSuccess = false;
        }
        
        emit DifferentAssetTest("Strategy completed", finalBalance);
    }
    
    /**
     * 💰 DIFFERENT ASSET AAVE STRATEGY
     * Supply USDC, borrow WETH instead of USDC
     */
    function _executeDifferentAssetAaveStrategy() internal {
        emit DifferentAssetTest("Starting Aave strategy", 1);
        
        // Step 1: Enable eMode
        AAVE_POOL.setUserEMode(EMODE_CATEGORY);
        emit DifferentAssetTest("eMode set", EMODE_CATEGORY);
        
        // Step 2: Supply USDC as collateral
        USDC.approve(address(AAVE_POOL), type(uint256).max);
        AAVE_POOL.supply(address(USDC), SUPPLY_AMOUNT, address(this), 0);
        emit DifferentAssetTest("USDC supplied", SUPPLY_AMOUNT);
        
        // Step 3: Get account data
        (
            ,
            ,
            uint256 availableBorrows,
            ,
            ,
            uint256 healthFactor
        ) = AAVE_POOL.getUserAccountData(address(this));
        
        emit DifferentAssetTest("Account data retrieved", availableBorrows);
        emit DifferentAssetTest("Health factor", healthFactor);
        
        // Step 4: Calculate WETH borrow amount
        // Available borrows is in USD (8 decimals)
        // WETH price ~$2600, so we can borrow availableBorrows / 2600 WETH
        uint256 wethPrice = 2600e8; // $2600 in 8 decimals
        uint256 maxWethBorrow = (availableBorrows * 1e18) / wethPrice; // Convert to 18 decimals
        uint256 safeWethBorrow = (maxWethBorrow * SAFETY_MARGIN) / 100;
        
        emit DifferentAssetTest("Max WETH borrow", maxWethBorrow);
        emit DifferentAssetTest("Safe WETH borrow", safeWethBorrow);
        
        // Step 5: Attempt borrowing WETH instead of USDC
        if (safeWethBorrow > 1e15 && safeWethBorrow < 10e18 && healthFactor > 1e18) { // Min 0.001 WETH, Max 10 WETH
            emit DifferentAssetTest("Attempting WETH borrow", safeWethBorrow);
            
            try AAVE_POOL.borrow(address(WETH), safeWethBorrow, 2, 0, address(this)) {
                emit DifferentAssetTest("WETH borrow successful", safeWethBorrow);
                emit WethBorrowingSuccess(safeWethBorrow);
                
                // Convert WETH to USDC via Uniswap
                uint256 wethBalance = WETH.balanceOf(address(this));
                emit DifferentAssetTest("WETH balance received", wethBalance);
                
                if (wethBalance > 0) {
                    // Swap WETH to USDC
                    WETH.approve(address(UNISWAP_ROUTER), wethBalance);
                    
                    IUniswapV3Router.ExactInputSingleParams memory params = IUniswapV3Router.ExactInputSingleParams({
                        tokenIn: address(WETH),
                        tokenOut: address(USDC),
                        fee: 3000, // 0.3% fee tier
                        recipient: address(this),
                        deadline: block.timestamp + 300,
                        amountIn: wethBalance,
                        amountOutMinimum: 0, // Accept any amount of USDC
                        sqrtPriceLimitX96: 0
                    });
                    
                    try UNISWAP_ROUTER.exactInputSingle(params) returns (uint256 usdcReceived) {
                        emit DifferentAssetTest("WETH swapped to USDC", usdcReceived);
                        
                        // Repay WETH borrow
                        WETH.approve(address(AAVE_POOL), type(uint256).max);
                        AAVE_POOL.repay(address(WETH), safeWethBorrow, 2, address(this));
                        emit DifferentAssetTest("WETH repay successful", safeWethBorrow);
                        
                    } catch Error(string memory reason) {
                        emit DifferentAssetTest("WETH swap failed", 0);
                        emit WethBorrowingFailed(string(abi.encodePacked("Swap failed: ", reason)));
                        
                        // Still try to repay WETH
                        WETH.approve(address(AAVE_POOL), type(uint256).max);
                        AAVE_POOL.repay(address(WETH), safeWethBorrow, 2, address(this));
                    }
                }
                
            } catch Error(string memory reason) {
                emit DifferentAssetTest("WETH borrow failed", 0);
                emit WethBorrowingFailed(reason);
                // Continue without borrowing
            } catch {
                emit DifferentAssetTest("WETH borrow failed unknown", 0);
                emit WethBorrowingFailed("Unknown error");
                // Continue without borrowing
            }
        } else {
            emit DifferentAssetTest("WETH borrow skipped", safeWethBorrow);
            emit WethBorrowingFailed("Amount invalid or health factor too low");
        }
        
        // Step 6: Withdraw USDC collateral
        AAVE_POOL.withdraw(address(USDC), SUPPLY_AMOUNT, address(this));
        emit DifferentAssetTest("USDC withdrawal completed", SUPPLY_AMOUNT);
    }
    
    /**
     * 📊 GET EXECUTION STATS
     */
    function getExecutionStats() external view returns (
        uint256 lastProfitAmount,
        bool lastExecutionSuccess,
        uint256 totalExecutionCount,
        uint256 totalProfits
    ) {
        return (lastProfit, lastSuccess, totalExecutions, totalProfit);
    }
    
    /**
     * 🔧 GET CONTRACT INFO
     */
    function getContractInfo() external view returns (
        address usdc,
        address weth,
        address balancer,
        address aavePool,
        address profitWallet
    ) {
        return (
            address(USDC),
            address(WETH),
            address(BALANCER),
            address(AAVE_POOL),
            PROFIT_WALLET
        );
    }
}
