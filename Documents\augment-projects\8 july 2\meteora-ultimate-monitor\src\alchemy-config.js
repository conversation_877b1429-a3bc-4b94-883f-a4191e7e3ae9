// 🔥 ALCHEMY RPC CONFIGURATION
// Your paid Alchemy RPC for unlimited blockchain access

export const ALCHEMY_CONFIG = {
  // 🔑 REPLACE WITH YOUR ACTUAL ALCHEMY API KEY
  API_KEY: 'YOUR_ALCHEMY_API_KEY_HERE',
  
  // 🌐 <PERSON><PERSON>ANA MAINNET ENDPOINT
  get RPC_URL() {
    return `https://solana-mainnet.g.alchemy.com/v2/${this.API_KEY}`;
  },
  
  // ⚙️ CONNECTION SETTINGS
  COMMITMENT: 'confirmed',
  
  // 🚀 RATE LIMITS (Alchemy Pro limits)
  REQUESTS_PER_SECOND: 100,
  BATCH_SIZE: 50,
  
  // 📊 ANALYSIS SETTINGS
  MAX_TRANSACTIONS_PER_POOL: 1000,
  ANALYSIS_TIMEFRAME_HOURS: 24,
  
  // 💰 PROFIT CALCULATION SETTINGS
  FEES_PER_DOLLAR_PRECISION: 6, // 6 decimal places
  MINIMUM_TVL_FOR_ANALYSIS: 100, // $100 minimum TVL
};

// 🔧 VALIDATE CONFIGURATION
export function validateAlchemyConfig() {
  if (ALCHEMY_CONFIG.API_KEY === 'YOUR_ALCHEMY_API_KEY_HERE') {
    console.warn('⚠️ Please set your actual Alchemy API key in alchemy-config.js');
    return false;
  }
  
  console.log('✅ Alchemy configuration validated');
  console.log(`🌐 RPC URL: ${ALCHEMY_CONFIG.RPC_URL.substring(0, 50)}...`);
  return true;
}

// 🚀 GET OPTIMIZED CONNECTION
export function getAlchemyConnection() {
  return {
    url: ALCHEMY_CONFIG.RPC_URL,
    commitment: ALCHEMY_CONFIG.COMMITMENT,
    config: ALCHEMY_CONFIG
  };
}
