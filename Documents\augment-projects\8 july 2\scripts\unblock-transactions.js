const { ethers } = require('hardhat');

/**
 * 🚀 UNBLOCK STUCK TRANSACTIONS
 * Speed up the first stuck transaction to unblock everything
 */

async function unblockTransactions() {
  console.log('\n🚀 UNBLOCKING STUCK TRANSACTIONS');
  console.log('💰 SPEEDING UP FIRST TRANSACTION TO UNBLOCK QUEUE');
  console.log('⚡ THIS WILL UNBLOCK ALL 7 STUCK TRANSACTIONS');
  console.log('🎯 THEN YOUR DEPLOYMENTS WILL CONFIRM IMMEDIATELY');
  console.log('=' .repeat(80));

  try {
    const [deployer] = await ethers.getSigners();
    const provider = ethers.provider;
    
    // Get current status
    const confirmedNonce = await deployer.getTransactionCount();
    const pendingNonce = await deployer.getTransactionCount('pending');
    const currentGasPrice = await provider.getGasPrice();
    
    console.log(`👤 Account: ${deployer.address}`);
    console.log(`📊 Confirmed Nonce: ${confirmedNonce}`);
    console.log(`📊 Pending Nonce: ${pendingNonce}`);
    console.log(`🚨 Stuck Transactions: ${pendingNonce - confirmedNonce}`);
    console.log(`⛽ Current Gas Price: ${ethers.utils.formatUnits(currentGasPrice, 'gwei')} gwei`);
    
    if (pendingNonce <= confirmedNonce) {
      console.log('\n✅ NO STUCK TRANSACTIONS - QUEUE IS CLEAR!');
      return { success: true, message: 'No stuck transactions' };
    }
    
    // Speed up the first stuck transaction (lowest nonce)
    const stuckNonce = confirmedNonce;
    console.log(`\n🎯 SPEEDING UP TRANSACTION WITH NONCE: ${stuckNonce}`);
    
    // Use 3x current gas price to guarantee fast confirmation
    const speedUpGasPrice = currentGasPrice.mul(300).div(100);
    console.log(`⚡ Using Gas Price: ${ethers.utils.formatUnits(speedUpGasPrice, 'gwei')} gwei (3x current)`);
    
    // Send a replacement transaction with higher gas price
    // This will replace the stuck transaction and unblock the queue
    const speedUpTx = await deployer.sendTransaction({
      to: deployer.address, // Send to self
      value: ethers.utils.parseEther('0.001'), // Small amount
      gasPrice: speedUpGasPrice,
      gasLimit: 21000,
      nonce: stuckNonce // Use the stuck nonce
    });
    
    console.log(`📋 Speed-up TX: ${speedUpTx.hash}`);
    console.log(`⏳ Waiting for confirmation...`);
    
    // Wait for confirmation with timeout
    const receipt = await Promise.race([
      speedUpTx.wait(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Speed-up timeout')), 60000)
      )
    ]);
    
    console.log('\n🎉 SPEED-UP TRANSACTION CONFIRMED!');
    console.log(`📦 Block: ${receipt.blockNumber}`);
    console.log(`⛽ Gas Used: ${receipt.gasUsed.toLocaleString()}`);
    
    // Check if queue is unblocked
    const newConfirmedNonce = await deployer.getTransactionCount();
    const newPendingNonce = await deployer.getTransactionCount('pending');
    
    console.log(`\n📊 UPDATED STATUS:`);
    console.log(`   Confirmed Nonce: ${newConfirmedNonce}`);
    console.log(`   Pending Nonce: ${newPendingNonce}`);
    console.log(`   Remaining Stuck: ${newPendingNonce - newConfirmedNonce}`);
    
    if (newPendingNonce - newConfirmedNonce < pendingNonce - confirmedNonce) {
      console.log('\n✅ QUEUE PARTIALLY UNBLOCKED!');
      console.log('🚀 YOUR DEPLOYMENTS SHOULD START CONFIRMING NOW!');
      
      // Check if any of our deployments confirmed
      await checkDeploymentStatus();
      
      return { 
        success: true, 
        message: 'Queue unblocked',
        unblocked: (pendingNonce - confirmedNonce) - (newPendingNonce - newConfirmedNonce)
      };
    } else {
      console.log('\n⚠️  QUEUE STILL BLOCKED - MAY NEED MORE SPEED-UPS');
      return { success: false, message: 'Queue still blocked' };
    }
    
  } catch (error) {
    console.error('\n💥 UNBLOCK FAILED:', error.message);
    
    if (error.message.includes('replacement transaction underpriced')) {
      console.log('\n💡 SOLUTION: Need even higher gas price');
      console.log('🔧 Try canceling transactions instead');
    }
    
    throw error;
  }
}

async function checkDeploymentStatus() {
  console.log('\n🔍 CHECKING DEPLOYMENT STATUS AFTER UNBLOCK...');
  
  const deployments = [
    { name: 'Full Contract', tx: '0x29dccc797d142b62b6d0bdc17bc9d16a2123e102fc3ffb98e11fa0e2b9e1b508' },
    { name: 'Fast Test', tx: '0xc32e909bc2dc0e77cc81b56e841a88b3627414aa1737c1a12103650573f170cc' },
    { name: 'High Gas', tx: '0x50578c65290d039d9b70d8f675c0eb6fedf72ce361a98e4cb4f71c5aa2cef090' },
    { name: 'Ultra High Gas', tx: '0x256c0e73eb44607ea7992dbc3abef404e7576c3fb81fc40d2cbb751ed7aac83c' }
  ];
  
  for (const deployment of deployments) {
    try {
      const receipt = await ethers.provider.getTransactionReceipt(deployment.tx);
      if (receipt) {
        console.log(`✅ ${deployment.name}: CONFIRMED!`);
        console.log(`   Status: ${receipt.status === 1 ? 'SUCCESS' : 'FAILED'}`);
        if (receipt.status === 1 && receipt.contractAddress) {
          console.log(`   📍 Contract: ${receipt.contractAddress}`);
          console.log(`   🚀 READY FOR TESTING!`);
        }
      } else {
        console.log(`⏳ ${deployment.name}: Still pending`);
      }
    } catch (error) {
      console.log(`❌ ${deployment.name}: Error checking`);
    }
  }
}

// Execute unblock
if (require.main === module) {
  unblockTransactions()
    .then((result) => {
      console.log('\n🎉 UNBLOCK OPERATION COMPLETED!');
      if (result.success) {
        console.log('✅ TRANSACTION QUEUE UNBLOCKED!');
        console.log('🚀 YOUR DEPLOYMENTS SHOULD CONFIRM NOW!');
      } else {
        console.log('⚠️  QUEUE STILL BLOCKED - TRY ALTERNATIVE APPROACH');
      }
    })
    .catch((error) => {
      console.error('Unblock failed:', error.message);
    });
}

module.exports = { unblockTransactions };
