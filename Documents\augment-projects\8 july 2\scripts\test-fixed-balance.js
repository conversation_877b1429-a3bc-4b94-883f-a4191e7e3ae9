/**
 * 🚀 TEST FIXED BALANCE ARBITRAGE
 * Test the contract that fixes the exact balance issue
 */

const hre = require("hardhat");

async function main() {
    console.log("🚀 TESTING FIXED BALANCE ARBITRAGE...");
    
    const contractAddress = "******************************************";
    const [deployer] = await hre.ethers.getSigners();
    
    console.log("📝 Testing with account:", deployer.address);
    console.log("📍 Contract address:", contractAddress);
    
    // Get contract instance
    const FixedBalanceArbitrage = await hre.ethers.getContractFactory("FixedBalanceArbitrage");
    const contract = FixedBalanceArbitrage.attach(contractAddress);
    
    // Check initial state
    console.log("\n📊 INITIAL STATE:");
    const initialResults = await contract.getResults();
    console.log("💰 Last profit:", hre.ethers.utils.formatUnits(initialResults[0], 6), "USDC");
    console.log("✅ Last success:", initialResults[1]);
    console.log("🔢 Total executions:", initialResults[2].toString());
    
    const currentBalance = await contract.getCurrentBalance();
    console.log("💳 Current balance:", hre.ethers.utils.formatUnits(currentBalance, 6), "USDC");
    
    // Execute fixed balance arbitrage
    console.log("\n🚀 EXECUTING FIXED BALANCE ARBITRAGE...");
    console.log("💡 This version fixes the exact balance issue found in debugging");
    
    try {
        const tx = await contract.executeFixedBalanceArbitrage({
            gasLimit: 2000000,
            gasPrice: hre.ethers.utils.parseUnits("50", "gwei")
        });
        
        console.log("📝 Transaction hash:", tx.hash);
        console.log("🔗 Polygonscan:", `https://polygonscan.com/tx/${tx.hash}`);
        
        console.log("⏳ Waiting for confirmation...");
        const receipt = await tx.wait();
        
        console.log("✅ TRANSACTION CONFIRMED!");
        console.log("⛽ Gas used:", receipt.gasUsed.toString());
        console.log("💰 Gas cost:", hre.ethers.utils.formatEther(receipt.gasUsed.mul(tx.gasPrice)), "MATIC");
        
        // Parse events
        console.log("\n📊 EVENTS:");
        for (const log of receipt.logs) {
            try {
                const parsed = contract.interface.parseLog(log);
                console.log(`🎯 ${parsed.name}:`, parsed.args);
            } catch (e) {
                // Skip unparseable logs
            }
        }
        
        // Check final results
        console.log("\n📊 FINAL RESULTS:");
        const finalResults = await contract.getResults();
        console.log("💰 Last profit:", hre.ethers.utils.formatUnits(finalResults[0], 6), "USDC");
        console.log("✅ Last success:", finalResults[1]);
        console.log("🔢 Total executions:", finalResults[2].toString());
        
        const finalBalance = await contract.getCurrentBalance();
        console.log("💳 Final balance:", hre.ethers.utils.formatUnits(finalBalance, 6), "USDC");
        
        if (finalResults[1]) {
            console.log("\n🎉 FIXED BALANCE ARBITRAGE SUCCESS!");
            if (finalResults[0].gt(0)) {
                console.log("💰 PROFIT GENERATED:", hre.ethers.utils.formatUnits(finalResults[0], 6), "USDC");
                console.log("🏦 Profit sent to:", "******************************************");
            } else {
                console.log("💡 No profit this round, but execution successful!");
            }
        } else {
            console.log("\n❌ EXECUTION FAILED - CHECK EVENTS FOR DETAILS");
        }
        
    } catch (error) {
        console.error("❌ EXECUTION FAILED:", error.message);
        
        if (error.message.includes("revert")) {
            console.log("💡 Contract reverted - this is expected behavior for safety");
        }
        
        if (error.message.includes("gas")) {
            console.log("⛽ Gas issue - try increasing gas limit");
        }
    }
    
    console.log("\n🎯 FIXED BALANCE ARBITRAGE TEST COMPLETE!");
}

main()
    .then(() => {
        console.log("✅ TEST COMPLETE");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ TEST FAILED:", error);
        process.exit(1);
    });
