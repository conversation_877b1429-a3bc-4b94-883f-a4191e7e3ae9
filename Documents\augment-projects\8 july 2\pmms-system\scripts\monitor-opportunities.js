const { ethers } = require("hardhat");
require("dotenv").config();

class OpportunityMonitor {
    constructor() {
        this.isRunning = false;
        this.strategies = [];
        this.minProfitThreshold = ethers.utils.parseUnits("50", 6); // $50 minimum profit
        this.checkInterval = 30000; // 30 seconds
    }

    async initialize() {
        console.log("🔍 Initializing Opportunity Monitor...");
        
        const [deployer] = await ethers.getSigners();
        this.signer = deployer;
        
        // Load contract addresses
        this.registryAddress = process.env.REGISTRY_ADDRESS;
        this.pmmsAddress = process.env.PMMS_ADDRESS;
        
        if (!this.registryAddress || !this.pmmsAddress) {
            throw new Error("Please set REGISTRY_ADDRESS and PMMS_ADDRESS in .env");
        }
        
        // Get contract instances
        this.registry = await ethers.getContractAt("Registry", this.registryAddress);
        this.pmms = await ethers.getContractAt("ProfitMaximizerModularSystem", this.pmmsAddress);
        
        // Load strategies
        await this.loadStrategies();
        
        console.log("✅ Monitor initialized successfully");
    }

    async loadStrategies() {
        console.log("📋 Loading strategies...");
        
        const strategyNames = ["DexArbitrage", "TriangularArbitrage", "StablecoinPegArbitrage"];
        
        for (const name of strategyNames) {
            try {
                const address = await this.registry.getStrategy(name);
                if (address !== ethers.constants.AddressZero) {
                    const contract = await ethers.getContractAt(`Strategy${name}`, address);
                    this.strategies.push({
                        name,
                        address,
                        contract
                    });
                    console.log(`✅ Loaded ${name} strategy at ${address}`);
                }
            } catch (error) {
                console.log(`⚠️ Failed to load ${name} strategy:`, error.message);
            }
        }
        
        console.log(`📊 Total strategies loaded: ${this.strategies.length}`);
    }

    async checkOpportunities() {
        console.log("\n🔍 Scanning for opportunities...");
        
        // Test amounts for different tokens
        const testAmounts = [
            { token: "******************************************", amount: ethers.utils.parseUnits("1000", 6), symbol: "USDC" },
            { token: "******************************************", amount: ethers.utils.parseUnits("1", 18), symbol: "WETH" },
            { token: "******************************************", amount: ethers.utils.parseUnits("1000", 18), symbol: "DAI" }
        ];

        const opportunities = [];

        for (const strategy of this.strategies) {
            for (const testAmount of testAmounts) {
                try {
                    const [profit, executionData] = await strategy.contract.checkOpportunity(
                        testAmount.token,
                        testAmount.amount
                    );

                    if (profit.gt(this.minProfitThreshold)) {
                        const opportunity = {
                            strategy: strategy.name,
                            token: testAmount.symbol,
                            tokenAddress: testAmount.token,
                            amount: testAmount.amount,
                            profit: profit,
                            profitUSD: ethers.utils.formatUnits(profit, 6),
                            executionData: executionData,
                            timestamp: new Date().toISOString()
                        };
                        
                        opportunities.push(opportunity);
                        
                        console.log(`💰 OPPORTUNITY FOUND!`);
                        console.log(`   Strategy: ${opportunity.strategy}`);
                        console.log(`   Token: ${opportunity.token}`);
                        console.log(`   Profit: $${opportunity.profitUSD}`);
                        console.log(`   Amount: ${ethers.utils.formatUnits(opportunity.amount, testAmount.symbol === "WETH" ? 18 : 6)} ${opportunity.token}`);
                    }
                } catch (error) {
                    console.log(`⚠️ Error checking ${strategy.name} for ${testAmount.symbol}:`, error.message);
                }
            }
        }

        if (opportunities.length === 0) {
            console.log("ℹ️ No profitable opportunities found at this time");
        } else {
            console.log(`🎯 Found ${opportunities.length} profitable opportunities!`);
            
            // Sort by profit (highest first)
            opportunities.sort((a, b) => b.profit.sub(a.profit));
            
            // Log the best opportunity
            const best = opportunities[0];
            console.log(`\n🏆 BEST OPPORTUNITY:`);
            console.log(`   Strategy: ${best.strategy}`);
            console.log(`   Token: ${best.token}`);
            console.log(`   Profit: $${best.profitUSD}`);
            
            // Auto-execute if profit is high enough (you can adjust this threshold)
            const autoExecuteThreshold = ethers.utils.parseUnits("100", 6); // $100
            
            if (best.profit.gt(autoExecuteThreshold)) {
                console.log(`🚀 Auto-executing best opportunity...`);
                await this.executeOpportunity(best);
            } else {
                console.log(`ℹ️ Profit below auto-execute threshold ($100)`);
            }
        }

        return opportunities;
    }

    async executeOpportunity(opportunity) {
        try {
            console.log(`⚡ Executing ${opportunity.strategy} for ${opportunity.token}...`);
            
            // Estimate gas
            const gasEstimate = await this.pmms.estimateGas.executeStrategy(
                opportunity.strategy,
                opportunity.tokenAddress,
                opportunity.amount,
                opportunity.executionData
            );
            
            console.log(`⛽ Estimated gas: ${gasEstimate.toString()}`);
            
            // Execute the strategy
            const tx = await this.pmms.executeStrategy(
                opportunity.strategy,
                opportunity.tokenAddress,
                opportunity.amount,
                opportunity.executionData,
                {
                    gasLimit: gasEstimate.mul(120).div(100) // 20% buffer
                }
            );
            
            console.log(`📝 Transaction sent: ${tx.hash}`);
            console.log(`⏳ Waiting for confirmation...`);
            
            const receipt = await tx.wait();
            
            if (receipt.status === 1) {
                console.log(`✅ EXECUTION SUCCESSFUL!`);
                console.log(`   Gas used: ${receipt.gasUsed.toString()}`);
                console.log(`   Block: ${receipt.blockNumber}`);
                console.log(`   Profit: $${opportunity.profitUSD}`);
                
                // Log to file for tracking
                this.logExecution(opportunity, receipt);
            } else {
                console.log(`❌ EXECUTION FAILED`);
            }
            
        } catch (error) {
            console.error(`❌ Execution error:`, error.message);
        }
    }

    logExecution(opportunity, receipt) {
        const fs = require('fs');
        const logEntry = {
            timestamp: new Date().toISOString(),
            strategy: opportunity.strategy,
            token: opportunity.token,
            profit: opportunity.profitUSD,
            txHash: receipt.transactionHash,
            gasUsed: receipt.gasUsed.toString(),
            blockNumber: receipt.blockNumber
        };
        
        const logPath = './logs/executions.json';
        
        // Create logs directory if it doesn't exist
        if (!fs.existsSync('./logs')) {
            fs.mkdirSync('./logs');
        }
        
        // Read existing logs or create new array
        let logs = [];
        if (fs.existsSync(logPath)) {
            logs = JSON.parse(fs.readFileSync(logPath, 'utf8'));
        }
        
        logs.push(logEntry);
        fs.writeFileSync(logPath, JSON.stringify(logs, null, 2));
        
        console.log(`📊 Execution logged to ${logPath}`);
    }

    async start() {
        if (this.isRunning) {
            console.log("⚠️ Monitor is already running");
            return;
        }
        
        await this.initialize();
        this.isRunning = true;
        
        console.log(`🚀 Starting opportunity monitor...`);
        console.log(`⏰ Check interval: ${this.checkInterval / 1000} seconds`);
        console.log(`💰 Min profit threshold: $${ethers.utils.formatUnits(this.minProfitThreshold, 6)}`);
        console.log(`🔄 Press Ctrl+C to stop\n`);
        
        // Start monitoring loop
        while (this.isRunning) {
            try {
                await this.checkOpportunities();
                await new Promise(resolve => setTimeout(resolve, this.checkInterval));
            } catch (error) {
                console.error("❌ Monitor error:", error.message);
                await new Promise(resolve => setTimeout(resolve, this.checkInterval));
            }
        }
    }

    stop() {
        console.log("🛑 Stopping opportunity monitor...");
        this.isRunning = false;
    }
}

async function main() {
    const monitor = new OpportunityMonitor();
    
    // Handle graceful shutdown
    process.on('SIGINT', () => {
        monitor.stop();
        process.exit(0);
    });
    
    await monitor.start();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = OpportunityMonitor;
