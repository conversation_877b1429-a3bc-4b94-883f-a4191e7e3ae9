const { ethers, upgrades } = require("hardhat");
require("dotenv").config();

async function main() {
    console.log("🚀 Starting PMMS Deployment on Polygon...");
    
    const [deployer] = await ethers.getSigners();
    console.log("Deploying contracts with account:", deployer.address);
    console.log("Account balance:", (await deployer.getBalance()).toString());

    // Network configuration
    const network = await ethers.provider.getNetwork();
    console.log("Network:", network.name, "Chain ID:", network.chainId);

    // Contract addresses for Polygon Mainnet
    const AAVE_ADDRESS_PROVIDER = "******************************************";
    const UNISWAP_V3_ROUTER = "******************************************";
    const UNISWAP_V3_QUOTER = "******************************************";
    const SUSHISWAP_ROUTER = "******************************************";
    
    // Token addresses
    const WETH = "******************************************";
    const USDC = "******************************************";
    const USDT = "******************************************";
    const DAI = "******************************************";

    try {
        // 1. Deploy Registry
        console.log("\n📋 Deploying Registry...");
        const Registry = await ethers.getContractFactory("contracts/core/Registry.sol:Registry");
        const registry = await upgrades.deployProxy(Registry, [deployer.address], {
            initializer: "initialize"
        });
        await registry.deployed();
        console.log("✅ Registry deployed to:", registry.address);

        // 2. Deploy StrategyExecutor
        console.log("\n🎯 Deploying StrategyExecutor...");
        const StrategyExecutor = await ethers.getContractFactory("contracts/PMMSStrategyExecutor.sol:StrategyExecutor");
        const strategyExecutor = await upgrades.deployProxy(
            StrategyExecutor,
            [registry.address, deployer.address],
            { initializer: "initialize" }
        );
        await strategyExecutor.deployed();
        console.log("✅ StrategyExecutor deployed to:", strategyExecutor.address);

        // 3. Deploy FlashloanExecutor
        console.log("\n⚡ Deploying FlashloanExecutor...");
        const FlashloanExecutor = await ethers.getContractFactory("contracts/PMMSFlashloanExecutor.sol:FlashloanExecutor");
        const flashloanExecutor = await upgrades.deployProxy(
            FlashloanExecutor,
            [AAVE_ADDRESS_PROVIDER, deployer.address, strategyExecutor.address],
            { initializer: "initialize" }
        );
        await flashloanExecutor.deployed();
        console.log("✅ FlashloanExecutor deployed to:", flashloanExecutor.address);

        // 4. Deploy Main PMMS Contract
        console.log("\n🏗️ Deploying ProfitMaximizerModularSystem...");
        const PMMS = await ethers.getContractFactory("ProfitMaximizerModularSystem");
        const pmms = await upgrades.deployProxy(
            PMMS,
            [registry.address, flashloanExecutor.address, deployer.address],
            { initializer: "initialize" }
        );
        await pmms.deployed();
        console.log("✅ PMMS deployed to:", pmms.address);

        // 5. Configure Registry with essential addresses
        console.log("\n⚙️ Configuring Registry...");
        
        await registry.setAddress("AAVE_ADDRESS_PROVIDER", AAVE_ADDRESS_PROVIDER);
        await registry.setAddress("UNISWAP_V3", UNISWAP_V3_ROUTER);
        await registry.setAddress("UNISWAP_V3_QUOTER", UNISWAP_V3_QUOTER);
        await registry.setAddress("SUSHISWAP", SUSHISWAP_ROUTER);
        await registry.setAddress("WETH", WETH);
        await registry.setAddress("USDC", USDC);
        await registry.setAddress("USDT", USDT);
        await registry.setAddress("DAI", DAI);
        
        console.log("✅ Registry configured with DeFi protocol addresses");

        // 6. Deploy and register strategies
        console.log("\n🎲 Deploying Strategies...");

        // Deploy DEX Arbitrage Strategy
        const StrategyDexArbitrage = await ethers.getContractFactory("PMMSStrategyDexArbitrage");
        const dexArbitrage = await upgrades.deployProxy(
            StrategyDexArbitrage,
            [registry.address],
            { initializer: "initialize" }
        );
        await dexArbitrage.deployed();
        console.log("✅ DEX Arbitrage Strategy deployed to:", dexArbitrage.address);

        // Deploy Triangular Arbitrage Strategy
        const StrategyTriangularArbitrage = await ethers.getContractFactory("contracts/strategies/StrategyTriangularArbitrage.sol:StrategyTriangularArbitrage");
        const triangularArbitrage = await upgrades.deployProxy(
            StrategyTriangularArbitrage,
            [registry.address],
            { initializer: "initialize" }
        );
        await triangularArbitrage.deployed();
        console.log("✅ Triangular Arbitrage Strategy deployed to:", triangularArbitrage.address);

        // Deploy Stablecoin Peg Arbitrage Strategy
        const StrategyStablecoinPegArbitrage = await ethers.getContractFactory("contracts/strategies/StrategyStablecoinPegArbitrage.sol:StrategyStablecoinPegArbitrage");
        const stablecoinArbitrage = await upgrades.deployProxy(
            StrategyStablecoinPegArbitrage,
            [registry.address],
            { initializer: "initialize" }
        );
        await stablecoinArbitrage.deployed();
        console.log("✅ Stablecoin Peg Arbitrage Strategy deployed to:", stablecoinArbitrage.address);

        // 7. Register strategies in the registry
        console.log("\n📝 Registering strategies...");
        await registry.registerStrategy("DexArbitrage", dexArbitrage.address);
        await registry.registerStrategy("TriangularArbitrage", triangularArbitrage.address);
        await registry.registerStrategy("StablecoinPegArbitrage", stablecoinArbitrage.address);
        console.log("✅ All strategies registered");

        // 8. Save deployment info
        const deploymentInfo = {
            network: network.name,
            chainId: network.chainId,
            deployer: deployer.address,
            timestamp: new Date().toISOString(),
            contracts: {
                Registry: registry.address,
                StrategyExecutor: strategyExecutor.address,
                FlashloanExecutor: flashloanExecutor.address,
                ProfitMaximizerModularSystem: pmms.address,
                strategies: {
                    DexArbitrage: dexArbitrage.address,
                    TriangularArbitrage: triangularArbitrage.address,
                    StablecoinPegArbitrage: stablecoinArbitrage.address
                }
            }
        };

        // Save to file
        const fs = require('fs');
        const deploymentPath = `./deployments/pmms-polygon-${Date.now()}.json`;
        fs.writeFileSync(deploymentPath, JSON.stringify(deploymentInfo, null, 2));

        console.log("\n🎉 PMMS DEPLOYMENT COMPLETE!");
        console.log("=".repeat(50));
        console.log("📋 Registry:", registry.address);
        console.log("🎯 StrategyExecutor:", strategyExecutor.address);
        console.log("⚡ FlashloanExecutor:", flashloanExecutor.address);
        console.log("🏗️ PMMS Main:", pmms.address);
        console.log("=".repeat(50));
        console.log("💾 Deployment info saved to:", deploymentPath);
        console.log("\n🚀 Ready to execute profitable flash loan strategies!");

    } catch (error) {
        console.error("❌ Deployment failed:", error);
        process.exit(1);
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
