// 🔥 TEST ALCHEMY INTELLIGENCE - TOP 20 POOLS WITH FEES PER $1 INVESTED
import AlchemyIntelligence from './src/alchemy-intelligence.js';
import chalk from 'chalk';

async function testAlchemyIntelligence() {
  console.log(chalk.yellow.bold('🔥 ALCHEMY INTELLIGENCE - TOP 20 POOLS ANALYSIS'));
  console.log(chalk.cyan('💰 Calculating exact fees per $1 invested for ALL Meteora DLMM pools'));
  console.log(chalk.green('🚀 Using your paid Alchemy RPC for unlimited blockchain access'));
  console.log('═'.repeat(80));
  
  const intelligence = new AlchemyIntelligence();
  
  try {
    console.log(chalk.blue('\n🔍 PHASE 1: Comprehensive Pool Analysis'));
    console.log('📊 Scanning ALL Meteora DLMM pools with Alchemy...');
    
    // Get TOP 20 pools with fees per $1 invested
    const top20Pools = await intelligence.getTop20PoolsWithFeesPerDollar();
    
    if (top20Pools.length === 0) {
      console.log(chalk.red('❌ No pools found or analysis failed'));
      console.log(chalk.yellow('💡 Make sure to set your Alchemy API key in src/alchemy-config.js'));
      return;
    }
    
    console.log(chalk.green(`✅ Analysis complete! Found TOP ${top20Pools.length} pools`));
    
    // Display comprehensive results
    console.log(chalk.yellow.bold('\n🏆 TOP 20 POOLS - FEES PER $1 INVESTED RANKING'));
    console.log('═'.repeat(100));
    
    // Create formatted table
    console.log(chalk.cyan('Rank | Pool Name      | Fees per $1  | Daily Return | Annual Return | TVL        | 24h Volume | Risk'));
    console.log('─'.repeat(100));
    
    top20Pools.forEach((pool, index) => {
      const rank = (index + 1).toString().padStart(4);
      const name = pool.poolName.padEnd(14);
      const feesPerDollar = `$${pool.feesPerDollarInvested.toFixed(6)}`.padEnd(12);
      const dailyReturn = `${(pool.dailyReturn * 100).toFixed(3)}%`.padEnd(12);
      const annualReturn = `${(pool.annualizedReturn * 100).toFixed(1)}%`.padEnd(13);
      const tvl = `$${formatNumber(pool.tvlUSD)}`.padEnd(10);
      const volume = `$${formatNumber(pool.volumeUSD24h)}`.padEnd(10);
      const risk = pool.riskScore.padEnd(6);
      
      const color = index < 3 ? chalk.green : index < 10 ? chalk.yellow : chalk.white;
      
      console.log(color(`${rank} | ${name} | ${feesPerDollar} | ${dailyReturn} | ${annualReturn} | ${tvl} | ${volume} | ${risk}`));
    });
    
    // Show detailed analysis for top 5 pools
    console.log(chalk.magenta.bold('\n🔍 DETAILED ANALYSIS - TOP 5 POOLS'));
    console.log('═'.repeat(80));
    
    top20Pools.slice(0, 5).forEach((pool, index) => {
      console.log(chalk.cyan(`\n${index + 1}. ${pool.poolName} (${pool.poolAddress.substring(0, 8)}...)`));
      console.log(chalk.white(`   💰 Fees per $1 Invested: ${chalk.green.bold('$' + pool.feesPerDollarInvested.toFixed(6))}`));
      console.log(chalk.white(`   📈 Daily Return: ${chalk.yellow((pool.dailyReturn * 100).toFixed(3))}%`));
      console.log(chalk.white(`   📊 Annual Return: ${chalk.magenta((pool.annualizedReturn * 100).toFixed(1))}%`));
      console.log(chalk.white(`   💵 TVL: $${formatNumber(pool.tvlUSD)}`));
      console.log(chalk.white(`   📈 24h Volume: $${formatNumber(pool.volumeUSD24h)}`));
      console.log(chalk.white(`   💸 24h Fees: $${formatNumber(pool.fees24hUSD)}`));
      console.log(chalk.white(`   🔄 Transactions (24h): ${pool.transactionCount24h}`));
      console.log(chalk.white(`   👥 Unique Wallets (24h): ${pool.uniqueWallets24h}`));
      console.log(chalk.white(`   ⚠️ Risk Level: ${getRiskColor(pool.riskScore)}`));
      
      // Calculate investment scenarios
      console.log(chalk.gray('   💡 Investment Scenarios:'));
      console.log(chalk.gray(`      $100 → $${(100 + (100 * pool.dailyReturn)).toFixed(2)} daily`));
      console.log(chalk.gray(`      $1000 → $${(1000 + (1000 * pool.dailyReturn)).toFixed(2)} daily`));
      console.log(chalk.gray(`      $10000 → $${(10000 + (10000 * pool.dailyReturn)).toFixed(2)} daily`));
    });
    
    // Show profitability statistics
    console.log(chalk.blue.bold('\n📊 PROFITABILITY STATISTICS'));
    console.log('═'.repeat(50));
    
    const avgFeesPerDollar = top20Pools.reduce((sum, pool) => sum + pool.feesPerDollarInvested, 0) / top20Pools.length;
    const maxFeesPerDollar = Math.max(...top20Pools.map(pool => pool.feesPerDollarInvested));
    const minFeesPerDollar = Math.min(...top20Pools.map(pool => pool.feesPerDollarInvested));
    const totalTVL = top20Pools.reduce((sum, pool) => sum + pool.tvlUSD, 0);
    const totalVolume = top20Pools.reduce((sum, pool) => sum + pool.volumeUSD24h, 0);
    const totalFees = top20Pools.reduce((sum, pool) => sum + pool.fees24hUSD, 0);
    
    console.log(chalk.white(`• Average Fees per $1: $${avgFeesPerDollar.toFixed(6)}`));
    console.log(chalk.white(`• Best Pool Fees per $1: $${maxFeesPerDollar.toFixed(6)}`));
    console.log(chalk.white(`• Lowest Pool Fees per $1: $${minFeesPerDollar.toFixed(6)}`));
    console.log(chalk.white(`• Total TVL (Top 20): $${formatNumber(totalTVL)}`));
    console.log(chalk.white(`• Total 24h Volume: $${formatNumber(totalVolume)}`));
    console.log(chalk.white(`• Total 24h Fees: $${formatNumber(totalFees)}`));
    
    // Show risk distribution
    const riskDistribution = top20Pools.reduce((acc, pool) => {
      acc[pool.riskScore] = (acc[pool.riskScore] || 0) + 1;
      return acc;
    }, {});
    
    console.log(chalk.yellow('\n⚠️ Risk Distribution:'));
    Object.entries(riskDistribution).forEach(([risk, count]) => {
      console.log(chalk.white(`  ${getRiskColor(risk)}: ${count} pools`));
    });
    
    // Show trading recommendations
    console.log(chalk.green.bold('\n🎯 TRADING RECOMMENDATIONS'));
    console.log('═'.repeat(50));
    
    const lowRiskHighReturn = top20Pools.filter(pool => 
      pool.riskScore === 'LOW' && pool.feesPerDollarInvested > avgFeesPerDollar
    );
    
    const mediumRiskHighReturn = top20Pools.filter(pool => 
      pool.riskScore === 'MEDIUM' && pool.feesPerDollarInvested > maxFeesPerDollar * 0.8
    );
    
    console.log(chalk.white(`🟢 LOW RISK, HIGH RETURN: ${lowRiskHighReturn.length} pools`));
    lowRiskHighReturn.slice(0, 3).forEach(pool => {
      console.log(chalk.green(`   • ${pool.poolName}: $${pool.feesPerDollarInvested.toFixed(6)} per $1`));
    });
    
    console.log(chalk.white(`🟡 MEDIUM RISK, HIGH RETURN: ${mediumRiskHighReturn.length} pools`));
    mediumRiskHighReturn.slice(0, 3).forEach(pool => {
      console.log(chalk.yellow(`   • ${pool.poolName}: $${pool.feesPerDollarInvested.toFixed(6)} per $1`));
    });
    
    // Show your $20 strategy
    console.log(chalk.magenta.bold('\n💰 YOUR $20 STRATEGY'));
    console.log('═'.repeat(40));
    
    if (lowRiskHighReturn.length > 0) {
      const bestLowRisk = lowRiskHighReturn[0];
      const dailyProfit = 20 * bestLowRisk.dailyReturn;
      const weeklyProfit = dailyProfit * 7;
      const monthlyProfit = dailyProfit * 30;
      
      console.log(chalk.white(`🎯 Recommended Pool: ${chalk.green.bold(bestLowRisk.poolName)}`));
      console.log(chalk.white(`💵 Investment: $20`));
      console.log(chalk.white(`📈 Daily Profit: $${dailyProfit.toFixed(4)}`));
      console.log(chalk.white(`📊 Weekly Profit: $${weeklyProfit.toFixed(2)}`));
      console.log(chalk.white(`📅 Monthly Profit: $${monthlyProfit.toFixed(2)}`));
      console.log(chalk.white(`🎯 Risk Level: ${getRiskColor(bestLowRisk.riskScore)}`));
    }
    
    console.log(chalk.green.bold('\n✅ ALCHEMY INTELLIGENCE ANALYSIS COMPLETE!'));
    console.log(chalk.cyan('🎯 You now have the exact fees per $1 invested for TOP 20 pools'));
    console.log(chalk.yellow('💰 Ready to start systematic trading with proven data'));
    console.log(chalk.magenta('🚀 Scale from $20 to $10,000 with these insights!'));
    
  } catch (error) {
    console.error(chalk.red('❌ Alchemy intelligence test failed:'), error.message);
    console.error(error.stack);
  }
}

function formatNumber(num) {
  if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
  if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
  return num.toFixed(0);
}

function getRiskColor(risk) {
  switch (risk) {
    case 'LOW': return chalk.green(risk);
    case 'MEDIUM': return chalk.yellow(risk);
    case 'HIGH': return chalk.red(risk);
    default: return chalk.gray(risk);
  }
}

// 🚀 SIMPLE USAGE FUNCTION
export async function getTop20FeesPerDollar() {
  const intelligence = new AlchemyIntelligence();
  const top20 = await intelligence.getTop20PoolsWithFeesPerDollar();
  
  return top20.map(pool => ({
    poolName: pool.poolName,
    feesPerDollarInvested: pool.feesPerDollarInvested,
    dailyReturn: pool.dailyReturn,
    annualReturn: pool.annualizedReturn,
    tvlUSD: pool.tvlUSD,
    riskLevel: pool.riskScore,
    recommendation: pool.riskScore === 'LOW' && pool.feesPerDollarInvested > 0.001 ? 'BUY' : 'MONITOR'
  }));
}

// Run the test
console.log(chalk.magenta.bold('🔥 STARTING ALCHEMY INTELLIGENCE TEST...'));
console.log(chalk.cyan('This will calculate exact fees per $1 invested for ALL Meteora pools!'));
console.log('');

testAlchemyIntelligence().catch(error => {
  console.error(chalk.red('❌ Test failed:'), error.message);
});
