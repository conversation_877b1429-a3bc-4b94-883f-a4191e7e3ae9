// 🍯 HONEY ANALYZER - The brain that finds the money
import { CONFIG, POOL_CATEGORIES } from './config.js';

class HoneyAnalyzer {
  constructor() {
    this.historicalData = new Map();
    this.trendData = new Map();
    this.alertThresholds = new Map();
  }

  // 🎯 ANALYZE POOL FOR HONEY POTENTIAL
  analyzePool(pool, stats = null) {
    try {
      // Handle nested document structure from search API
      const doc = pool.document || pool;

      const analysis = {
        address: doc.id || doc.address || doc.pool_address || doc.pool_mint,
        name: doc.pool_name || doc.name || `${doc.mint_x_symbol || 'TOKEN'}/${doc.mint_y_symbol || 'TOKEN'}`,
        timestamp: Date.now(),

        // Core metrics
        tvl: parseFloat(doc.tvl || doc.liquidity_usd || 0),
        volume24h: parseFloat(doc.volume_24h || 0),
        fees24h: parseFloat(doc.fee_24h || doc.fees_24h || 0),
        
        // Calculate honey metrics
        feeToTvlRatio: this.calculateFeeToTvlRatio(doc),
        volumeVelocity: this.calculateVolumeVelocity(doc),
        feeGenerationRate: this.calculateFeeGenerationRate(doc),
        liquidityEfficiency: this.calculateLiquidityEfficiency(doc),
        volatilityBonus: this.calculateVolatilityBonus(doc),
        
        // Overall scores
        honeyScore: 0,
        category: null,
        profitPotential: 0,
        riskLevel: 'UNKNOWN'
      };

      // Calculate overall honey score
      analysis.honeyScore = this.calculateHoneyScore(analysis);
      
      // Categorize the pool
      analysis.category = this.categorizePool(analysis);
      
      // Calculate profit potential
      analysis.profitPotential = this.calculateProfitPotential(analysis);
      
      // Assess risk level
      analysis.riskLevel = this.assessRiskLevel(analysis);
      
      // Store historical data
      this.storeHistoricalData(analysis);
      
      return analysis;
    } catch (error) {
      console.error('❌ Error analyzing pool:', error.message);
      return null;
    }
  }

  // 💰 CALCULATE FEE TO TVL RATIO - The most important metric
  calculateFeeToTvlRatio(pool) {
    const tvl = parseFloat(pool.tvl || pool.liquidity_usd || 0);
    const fees24h = parseFloat(pool.fee_24h || pool.fees_24h || 0);
    
    if (tvl === 0) return 0;
    
    // Convert to percentage
    return (fees24h / tvl) * 100;
  }

  // 🚀 CALCULATE VOLUME VELOCITY - Trading intensity
  calculateVolumeVelocity(pool) {
    const tvl = parseFloat(pool.tvl || pool.liquidity_usd || 0);
    const volume24h = parseFloat(pool.volume_24h || 0);
    
    if (tvl === 0) return 0;
    
    // Volume turnover ratio
    return volume24h / tvl;
  }

  // ⚡ CALCULATE FEE GENERATION RATE - Fees per hour
  calculateFeeGenerationRate(pool) {
    const fees24h = parseFloat(pool.fee_24h || pool.fees_24h || 0);
    
    // Fees per hour
    return fees24h / 24;
  }

  // 🎯 CALCULATE LIQUIDITY EFFICIENCY - How well liquidity works
  calculateLiquidityEfficiency(pool) {
    const tvl = parseFloat(pool.tvl || pool.liquidity_usd || 0);
    const volume24h = parseFloat(pool.volume_24h || 0);
    const fees24h = parseFloat(pool.fee_24h || pool.fees_24h || 0);
    
    if (tvl === 0 || volume24h === 0) return 0;
    
    // Efficiency = (fees / volume) * (volume / tvl)
    const feeRate = fees24h / volume24h;
    const turnover = volume24h / tvl;
    
    return feeRate * turnover * 1000; // Scale for readability
  }

  // 🔥 CALCULATE VOLATILITY BONUS - Higher volatility = higher fees in DLMM
  calculateVolatilityBonus(pool) {
    // For now, use volume/TVL as volatility proxy
    // In future, we can add price volatility data
    const volumeVelocity = this.calculateVolumeVelocity(pool);
    
    // Higher volume velocity suggests more volatility
    if (volumeVelocity > 5) return 1.5;      // 50% bonus
    if (volumeVelocity > 2) return 1.2;      // 20% bonus
    if (volumeVelocity > 1) return 1.1;      // 10% bonus
    
    return 1.0; // No bonus
  }

  // 🏆 CALCULATE OVERALL HONEY SCORE
  calculateHoneyScore(analysis) {
    const weights = CONFIG.RANKING_WEIGHTS;
    
    // Normalize metrics to 0-100 scale
    const normalizedMetrics = {
      feeToTvl: Math.min(analysis.feeToTvlRatio * 20, 100),        // 5% = 100 points
      volumeVelocity: Math.min(analysis.volumeVelocity * 10, 100), // 10x = 100 points
      feeGenRate: Math.min(analysis.feeGenerationRate / 10, 100),  // $1000/hr = 100 points
      efficiency: Math.min(analysis.liquidityEfficiency * 2, 100), // Scale efficiency
      volatility: (analysis.volatilityBonus - 1) * 100            // Bonus as percentage
    };
    
    // Calculate weighted score
    const score = (
      normalizedMetrics.feeToTvl * weights.FEE_TVL_RATIO +
      normalizedMetrics.volumeVelocity * weights.VOLUME_VELOCITY +
      normalizedMetrics.feeGenRate * weights.FEE_GENERATION_RATE +
      normalizedMetrics.efficiency * weights.LIQUIDITY_EFFICIENCY +
      normalizedMetrics.volatility * weights.VOLATILITY_BONUS
    );
    
    return Math.round(score * 100) / 100; // Round to 2 decimals
  }

  // 🍯 CATEGORIZE POOL - What type of honey is this?
  categorizePool(analysis) {
    const categories = Object.entries(POOL_CATEGORIES);
    
    // Check from highest to lowest category
    for (const [key, category] of categories) {
      const criteria = category.criteria;
      
      if (analysis.feeToTvlRatio >= criteria.feeToTvlRatio &&
          analysis.volume24h >= criteria.volume24h &&
          analysis.feeGenerationRate >= criteria.feesPerHour) {
        return category.name;
      }
    }
    
    return 'NO HONEY 🚫';
  }

  // 💎 CALCULATE PROFIT POTENTIAL - Expected returns
  calculateProfitPotential(analysis) {
    // Base profit on fee generation rate and efficiency
    const basePotential = analysis.feeGenerationRate * analysis.liquidityEfficiency;
    
    // Apply volatility bonus
    const adjustedPotential = basePotential * analysis.volatilityBonus;
    
    // Factor in TVL stability (higher TVL = more stable)
    const stabilityFactor = Math.min(analysis.tvl / 100000, 2); // Cap at 2x
    
    return Math.round(adjustedPotential * stabilityFactor);
  }

  // ⚠️ ASSESS RISK LEVEL
  assessRiskLevel(analysis) {
    const tvl = analysis.tvl;
    const volumeVelocity = analysis.volumeVelocity;
    
    // High volume velocity with low TVL = HIGH RISK
    if (volumeVelocity > 10 && tvl < 50000) return 'HIGH RISK 🔴';
    
    // Very high volume velocity = MEDIUM RISK
    if (volumeVelocity > 5) return 'MEDIUM RISK 🟡';
    
    // Stable metrics = LOW RISK
    if (volumeVelocity < 2 && tvl > 100000) return 'LOW RISK 🟢';
    
    return 'MEDIUM RISK 🟡';
  }

  // 📊 STORE HISTORICAL DATA
  storeHistoricalData(analysis) {
    const address = analysis.address;
    
    if (!this.historicalData.has(address)) {
      this.historicalData.set(address, []);
    }
    
    const history = this.historicalData.get(address);
    history.push({
      timestamp: analysis.timestamp,
      honeyScore: analysis.honeyScore,
      feeToTvlRatio: analysis.feeToTvlRatio,
      volume24h: analysis.volume24h,
      fees24h: analysis.fees24h
    });
    
    // Keep only last 24 hours of data
    const cutoff = Date.now() - (24 * 60 * 60 * 1000);
    this.historicalData.set(address, 
      history.filter(entry => entry.timestamp > cutoff)
    );
  }

  // 📈 ANALYZE TRENDS
  analyzeTrends(address) {
    const history = this.historicalData.get(address);
    if (!history || history.length < 2) return null;
    
    const recent = history.slice(-6); // Last 6 data points
    
    return {
      honeyScoreTrend: this.calculateTrend(recent, 'honeyScore'),
      feeRatioTrend: this.calculateTrend(recent, 'feeToTvlRatio'),
      volumeTrend: this.calculateTrend(recent, 'volume24h'),
      isImproving: this.isPoolImproving(recent)
    };
  }

  // 📊 CALCULATE TREND
  calculateTrend(data, metric) {
    if (data.length < 2) return 0;
    
    const first = data[0][metric];
    const last = data[data.length - 1][metric];
    
    if (first === 0) return 0;
    
    return ((last - first) / first) * 100; // Percentage change
  }

  // 📈 IS POOL IMPROVING?
  isPoolImproving(data) {
    if (data.length < 3) return false;
    
    const recent3 = data.slice(-3);
    let improvements = 0;
    
    for (let i = 1; i < recent3.length; i++) {
      if (recent3[i].honeyScore > recent3[i-1].honeyScore) {
        improvements++;
      }
    }
    
    return improvements >= 2; // At least 2 out of 3 improvements
  }

  // 🚨 CHECK FOR ALERTS
  checkAlerts(analysis) {
    const alerts = [];
    
    // Concentration spike alert
    if (analysis.feeToTvlRatio > 1.0) {
      alerts.push({
        type: 'CONCENTRATION_SPIKE',
        message: `🔥 HIGH CONCENTRATION: ${analysis.feeToTvlRatio.toFixed(2)}% fee/TVL ratio!`,
        priority: 'HIGH'
      });
    }
    
    // New honey pool alert
    if (analysis.category !== 'NO HONEY 🚫' && analysis.honeyScore > 50) {
      alerts.push({
        type: 'NEW_HONEY_POOL',
        message: `🍯 HONEY DETECTED: ${analysis.name} - ${analysis.category}`,
        priority: 'MEDIUM'
      });
    }
    
    // Volume surge alert
    if (analysis.volumeVelocity > 5) {
      alerts.push({
        type: 'VOLUME_SURGE',
        message: `🚀 VOLUME SURGE: ${analysis.volumeVelocity.toFixed(1)}x turnover rate!`,
        priority: 'HIGH'
      });
    }
    
    return alerts;
  }
}

export default HoneyAnalyzer;
