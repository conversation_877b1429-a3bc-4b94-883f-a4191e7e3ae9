/**
 * 🧪 MINIMAL FLASH LOAN TEST
 * Test just the flash loan request without Aave strategy
 */

const { ethers } = require('hardhat');

async function testMinimalFlashLoan() {
    console.log('🧪 MINIMAL FLASH LOAN TEST');
    console.log('🎯 Testing ONLY Balancer flash loan request');
    console.log('⚡ NO Aave strategy - just flash loan callback');
    console.log('=' .repeat(80));
    
    try {
        // Deploy a minimal test contract
        console.log('\n🔧 DEPLOYING MINIMAL TEST CONTRACT...');
        
        const minimalContractCode = `
        // SPDX-License-Identifier: MIT
        pragma solidity ^0.8.19;
        
        interface IERC20 {
            function balanceOf(address account) external view returns (uint256);
            function transfer(address to, uint256 amount) external returns (bool);
        }
        
        interface IBalancerVault {
            function flashLoan(
                address recipient,
                address[] memory tokens,
                uint256[] memory amounts,
                bytes memory userData
            ) external;
        }
        
        interface IFlashLoanRecipient {
            function receiveFlashLoan(
                address[] memory tokens,
                uint256[] memory amounts,
                uint256[] memory feeAmounts,
                bytes memory userData
            ) external;
        }
        
        contract MinimalFlashLoanTest is IFlashLoanRecipient {
            IERC20 public constant USDC = IERC20(0xaf88d065e77c8cC2239327C5EDb3A432268e5831);
            IBalancerVault public constant BALANCER = IBalancerVault(0xBA12222222228d8Ba445958a75a0704d566BF2C8);
            
            event FlashLoanReceived(uint256 amount, uint256 fee);
            event FlashLoanCompleted(bool success);
            
            function testFlashLoan() external {
                address[] memory tokens = new address[](1);
                uint256[] memory amounts = new uint256[](1);
                
                tokens[0] = address(USDC);
                amounts[0] = 1000 * 1e6; // $1,000 USDC
                
                BALANCER.flashLoan(address(this), tokens, amounts, "");
            }
            
            function receiveFlashLoan(
                address[] memory tokens,
                uint256[] memory amounts,
                uint256[] memory feeAmounts,
                bytes memory userData
            ) external override {
                require(msg.sender == address(BALANCER), "Only Balancer");
                
                emit FlashLoanReceived(amounts[0], feeAmounts[0]);
                
                // Just return the borrowed amount (no strategy)
                USDC.transfer(address(BALANCER), amounts[0] + feeAmounts[0]);
                
                emit FlashLoanCompleted(true);
            }
            
            function getBalance() external view returns (uint256) {
                return USDC.balanceOf(address(this));
            }
        }`;
        
        // Save the minimal contract
        const fs = require('fs');
        const path = require('path');
        const contractPath = path.join(__dirname, '..', 'contracts', 'MinimalFlashLoanTest.sol');
        fs.writeFileSync(contractPath, minimalContractCode);
        
        console.log('✅ Minimal contract saved');
        
        // Compile and deploy
        const MinimalFlashLoanTest = await ethers.getContractFactory('MinimalFlashLoanTest');
        const minimalTest = await MinimalFlashLoanTest.deploy();
        await minimalTest.deployed();
        
        console.log(`✅ Minimal contract deployed: ${minimalTest.address}`);
        
        // Test the minimal flash loan
        console.log('\n🧪 TESTING MINIMAL FLASH LOAN...');
        console.log('💰 Amount: $1,000 USDC');
        console.log('🎯 Strategy: None (just return borrowed amount)');
        
        try {
            const tx = await minimalTest.testFlashLoan({
                gasLimit: 1000000
            });
            
            console.log(`📝 TX Hash: ${tx.hash}`);
            console.log('⏳ Waiting for confirmation...');
            
            const receipt = await tx.wait();
            
            if (receipt.status === 1) {
                console.log('🎉 MINIMAL FLASH LOAN SUCCESS!');
                console.log(`⛽ Gas Used: ${receipt.gasUsed.toLocaleString()}`);
                
                // Check events
                if (receipt.logs && receipt.logs.length > 0) {
                    console.log(`📝 Events: ${receipt.logs.length}`);
                    
                    // Try to decode events
                    for (const log of receipt.logs) {
                        try {
                            const decoded = minimalTest.interface.parseLog(log);
                            console.log(`   Event: ${decoded.name}`);
                            if (decoded.name === 'FlashLoanReceived') {
                                console.log(`   Amount: $${ethers.utils.formatUnits(decoded.args.amount, 6)}`);
                                console.log(`   Fee: $${ethers.utils.formatUnits(decoded.args.fee, 6)}`);
                            }
                        } catch (error) {
                            console.log(`   Raw log: ${log.topics[0].substring(0, 10)}...`);
                        }
                    }
                }
                
                console.log('\n✅ CONCLUSION: Balancer flash loans work perfectly!');
                console.log('💡 The issue is in the Aave strategy, not flash loan mechanism');
                
            } else {
                console.log('❌ MINIMAL FLASH LOAN FAILED');
                console.log('💡 Issue is with Balancer flash loan mechanism');
            }
            
        } catch (error) {
            console.log('❌ MINIMAL FLASH LOAN EXECUTION FAILED');
            console.log(`Error: ${error.message}`);
            
            if (error.message.includes('revert')) {
                console.log('💡 Flash loan request was rejected by Balancer');
            } else if (error.message.includes('insufficient')) {
                console.log('💡 Insufficient gas or funds');
            } else {
                console.log('💡 Unknown error - check transaction details');
            }
        }
        
        return {
            minimalContract: minimalTest.address,
            tested: true
        };
        
    } catch (error) {
        console.error('💥 MINIMAL TEST FAILED:', error.message);
        throw error;
    }
}

// Execute test
if (require.main === module) {
    testMinimalFlashLoan()
        .then((result) => {
            console.log('\n🎉 MINIMAL TEST COMPLETED!');
            console.log(`🔧 Minimal Contract: ${result.minimalContract}`);
            console.log('💡 This test isolates the flash loan mechanism from Aave strategy');
        })
        .catch((error) => {
            console.error('Minimal test failed:', error.message);
        });
}

module.exports = { testMinimalFlashLoan };
