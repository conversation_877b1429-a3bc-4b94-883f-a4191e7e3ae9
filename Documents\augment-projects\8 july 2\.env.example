# Alchemy Configuration
ALCHEMY_API_KEY=your_alchemy_api_key_here

# Network RPC URLs - Alchemy
MAINNET_RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_API_KEY
OPTIMISM_RPC_URL=https://opt-mainnet.g.alchemy.com/v2/YOUR_API_KEY
ARBITRUM_RPC_URL=https://arb-mainnet.g.alchemy.com/v2/YOUR_API_KEY
BASE_RPC_URL=https://base-mainnet.g.alchemy.com/v2/YOUR_API_KEY
POLYGON_RPC_URL=https://polygon-mainnet.g.alchemy.com/v2/YOUR_API_KEY

# Testnet RPC URLs - Alchemy
OPTIMISM_TESTNET_RPC_URL=https://opt-sepolia.g.alchemy.com/v2/YOUR_API_KEY
ARBITRUM_TESTNET_RPC_URL=https://arb-sepolia.g.alchemy.com/v2/YOUR_API_KEY
BASE_TESTNET_RPC_URL=https://base-sepolia.g.alchemy.com/v2/YOUR_API_KEY
POLYGON_TESTNET_RPC_URL=https://polygon-mumbai.g.alchemy.com/v2/YOUR_API_KEY

# Private Keys (SECURE - NEVER SHARE THESE!)
PRIVATE_KEY=1ba95f985f787d48bec0f8774861a3d02d569d1caea30b00e8854888cd2440e7

# API Keys
ETHERSCAN_API_KEY=your_etherscan_api_key
OPENAI_API_KEY=********************************************************************************************************************************************************************
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/flashloan_scout
REDIS_URL=redis://localhost:6379
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=your_influxdb_token
INFLUXDB_ORG=your_org
INFLUXDB_BUCKET=flashloan_metrics

# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# Flash Loan Configuration
MIN_PROFIT_THRESHOLD=100  # Minimum profit in USD to consider
MAX_GAS_PRICE=50         # Maximum gas price in gwei
SLIPPAGE_TOLERANCE=0.5   # Maximum slippage tolerance in %

# Monitoring Configuration
BLOCK_CONFIRMATION_COUNT=1
MEMPOOL_MONITORING=true
MEV_PROTECTION=true

# Risk Management
MAX_POSITION_SIZE=10000  # Maximum position size in USD
RISK_TOLERANCE=low       # low, medium, high
STOP_LOSS_PERCENTAGE=5   # Stop loss percentage

# Notification Configuration
DISCORD_WEBHOOK_URL=your_discord_webhook
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Development Configuration
ENABLE_TESTNET=true
SIMULATE_TRANSACTIONS=true
DRY_RUN_MODE=true
