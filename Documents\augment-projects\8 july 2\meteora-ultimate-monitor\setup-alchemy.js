// 🔧 SETUP ALCHEMY API KEY
// Quick setup script to configure your Alchemy RPC

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🔧 ALCHEMY SETUP - Configure your paid RPC access');
console.log('═'.repeat(60));

// Instructions for getting Alchemy API key
console.log('📋 INSTRUCTIONS:');
console.log('1. Go to https://www.alchemy.com/');
console.log('2. Sign up/login to your account');
console.log('3. Create a new Solana Mainnet app');
console.log('4. Copy your API key');
console.log('5. Run this script and paste your API key');
console.log('');

// Read current config
const configPath = path.join(__dirname, 'src', 'alchemy-config.js');
let configContent = fs.readFileSync(configPath, 'utf8');

console.log('🔑 Current API Key Status:');
if (configContent.includes('YOUR_ALCHEMY_API_KEY_HERE')) {
  console.log('❌ API key not configured');
  console.log('');
  console.log('💡 To configure your API key:');
  console.log('1. Open: src/alchemy-config.js');
  console.log('2. Replace "YOUR_ALCHEMY_API_KEY_HERE" with your actual Alchemy API key');
  console.log('3. Save the file');
  console.log('4. Run: node test-alchemy-intelligence.js');
  console.log('');
  console.log('🔗 Your Alchemy RPC URL will be:');
  console.log('   https://solana-mainnet.g.alchemy.com/v2/YOUR_API_KEY');
  console.log('');
  console.log('💰 Benefits of Alchemy RPC:');
  console.log('   ✅ Unlimited getProgramAccounts calls');
  console.log('   ✅ Higher rate limits (100+ requests/second)');
  console.log('   ✅ Better reliability and uptime');
  console.log('   ✅ Advanced analytics and monitoring');
  console.log('   ✅ Access to ALL Meteora DLMM pools');
  console.log('');
  console.log('📊 What you\'ll get:');
  console.log('   🏆 TOP 20 pools ranked by fees per $1 invested');
  console.log('   💰 Exact daily/annual return calculations');
  console.log('   📈 Comprehensive TVL and volume analysis');
  console.log('   ⚠️ Risk assessment for each pool');
  console.log('   🎯 Trading recommendations for your $20 strategy');
} else {
  console.log('✅ API key appears to be configured');
  console.log('');
  console.log('🚀 Ready to run Alchemy intelligence!');
  console.log('   Run: node test-alchemy-intelligence.js');
}

console.log('');
console.log('🔥 EXAMPLE OUTPUT YOU\'LL GET:');
console.log('═'.repeat(60));
console.log('🏆 TOP 20 POOLS - FEES PER $1 INVESTED RANKING');
console.log('Rank | Pool Name      | Fees per $1  | Daily Return | Annual Return');
console.log('   1 | DEGEN-SOL      | $0.012500    | 1.250%       | 456.3%');
console.log('   2 | MEME-SOL       | $0.008750    | 0.875%       | 319.4%');
console.log('   3 | BONK-SOL       | $0.006250    | 0.625%       | 228.1%');
console.log('   ...');
console.log('');
console.log('💰 YOUR $20 STRATEGY:');
console.log('🎯 Recommended Pool: DEGEN-SOL');
console.log('💵 Investment: $20');
console.log('📈 Daily Profit: $0.25');
console.log('📊 Weekly Profit: $1.75');
console.log('📅 Monthly Profit: $7.50');
console.log('');
console.log('🚀 Ready to become the ULTIMATE METEORA WIZARD!');
