/**
 * 🔍 DIRECT CONTRACT CALL TEST
 * Test the exact contract interface
 */

const { ethers } = require('hardhat');

async function testDirectContractCall() {
    console.log('🔍 DIRECT CONTRACT CALL TEST');
    console.log('🎯 New Contract: ******************************************');
    console.log('🔧 Testing exact function calls');
    console.log('=' .repeat(80));
    
    const contractAddress = '******************************************';
    
    try {
        // Method 1: Use raw contract interface
        console.log('\n📋 METHOD 1: RAW CONTRACT INTERFACE');
        const rawContract = new ethers.Contract(
            contractAddress,
            [
                'function getArbitrumInfo() view returns (address usdc, address weth, address balancer, address aave, address oracle)',
                'function AAVE_POOL() view returns (address)',
                'function AAVE_ORACLE() view returns (address)',
                'function USDC() view returns (address)',
                'function WETH() view returns (address)',
                'function BALANCER() view returns (address)'
            ],
            ethers.provider
        );
        
        // Test individual constants
        console.log('🔧 TESTING INDIVIDUAL CONSTANTS:');
        try {
            const usdc = await rawContract.USDC();
            console.log(`   USDC: ${usdc}`);
        } catch (error) {
            console.log(`   USDC: ❌ ${error.message.substring(0, 30)}...`);
        }
        
        try {
            const weth = await rawContract.WETH();
            console.log(`   WETH: ${weth}`);
        } catch (error) {
            console.log(`   WETH: ❌ ${error.message.substring(0, 30)}...`);
        }
        
        try {
            const balancer = await rawContract.BALANCER();
            console.log(`   BALANCER: ${balancer}`);
        } catch (error) {
            console.log(`   BALANCER: ❌ ${error.message.substring(0, 30)}...`);
        }
        
        try {
            const aavePool = await rawContract.AAVE_POOL();
            console.log(`   AAVE_POOL: ${aavePool}`);
        } catch (error) {
            console.log(`   AAVE_POOL: ❌ ${error.message.substring(0, 30)}...`);
        }
        
        try {
            const aaveOracle = await rawContract.AAVE_ORACLE();
            console.log(`   AAVE_ORACLE: ${aaveOracle}`);
        } catch (error) {
            console.log(`   AAVE_ORACLE: ❌ ${error.message.substring(0, 30)}...`);
        }
        
        // Test getArbitrumInfo function
        console.log('\n🔧 TESTING getArbitrumInfo FUNCTION:');
        try {
            const result = await rawContract.getArbitrumInfo();
            console.log(`   Raw Result: ${JSON.stringify(result)}`);
            console.log(`   USDC: ${result.usdc || result[0]}`);
            console.log(`   WETH: ${result.weth || result[1]}`);
            console.log(`   Balancer: ${result.balancer || result[2]}`);
            console.log(`   Aave: ${result.aave || result[3]}`);
            console.log(`   Oracle: ${result.oracle || result[4]}`);
        } catch (error) {
            console.log(`   ❌ getArbitrumInfo failed: ${error.message}`);
        }
        
        // Method 2: Use factory interface
        console.log('\n📋 METHOD 2: FACTORY INTERFACE');
        try {
            const ArbitrumAaveFlashLoan = await ethers.getContractFactory('ArbitrumAaveFlashLoan');
            const contract = ArbitrumAaveFlashLoan.attach(contractAddress);
            
            const arbitrumInfo = await contract.getArbitrumInfo();
            console.log(`   Factory Result: ${JSON.stringify(arbitrumInfo)}`);
            
            // Check if it's an array or object
            if (Array.isArray(arbitrumInfo)) {
                console.log(`   Array[0] (USDC): ${arbitrumInfo[0]}`);
                console.log(`   Array[1] (WETH): ${arbitrumInfo[1]}`);
                console.log(`   Array[2] (Balancer): ${arbitrumInfo[2]}`);
                console.log(`   Array[3] (Aave): ${arbitrumInfo[3]}`);
                console.log(`   Array[4] (Oracle): ${arbitrumInfo[4]}`);
            } else {
                console.log(`   Object.usdc: ${arbitrumInfo.usdc}`);
                console.log(`   Object.weth: ${arbitrumInfo.weth}`);
                console.log(`   Object.balancer: ${arbitrumInfo.balancer}`);
                console.log(`   Object.aave: ${arbitrumInfo.aave}`);
                console.log(`   Object.oracle: ${arbitrumInfo.oracle}`);
            }
            
        } catch (error) {
            console.log(`   ❌ Factory interface failed: ${error.message}`);
        }
        
        // Method 3: Direct execution test
        console.log('\n🚀 METHOD 3: DIRECT EXECUTION TEST');
        try {
            const ArbitrumAaveFlashLoan = await ethers.getContractFactory('ArbitrumAaveFlashLoan');
            const contract = ArbitrumAaveFlashLoan.attach(contractAddress);
            
            // Try to call the execution function with static call
            console.log('   Testing executeArbitrumAaveFlashLoan...');
            
            try {
                await contract.callStatic.executeArbitrumAaveFlashLoan();
                console.log('   ✅ Static call succeeded');
            } catch (error) {
                console.log(`   ⚠️ Static call reverted: ${error.reason || error.message.substring(0, 50)}`);
                
                // Check if it's a specific revert reason
                if (error.message.includes('AAVE_POOL')) {
                    console.log('   🎯 FOUND IT: Error mentions AAVE_POOL!');
                } else if (error.message.includes('undefined')) {
                    console.log('   🎯 FOUND IT: Error mentions undefined!');
                } else {
                    console.log('   💡 Different error - might be market conditions');
                }
            }
            
        } catch (error) {
            console.log(`   ❌ Execution test failed: ${error.message}`);
        }
        
        console.log('\n🎯 DIRECT TEST SUMMARY:');
        console.log('🔧 Testing completed - check results above');
        console.log('💡 If AAVE_POOL shows valid address, the issue is in JavaScript interface');
        console.log('💡 If AAVE_POOL shows 0x0, the issue is in contract deployment');
        
        return { tested: true, contractAddress };
        
    } catch (error) {
        console.error('💥 DIRECT TEST FAILED:', error.message);
        throw error;
    }
}

// Execute test
if (require.main === module) {
    testDirectContractCall()
        .then((result) => {
            console.log('\n🎉 DIRECT TEST COMPLETED!');
            console.log('🔧 Check the results above for the exact issue');
        })
        .catch((error) => {
            console.error('Direct test failed:', error.message);
        });
}

module.exports = { testDirectContractCall };
