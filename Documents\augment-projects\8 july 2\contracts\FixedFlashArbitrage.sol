// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@aave/core-v3/contracts/interfaces/IPool.sol";
import "@aave/core-v3/contracts/flashloan/interfaces/IFlashLoanReceiver.sol";
import "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol";

interface IUniswapV2Router {
    function getAmountsOut(uint amountIn, address[] calldata path)
        external view returns (uint[] memory amounts);
    
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);
}

/**
 * @title FixedFlashArbitrage
 * @dev FIXED flash loan arbitrage - REAL PROFITS!
 */
contract FixedFlashArbitrage is IFlashLoanReceiver {
    
    IPool public constant POOL = IPool(******************************************);
    
    // Polygon addresses
    address public constant WETH = ******************************************;
    address public constant WMATIC = ******************************************;
    address public constant QUICKSWAP = ******************************************;
    address public constant SUSHISWAP = ******************************************;
    address public constant PROFIT_WALLET = ******************************************;
    
    event ArbitrageExecuted(uint256 profit, uint256 gasUsed, string strategy);
    
    /**
     * @dev Check which direction is profitable
     */
    function checkArbitrageDirection(uint256 wethAmount) external view returns (
        bool profitable,
        uint256 estimatedProfit,
        bool useQuickSwapFirst
    ) {
        // Path: WETH → WMATIC
        address[] memory pathToWMATIC = new address[](2);
        pathToWMATIC[0] = WETH;
        pathToWMATIC[1] = WMATIC;
        
        // Path: WMATIC → WETH
        address[] memory pathToWETH = new address[](2);
        pathToWETH[0] = WMATIC;
        pathToWETH[1] = WETH;
        
        // Strategy 1: WETH → WMATIC on QuickSwap, WMATIC → WETH on SushiSwap
        try IUniswapV2Router(QUICKSWAP).getAmountsOut(wethAmount, pathToWMATIC) returns (uint[] memory amounts1) {
            uint256 wmaticFromQuickSwap = amounts1[1];
            
            try IUniswapV2Router(SUSHISWAP).getAmountsOut(wmaticFromQuickSwap, pathToWETH) returns (uint[] memory amounts2) {
                uint256 wethFromSushiSwap = amounts2[1];
                
                if (wethFromSushiSwap > wethAmount) {
                    uint256 profit1 = wethFromSushiSwap - wethAmount;
                    
                    // Strategy 2: WETH → WMATIC on SushiSwap, WMATIC → WETH on QuickSwap
                    try IUniswapV2Router(SUSHISWAP).getAmountsOut(wethAmount, pathToWMATIC) returns (uint[] memory amounts3) {
                        uint256 wmaticFromSushiSwap = amounts3[1];
                        
                        try IUniswapV2Router(QUICKSWAP).getAmountsOut(wmaticFromSushiSwap, pathToWETH) returns (uint[] memory amounts4) {
                            uint256 wethFromQuickSwap = amounts4[1];
                            
                            if (wethFromQuickSwap > wethAmount) {
                                uint256 profit2 = wethFromQuickSwap - wethAmount;
                                
                                // Choose better strategy
                                if (profit1 > profit2) {
                                    return (true, profit1, true); // QuickSwap first
                                } else {
                                    return (true, profit2, false); // SushiSwap first
                                }
                            } else {
                                return (true, profit1, true); // Only strategy 1 profitable
                            }
                        } catch {
                            return (true, profit1, true);
                        }
                    } catch {
                        return (true, profit1, true);
                    }
                }
            } catch {}
        } catch {}
        
        // Strategy 2 only
        try IUniswapV2Router(SUSHISWAP).getAmountsOut(wethAmount, pathToWMATIC) returns (uint[] memory amounts3) {
            uint256 wmaticFromSushiSwap = amounts3[1];
            
            try IUniswapV2Router(QUICKSWAP).getAmountsOut(wmaticFromSushiSwap, pathToWETH) returns (uint[] memory amounts4) {
                uint256 wethFromQuickSwap = amounts4[1];
                
                if (wethFromQuickSwap > wethAmount) {
                    uint256 profit2 = wethFromQuickSwap - wethAmount;
                    return (true, profit2, false); // SushiSwap first
                }
            } catch {}
        } catch {}
        
        return (false, 0, false);
    }
    
    /**
     * @dev Execute flash loan arbitrage with dynamic direction
     */
    function executeFlashArbitrage(uint256 wethAmount) external {
        // Check profitability first
        (bool profitable, uint256 estimatedProfit, bool useQuickSwapFirst) = this.checkArbitrageDirection(wethAmount);
        require(profitable, "No profitable arbitrage found");
        require(estimatedProfit > wethAmount / 1000, "Profit too small"); // 0.1% minimum
        
        address[] memory assets = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        uint256[] memory modes = new uint256[](1);
        
        assets[0] = WETH;
        amounts[0] = wethAmount;
        modes[0] = 0; // No debt
        
        // Encode strategy in params
        bytes memory params = abi.encode(useQuickSwapFirst);
        
        POOL.flashLoan(
            address(this),
            assets,
            amounts,
            modes,
            address(this),
            params,
            0
        );
    }
    
    /**
     * @dev Flash loan callback - DYNAMIC ARBITRAGE!
     */
    function executeOperation(
        address[] calldata assets,
        uint256[] calldata amounts,
        uint256[] calldata premiums,
        address initiator,
        bytes calldata params
    ) external override returns (bool) {
        require(msg.sender == address(POOL), "Invalid caller");
        
        uint256 gasStart = gasleft();
        uint256 wethAmount = amounts[0];
        
        // Decode strategy
        bool useQuickSwapFirst = abi.decode(params, (bool));
        
        address[] memory pathToWMATIC = new address[](2);
        pathToWMATIC[0] = WETH;
        pathToWMATIC[1] = WMATIC;
        
        address[] memory pathToWETH = new address[](2);
        pathToWETH[0] = WMATIC;
        pathToWETH[1] = WETH;
        
        uint256 finalWethAmount;
        string memory strategy;
        
        if (useQuickSwapFirst) {
            // Strategy 1: WETH → WMATIC on QuickSwap, WMATIC → WETH on SushiSwap
            strategy = "QuickSwap->SushiSwap";
            
            // Step 1: WETH → WMATIC on QuickSwap
            IERC20(WETH).approve(QUICKSWAP, wethAmount);
            uint[] memory amounts1 = IUniswapV2Router(QUICKSWAP).swapExactTokensForTokens(
                wethAmount,
                0, // Accept any amount
                pathToWMATIC,
                address(this),
                block.timestamp + 300
            );
            uint256 wmaticAmount = amounts1[1];
            
            // Step 2: WMATIC → WETH on SushiSwap
            IERC20(WMATIC).approve(SUSHISWAP, wmaticAmount);
            uint[] memory amounts2 = IUniswapV2Router(SUSHISWAP).swapExactTokensForTokens(
                wmaticAmount,
                0, // Accept any amount
                pathToWETH,
                address(this),
                block.timestamp + 300
            );
            finalWethAmount = amounts2[1];
            
        } else {
            // Strategy 2: WETH → WMATIC on SushiSwap, WMATIC → WETH on QuickSwap
            strategy = "SushiSwap->QuickSwap";
            
            // Step 1: WETH → WMATIC on SushiSwap
            IERC20(WETH).approve(SUSHISWAP, wethAmount);
            uint[] memory amounts1 = IUniswapV2Router(SUSHISWAP).swapExactTokensForTokens(
                wethAmount,
                0, // Accept any amount
                pathToWMATIC,
                address(this),
                block.timestamp + 300
            );
            uint256 wmaticAmount = amounts1[1];
            
            // Step 2: WMATIC → WETH on QuickSwap
            IERC20(WMATIC).approve(QUICKSWAP, wmaticAmount);
            uint[] memory amounts2 = IUniswapV2Router(QUICKSWAP).swapExactTokensForTokens(
                wmaticAmount,
                0, // Accept any amount
                pathToWETH,
                address(this),
                block.timestamp + 300
            );
            finalWethAmount = amounts2[1];
        }
        
        // Calculate profit
        uint256 totalDebt = wethAmount + premiums[0];
        require(finalWethAmount > totalDebt, "Arbitrage not profitable");
        
        // Repay flash loan
        IERC20(WETH).approve(address(POOL), totalDebt);
        
        // Extract profit
        uint256 profit = finalWethAmount - totalDebt;
        IERC20(WETH).transfer(PROFIT_WALLET, profit);
        
        uint256 gasUsed = gasStart - gasleft();
        emit ArbitrageExecuted(profit, gasUsed, strategy);
        
        return true;
    }
    
    function ADDRESSES_PROVIDER() external pure returns (IPoolAddressesProvider) {
        return IPoolAddressesProvider(******************************************);
    }
}
