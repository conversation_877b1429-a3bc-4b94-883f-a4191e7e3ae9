// 🧪 TEST CONNECTION TO SOLANA AND METEORA
console.log('🧪 Testing Solana connection and Meteora access...');

async function testConnection() {
  try {
    // Test dynamic import
    const { Connection, PublicKey } = await import('@solana/web3.js');
    console.log('✅ Solana web3.js imported successfully');
    
    // Test connection
    const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
    console.log('✅ Connection to Solana mainnet created');
    
    // Test basic RPC call
    const slot = await connection.getSlot();
    console.log(`✅ Current slot: ${slot}`);
    
    // Test Meteora program access
    const METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
    console.log(`✅ Meteora DLMM Program: ${METEORA_DLMM_PROGRAM.toString()}`);
    
    // Test getting program accounts (limited for testing)
    console.log('🔍 Testing program account access...');
    const accounts = await connection.getProgramAccounts(METEORA_DLMM_PROGRAM, {
      dataSlice: { offset: 0, length: 0 },
      limit: 5
    });
    
    console.log(`✅ Found ${accounts.length} Meteora DLMM accounts`);
    
    if (accounts.length > 0) {
      console.log('📋 Sample account addresses:');
      accounts.forEach((account, index) => {
        console.log(`   ${index + 1}. ${account.pubkey.toString()}`);
      });
    }
    
    console.log('\n🎉 CONNECTION TEST SUCCESSFUL!');
    console.log('✅ Ready to build ultimate blockchain intelligence system');
    
  } catch (error) {
    console.error('❌ Connection test failed:', error.message);
  }
}

testConnection();
