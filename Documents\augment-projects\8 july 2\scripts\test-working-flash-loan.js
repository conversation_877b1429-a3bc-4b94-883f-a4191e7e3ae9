const { ethers } = require('hardhat');

/**
 * 🚀 TEST WORKING FLASH LOAN
 * Test with proper IFlashLoanRecipient interface
 */

async function testWorkingFlashLoan() {
  console.log('\n🚀 TESTING WORKING FLASH LOAN');
  console.log('💰 PROPER IFLASHLOANRECIPIENT INTERFACE');
  console.log('⚡ THIS SHOULD FINALLY WORK!');
  console.log('🎯 MOMENT OF TRUTH!');
  console.log('=' .repeat(80));

  try {
    const [deployer] = await ethers.getSigners();
    const currentGasPrice = await ethers.provider.getGasPrice();
    const highGasPrice = currentGasPrice.mul(200).div(100);
    
    console.log(`👤 Deployer: ${deployer.address}`);
    console.log(`💰 Balance: ${ethers.utils.formatEther(await deployer.getBalance())} POL`);

    console.log('\n🔥 DEPLOYING WORKING FLASH LOAN CONTRACT...');
    
    const WorkingFlashLoan = await ethers.getContractFactory('WorkingFlashLoan');
    const working = await WorkingFlashLoan.deploy({
      gasLimit: 1500000,
      gasPrice: highGasPrice
    });

    await working.deployed();
    console.log(`✅ Contract deployed: ${working.address}`);

    // Check profit wallet before
    const usdcContract = new ethers.Contract(
      await working.USDC(),
      ['function balanceOf(address) view returns (uint256)'],
      deployer
    );
    
    const profitBefore = await usdcContract.balanceOf(await working.PROFIT_WALLET());
    console.log(`💰 Profit Wallet Before: ${ethers.utils.formatUnits(profitBefore, 6)} USDC`);

    console.log('\n📊 WORKING FLASH LOAN PARAMETERS:');
    console.log(`   Flash Amount: ${ethers.utils.formatUnits(await working.FLASH_AMOUNT(), 6)} USDC`);
    console.log(`   Balancer Vault: ${await working.BALANCER()}`);
    console.log(`   USDC Token: ${await working.USDC()}`);
    console.log(`   Profit Wallet: ${await working.PROFIT_WALLET()}`);

    console.log('\n🚀 EXECUTING WORKING FLASH LOAN...');
    console.log('💰 This should receive $1K USDC and repay it');
    console.log('⚡ Testing the flash loan mechanism itself');
    
    const executionTx = await working.executeWorkingFlashLoan({
      gasLimit: 1500000,
      gasPrice: highGasPrice
    });
    
    console.log(`📋 Execution TX: ${executionTx.hash}`);
    const execReceipt = await executionTx.wait();
    
    console.log(`\n📊 EXECUTION RESULTS:`);
    console.log(`   Status: ${execReceipt.status === 1 ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`   Gas Used: ${execReceipt.gasUsed.toLocaleString()}`);
    console.log(`   Block: ${execReceipt.blockNumber}`);
    
    // Parse detailed events
    console.log('\n🔍 DETAILED EXECUTION STEPS:');
    for (const log of execReceipt.logs) {
      try {
        const event = working.interface.parseLog(log);
        if (event.name === 'DebugStep') {
          console.log(`   📊 ${event.args.step}: ${event.args.value.toString()}`);
        } else if (event.name === 'FlashLoanResult') {
          console.log(`   🚀 Flash Loan Result: Profit=${ethers.utils.formatUnits(event.args.profit, 6)}, Success=${event.args.success}`);
        }
      } catch {
        // Skip unparseable logs
      }
    }
    
    const results = await working.getResults();
    const profitAfter = await usdcContract.balanceOf(await working.PROFIT_WALLET());
    const actualProfit = profitAfter.sub(profitBefore);
    
    console.log(`\n💰 FINAL RESULTS:`);
    console.log(`   Contract Profit: ${ethers.utils.formatUnits(results.profit, 6)} USDC`);
    console.log(`   Wallet Profit: ${ethers.utils.formatUnits(actualProfit, 6)} USDC`);
    console.log(`   Success: ${results.success ? '✅' : '❌'}`);
    
    if (execReceipt.status === 1) {
      console.log('\n🎉🎉🎉 FLASH LOAN MECHANISM WORKS! 🎉🎉🎉');
      console.log('💰 BALANCER FLASH LOAN CONFIRMED WORKING!');
      console.log('🔥 ZERO UPFRONT CAPITAL VALIDATED!');
      console.log('⚡ PROPER INTERFACE IMPLEMENTATION!');
      
      if (results.success) {
        console.log('\n✅ FLASH LOAN CALLBACK SUCCESSFUL!');
        console.log('🎯 Ready to add profitable strategy inside!');
      } else {
        console.log('\n🔧 Flash loan works but strategy needs optimization');
      }
      
      console.log('\n🚀 IMMEDIATE NEXT STEPS:');
      console.log('1. ✅ Flash loan mechanism validated');
      console.log('2. 🔧 Add profitable strategy (DEX arbitrage or lending)');
      console.log('3. 💰 Scale to larger amounts');
      console.log('4. 🚀 Build automated execution system');
      
      return {
        success: true,
        flashLoanWorks: true,
        address: working.address
      };
      
    } else {
      console.log('\n🔧 TRANSACTION FAILED - CHECKING DETAILS');
      console.log('💡 CHECK DEBUG STEPS ABOVE FOR FAILURE POINT');
      
      return {
        success: false,
        address: working.address
      };
    }
    
  } catch (error) {
    console.error('\n💥 WORKING FLASH LOAN TEST FAILED:', error.message);
    
    if (error.reason) {
      console.log(`💥 Revert Reason: ${error.reason}`);
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

// Execute test
if (require.main === module) {
  testWorkingFlashLoan()
    .then((result) => {
      console.log('\n🎉 WORKING FLASH LOAN TEST COMPLETED!');
      if (result.success) {
        console.log('💰 FLASH LOAN MECHANISM WORKS!');
        console.log(`📍 Contract: ${result.address}`);
        console.log('🚀 Ready to add profitable strategy!');
      } else {
        console.log('🔧 Found specific issue to fix');
      }
    })
    .catch((error) => {
      console.error('Test failed:', error.message);
    });
}

module.exports = { testWorkingFlashLoan };
