// scripts/updateOracleHub.js
const { ethers } = require('hardhat');

async function main() {
  const bandFeedAddress = '******************************************'; // From deployBandFeed.js
  const oracleHub = new ethers.Contract(
    '******************************************',
    ['function setOracle(string memory symbol, address source, uint8 decimals, bool isCustom, bool isInverse) external'],
    await ethers.getSigner('******************************************')
  );
  const symbols = ['XAU', 'USD', 'BTC', 'ETH', 'XOF', 'ZAR'];
  for (const symbol of symbols) {
    const tx = await oracleHub.setOracle(symbol, bandFeedAddress, 18, false, false);
    await tx.wait();
    console.log(`Set ${symbol} to ${bandFeedAddress}`);
  }
}

main().catch(error => {
  console.error(error);
  process.exitCode = 1;
});