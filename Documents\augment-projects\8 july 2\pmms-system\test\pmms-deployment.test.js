const { expect } = require("chai");
const { ethers, upgrades } = require("hardhat");

describe("PMMS Deployment Test", function () {
  let Registry, registry;
  let StrategyExecutor, strategyExecutor;
  let FlashloanExecutor, flashloanExecutor;
  let PMMS, pmms;
  let owner, user;
  
  // Mock addresses for testing
  const MOCK_AAVE_PROVIDER = "******************************************";
  const MOCK_UNISWAP_V3 = "******************************************";
  const MOCK_USDC = "******************************************";
  
  before(async function () {
    [owner, user] = await ethers.getSigners();
    
    // Get contract factories with fully qualified names to avoid conflicts
    Registry = await ethers.getContractFactory("contracts/core/Registry.sol:Registry");
    StrategyExecutor = await ethers.getContractFactory("contracts/core/StrategyExecutor.sol:StrategyExecutor");
    FlashloanExecutor = await ethers.getContractFactory("contracts/core/FlashloanExecutor.sol:FlashloanExecutor");
    PMMS = await ethers.getContractFactory("contracts/ProfitMaximizerModularSystem.sol:ProfitMaximizerModularSystem");
  });
  
  it("Should deploy Registry contract", async function () {
    registry = await upgrades.deployProxy(Registry, [owner.address], {
      initializer: "initialize"
    });
    await registry.deployed();
    
    expect(await registry.owner()).to.equal(owner.address);
  });
  
  it("Should deploy StrategyExecutor contract", async function () {
    strategyExecutor = await upgrades.deployProxy(
      StrategyExecutor, 
      [registry.address, owner.address], 
      { initializer: "initialize" }
    );
    await strategyExecutor.deployed();
    
    expect(await strategyExecutor.owner()).to.equal(owner.address);
    expect(await strategyExecutor.registry()).to.equal(registry.address);
  });
  
  it("Should deploy FlashloanExecutor contract", async function () {
    flashloanExecutor = await upgrades.deployProxy(
      FlashloanExecutor,
      [MOCK_AAVE_PROVIDER, owner.address, strategyExecutor.address],
      { initializer: "initialize" }
    );
    await flashloanExecutor.deployed();
    
    expect(await flashloanExecutor.owner()).to.equal(owner.address);
  });
  
  it("Should deploy PMMS contract", async function () {
    pmms = await upgrades.deployProxy(
      PMMS,
      [registry.address, flashloanExecutor.address, owner.address],
      { initializer: "initialize" }
    );
    await pmms.deployed();
    
    expect(await pmms.owner()).to.equal(owner.address);
    expect(await pmms.registry()).to.equal(registry.address);
    expect(await pmms.flashloanExecutor()).to.equal(flashloanExecutor.address);
  });
  
  it("Should configure Registry with essential addresses", async function () {
    await registry.setAddress("AAVE_ADDRESS_PROVIDER", MOCK_AAVE_PROVIDER);
    await registry.setAddress("UNISWAP_V3", MOCK_UNISWAP_V3);
    await registry.setAddress("USDC", MOCK_USDC);
    
    expect(await registry.getAddress("AAVE_ADDRESS_PROVIDER")).to.equal(MOCK_AAVE_PROVIDER);
    expect(await registry.getAddress("UNISWAP_V3")).to.equal(MOCK_UNISWAP_V3);
    expect(await registry.getAddress("USDC")).to.equal(MOCK_USDC);
  });
  
  it("Should deploy and register a strategy", async function () {
    // Deploy a mock strategy
    const StrategyDexArbitrage = await ethers.getContractFactory("contracts/strategies/StrategyDexArbitrage.sol:StrategyDexArbitrage");
    const dexArbitrage = await upgrades.deployProxy(
      StrategyDexArbitrage,
      [registry.address],
      { initializer: "initialize" }
    );
    await dexArbitrage.deployed();
    
    // Register the strategy
    await registry.registerStrategy("DexArbitrage", dexArbitrage.address);
    
    // Verify registration
    expect(await registry.getStrategy("DexArbitrage")).to.equal(dexArbitrage.address);
  });
  
  it("Should not allow non-owners to register strategies", async function () {
    // Try to register a strategy as non-owner
    await expect(
      registry.connect(user).registerStrategy("UnauthorizedStrategy", user.address)
    ).to.be.revertedWith("Ownable: caller is not the owner");
  });
  
  it("Should not allow non-owners to execute strategies", async function () {
    // Try to execute a strategy as non-owner
    await expect(
      pmms.connect(user).executeStrategy("DexArbitrage", MOCK_USDC, 1000, "0x")
    ).to.be.revertedWith("Ownable: caller is not the owner");
  });
});
