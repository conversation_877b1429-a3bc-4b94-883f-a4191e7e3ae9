
        // SPDX-License-Identifier: MIT
        pragma solidity ^0.8.19;
        
        interface IERC20 {
            function balanceOf(address account) external view returns (uint256);
            function transfer(address to, uint256 amount) external returns (bool);
        }
        
        interface IBalancerVault {
            function flashLoan(
                address recipient,
                address[] memory tokens,
                uint256[] memory amounts,
                bytes memory userData
            ) external;
        }
        
        interface IFlashLoanRecipient {
            function receiveFlashLoan(
                address[] memory tokens,
                uint256[] memory amounts,
                uint256[] memory feeAmounts,
                bytes memory userData
            ) external;
        }
        
        contract MinimalFlashLoanTest is IFlashLoanRecipient {
            IERC20 public constant USDC = IERC20(0xaf88d065e77c8cC2239327C5EDb3A432268e5831);
            IBalancerVault public constant BALANCER = IBalancerVault(0xBA12222222228d8Ba445958a75a0704d566BF2C8);
            
            event FlashLoanReceived(uint256 amount, uint256 fee);
            event FlashLoanCompleted(bool success);
            
            function testFlashLoan() external {
                address[] memory tokens = new address[](1);
                uint256[] memory amounts = new uint256[](1);
                
                tokens[0] = address(USDC);
                amounts[0] = 1000 * 1e6; // $1,000 USDC
                
                BALANCER.flashLoan(address(this), tokens, amounts, "");
            }
            
            function receiveFlashLoan(
                address[] memory tokens,
                uint256[] memory amounts,
                uint256[] memory feeAmounts,
                bytes memory userData
            ) external override {
                require(msg.sender == address(BALANCER), "Only Balancer");
                
                emit FlashLoanReceived(amounts[0], feeAmounts[0]);
                
                // Just return the borrowed amount (no strategy)
                USDC.transfer(address(BALANCER), amounts[0] + feeAmounts[0]);
                
                emit FlashLoanCompleted(true);
            }
            
            function getBalance() external view returns (uint256) {
                return USDC.balanceOf(address(this));
            }
        }
