name: Deploy Frontend to GitHub Pages

on:
  push:
    branches:
      - main # Trigger workflow on pushes to the main branch
  workflow_dispatch: # Allows manual triggering from the GitHub Actions tab

permissions:
  contents: read      # <PERSON><PERSON> read access to the repository content
  pages: write        # <PERSON>s write access to GitHub Pages for deployment
  id-token: write     # <PERSON>s write access to the id-token for authentication (OIDC)

jobs:
  build:
    name: Build Frontend
    runs-on: ubuntu-latest # Execute on the latest Ubuntu runner
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4 # Checkout the repository code

      - name: Setup Node.js
        uses: actions/setup-node@v4 # Set up Node.js environment
        with:
          node-version: '20' # Or your preferred Node.js version (e.g., '18', '20')
          cache: 'npm' # Cache npm dependencies

      - name: Install dependencies
        # The working-directory ensures npm commands run in the 'frontend' directory
        run: npm ci
        working-directory: ./frontend

      - name: Build project
        # Execute the Vite build command for the frontend application
        run: npm run build
        working-directory: ./frontend

      - name: Upload production-ready build files
        # Upload the built 'dist' folder from the frontend directory as an artifact
        uses: actions/upload-artifact@v4
        with:
          name: production-files
          path: ./frontend/dist # Path to your Vite build output directory within 'frontend'

  deploy:
    name: Deploy to GitHub Pages
    needs: build # This job depends on the successful completion of the 'build' job
    runs-on: ubuntu-latest # Execute on the latest Ubuntu runner
    # Only deploy if the push was to the main branch and the build job succeeded
    if: github.ref == 'refs/heads/main' && needs.build.result == 'success'
    steps:
      - name: Download artifact
        # Download the 'production-files' artifact from the 'build' job
        uses: actions/download-artifact@v4
        with:
          name: production-files
          # Download it to the current working directory, which will be the root for peaceiris action
          path: ./dist 

      - name: Deploy to GitHub Pages
        # Use the peaceiris action for deployment
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }} # Automatically provided GitHub token for authentication
          publish_dir: ./dist # Specify the directory to publish to GitHub Pages (this is the downloaded artifact)
          # The cname option is useful if you use a custom domain.
          # If you don't have a CNAME file and are using a custom domain, add:
          # cname: 'your-custom-domain.com'
          # If you are not using a custom domain and rely on the repo name in the URL, you might not need a CNAME file.
