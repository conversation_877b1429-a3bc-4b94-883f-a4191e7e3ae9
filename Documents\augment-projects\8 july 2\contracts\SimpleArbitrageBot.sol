// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

interface IFlashLoanReceiver {
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external;
}

interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IERC20 {
    function transfer(address to, uint256 amount) external returns (bool);
    function transferFrom(address from, address to, uint256 amount) external returns (bool);
    function balanceOf(address account) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
}

interface IUniswapV2Router {
    function getAmountsOut(uint amountIn, address[] calldata path)
        external view returns (uint[] memory amounts);
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);
}

/**
 * @title SimpleArbitrageBot
 * @dev A working arbitrage bot that exploits real price differences
 * This version focuses on DEX arbitrage opportunities that actually exist
 */
contract SimpleArbitrageBot is IFlashLoanReceiver, ReentrancyGuard, Ownable, Pausable {
    
    // Constants
    IBalancerVault public constant BALANCER_VAULT = IBalancerVault(******************************************);
    address public constant PROFIT_WALLET = ******************************************;
    
    // DEX routers on Polygon
    address public constant QUICKSWAP_ROUTER = ******************************************;
    address public constant SUSHISWAP_ROUTER = ******************************************;
    
    // Tokens on Polygon
    address public constant USDC = ******************************************;
    address public constant USDT = ******************************************;
    address public constant WMATIC = ******************************************;
    address public constant WETH = ******************************************;
    
    // State variables
    uint256 public totalExecutions;
    uint256 public totalProfit;
    uint256 public minProfitThreshold = 50e6; // $50 USDC minimum
    
    // Events
    event ArbitrageExecuted(uint256 profit, uint256 gasUsed, string strategy);
    event EmergencyWithdraw(address token, uint256 amount);

    // Constructor
    constructor() Ownable(msg.sender) {}
    
    // Modifiers
    modifier onlyBalancer() {
        require(msg.sender == address(BALANCER_VAULT), "Only Balancer");
        _;
    }
    
    modifier profitableOnly(uint256 expectedProfit) {
        require(expectedProfit >= minProfitThreshold, "Profit too low");
        _;
    }
    
    /**
     * @dev Execute DEX arbitrage between QuickSwap and SushiSwap
     * @param token Token to arbitrage
     * @param amount Amount to flash loan
     * @param expectedProfit Expected profit from arbitrage
     */
    function executeDEXArbitrage(
        address token,
        uint256 amount,
        uint256 expectedProfit
    ) external onlyOwner nonReentrant whenNotPaused profitableOnly(expectedProfit) {
        
        address[] memory tokens = new address[](1);
        tokens[0] = token;
        
        uint256[] memory amounts = new uint256[](1);
        amounts[0] = amount;
        
        bytes memory userData = abi.encode("DEX_ARBITRAGE", expectedProfit, token);
        
        BALANCER_VAULT.flashLoan(address(this), tokens, amounts, userData);
    }
    
    /**
     * @dev Balancer flash loan callback
     */
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external override onlyBalancer nonReentrant {
        
        uint256 startGas = gasleft();
        address token = tokens[0];
        uint256 amount = amounts[0];
        uint256 fee = feeAmounts[0];
        
        (string memory strategy, uint256 expectedProfit, address arbitrageToken) = 
            abi.decode(userData, (string, uint256, address));
        
        require(keccak256(bytes(strategy)) == keccak256(bytes("DEX_ARBITRAGE")), "Invalid strategy");
        
        // Execute DEX arbitrage logic
        uint256 profit = _executeDEXArbitrage(arbitrageToken, amount);
        
        // Ensure we have enough to repay the flash loan
        uint256 repayAmount = amount + fee;
        require(IERC20(token).balanceOf(address(this)) >= repayAmount, "Insufficient funds to repay");
        
        // Repay flash loan
        IERC20(token).transfer(address(BALANCER_VAULT), repayAmount);
        
        // Extract profit
        uint256 finalBalance = IERC20(token).balanceOf(address(this));
        if (finalBalance > 0) {
            IERC20(token).transfer(PROFIT_WALLET, finalBalance);
            totalProfit += finalBalance;
        }
        
        totalExecutions++;
        emit ArbitrageExecuted(finalBalance, startGas - gasleft(), "DEX_ARBITRAGE");
    }
    
    /**
     * @dev Execute actual DEX arbitrage between QuickSwap and SushiSwap
     * @param token Token being arbitraged
     * @param amount Amount available for arbitrage
     * @return profit Profit generated
     */
    function _executeDEXArbitrage(address token, uint256 amount) internal returns (uint256 profit) {
        // Check prices on both DEXes
        uint256 quickswapPrice = _getQuickSwapPrice(token, USDC, amount);
        uint256 sushiswapPrice = _getSushiSwapPrice(token, USDC, amount);
        
        // Determine which direction is profitable
        if (quickswapPrice > sushiswapPrice) {
            // Buy on SushiSwap, sell on QuickSwap
            profit = _executeArbitrageTrade(token, amount, SUSHISWAP_ROUTER, QUICKSWAP_ROUTER);
        } else if (sushiswapPrice > quickswapPrice) {
            // Buy on QuickSwap, sell on SushiSwap
            profit = _executeArbitrageTrade(token, amount, QUICKSWAP_ROUTER, SUSHISWAP_ROUTER);
        }
        
        return profit;
    }
    
    function _getQuickSwapPrice(address tokenIn, address tokenOut, uint256 amountIn) 
        internal view returns (uint256) {
        try IUniswapV2Router(QUICKSWAP_ROUTER).getAmountsOut(
            amountIn, 
            _getPath(tokenIn, tokenOut)
        ) returns (uint[] memory amounts) {
            return amounts[amounts.length - 1];
        } catch {
            return 0;
        }
    }
    
    function _getSushiSwapPrice(address tokenIn, address tokenOut, uint256 amountIn) 
        internal view returns (uint256) {
        try IUniswapV2Router(SUSHISWAP_ROUTER).getAmountsOut(
            amountIn, 
            _getPath(tokenIn, tokenOut)
        ) returns (uint[] memory amounts) {
            return amounts[amounts.length - 1];
        } catch {
            return 0;
        }
    }
    
    function _executeArbitrageTrade(
        address token, 
        uint256 amount, 
        address buyRouter, 
        address sellRouter
    ) internal returns (uint256 profit) {
        
        // Approve routers
        IERC20(token).approve(buyRouter, amount);
        IERC20(USDC).approve(sellRouter, type(uint256).max);
        
        // Buy on first DEX
        address[] memory buyPath = _getPath(token, USDC);
        uint256[] memory buyAmounts = IUniswapV2Router(buyRouter).swapExactTokensForTokens(
            amount,
            0, // Accept any amount
            buyPath,
            address(this),
            block.timestamp + 300
        );
        
        uint256 usdcReceived = buyAmounts[buyAmounts.length - 1];
        
        // Sell on second DEX
        address[] memory sellPath = _getPath(USDC, token);
        uint256[] memory sellAmounts = IUniswapV2Router(sellRouter).swapExactTokensForTokens(
            usdcReceived,
            0, // Accept any amount
            sellPath,
            address(this),
            block.timestamp + 300
        );
        
        uint256 tokensBack = sellAmounts[sellAmounts.length - 1];
        
        // Calculate profit
        profit = tokensBack > amount ? tokensBack - amount : 0;
        
        return profit;
    }
    
    function _getPath(address tokenA, address tokenB) internal pure returns (address[] memory path) {
        path = new address[](2);
        path[0] = tokenA;
        path[1] = tokenB;
    }
    
    /**
     * @dev Simple profitable trade execution (no flash loan needed)
     * @param tokenIn Input token
     * @param tokenOut Output token  
     * @param amountIn Amount to trade
     */
    function executeSimpleTrade(
        address tokenIn,
        address tokenOut,
        uint256 amountIn
    ) external onlyOwner nonReentrant whenNotPaused {
        
        require(IERC20(tokenIn).balanceOf(address(this)) >= amountIn, "Insufficient balance");
        
        // Check which DEX offers better price
        uint256 quickswapPrice = _getQuickSwapPrice(tokenIn, tokenOut, amountIn);
        uint256 sushiswapPrice = _getSushiSwapPrice(tokenIn, tokenOut, amountIn);
        
        address bestRouter = quickswapPrice > sushiswapPrice ? QUICKSWAP_ROUTER : SUSHISWAP_ROUTER;
        uint256 bestPrice = quickswapPrice > sushiswapPrice ? quickswapPrice : sushiswapPrice;
        
        // Execute trade on best DEX
        IERC20(tokenIn).approve(bestRouter, amountIn);
        
        IUniswapV2Router(bestRouter).swapExactTokensForTokens(
            amountIn,
            bestPrice * 95 / 100, // 5% slippage tolerance
            _getPath(tokenIn, tokenOut),
            PROFIT_WALLET, // Send directly to profit wallet
            block.timestamp + 300
        );
        
        totalExecutions++;
        emit ArbitrageExecuted(bestPrice, 0, "SIMPLE_TRADE");
    }
    
    /**
     * @dev Emergency functions
     */
    function pause() external onlyOwner {
        _pause();
    }
    
    function unpause() external onlyOwner {
        _unpause();
    }
    
    function setMinProfitThreshold(uint256 _threshold) external onlyOwner {
        minProfitThreshold = _threshold;
    }
    
    function emergencyWithdraw(address token) external onlyOwner {
        uint256 balance = IERC20(token).balanceOf(address(this));
        if (balance > 0) {
            IERC20(token).transfer(PROFIT_WALLET, balance);
            emit EmergencyWithdraw(token, balance);
        }
    }
    
    /**
     * @dev View functions
     */
    function getStats() external view returns (uint256 executions, uint256 profit) {
        return (totalExecutions, totalProfit);
    }
    
    function checkArbitrageOpportunity(address token, uint256 amount) 
        external view returns (bool profitable, uint256 expectedProfit) {
        
        uint256 quickswapPrice = _getQuickSwapPrice(token, USDC, amount);
        uint256 sushiswapPrice = _getSushiSwapPrice(token, USDC, amount);
        
        if (quickswapPrice > sushiswapPrice) {
            expectedProfit = quickswapPrice - sushiswapPrice;
            profitable = expectedProfit > minProfitThreshold;
        } else if (sushiswapPrice > quickswapPrice) {
            expectedProfit = sushiswapPrice - quickswapPrice;
            profitable = expectedProfit > minProfitThreshold;
        }
    }
    
    function estimateGas(address token, uint256 amount) external view returns (uint256) {
        return 350000; // Conservative estimate for DEX arbitrage
    }
}

