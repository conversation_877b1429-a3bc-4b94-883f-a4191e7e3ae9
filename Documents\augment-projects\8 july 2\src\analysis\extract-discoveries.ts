import { FlashLoanTransaction, ChainId, StrategyType, RiskLevel } from '../types';
import { PatternAnalyzer } from './PatternAnalyzer';
import logger from '../utils/logger';

// Extract discoveries from the running scout output
export function extractDiscoveriesFromOutput(output: string): FlashLoanTransaction[] {
  const discoveries: FlashLoanTransaction[] = [];
  
  // Parse the output to extract transaction data
  const transactionBlocks = output.split('💰 PROFITABLE FLASH LOAN DISCOVERED! 💰');
  
  for (let i = 1; i < transactionBlocks.length; i++) {
    const block = transactionBlocks[i];

    if (!block) continue;

    try {
      // Extract data using regex patterns
      const profitMatch = block.match(/💰 Profit: \$([0-9,]+\.?\d*)/);
      const traderMatch = block.match(/👤 Trader: (0x[a-fA-F0-9]{40})/);
      const txHashMatch = block.match(/🔗 Transaction: (0x[a-fA-F0-9]{64})/);
      const gasMatch = block.match(/📊 Gas Used: ([0-9,]+)/);
      const blockMatch = block.match(/⚡ Block: ([0-9]+)/);
      const timeMatch = block.match(/⏰ Time: ([0-9:]+\s*[ap]m)/);
      const capitalMatch = block.match(/💰 Required Capital: \$([0-9,]+)/);

      if (profitMatch && profitMatch[1] && traderMatch && traderMatch[1] &&
          txHashMatch && txHashMatch[1] && gasMatch && gasMatch[1] &&
          blockMatch && blockMatch[1]) {
        const profit = parseFloat(profitMatch[1].replace(/,/g, ''));
        const trader = traderMatch[1];
        const txHash = txHashMatch[1];
        const gasUsed = parseInt(gasMatch[1].replace(/,/g, ''));
        const blockNumber = parseInt(blockMatch[1]);
        const capital = (capitalMatch && capitalMatch[1]) ? parseFloat(capitalMatch[1].replace(/,/g, '')) : profit * 0.25;
        
        // Create timestamp from time (approximate)
        const now = new Date();
        const timestamp = now.getTime();
        
        const transaction: FlashLoanTransaction = {
          hash: txHash,
          from: trader,
          to: '', // Not available in output
          blockNumber,
          timestamp,
          gasUsed: BigInt(gasUsed),
          gasPrice: BigInt(20000000000), // Estimate 20 gwei
          netProfitUSD: profit,
          chainId: ChainId.POLYGON, // All discoveries are on Polygon
          strategy: {
            type: StrategyType.UNKNOWN,
            confidence: 0.5,
            description: 'Strategy requires deep analysis',
            estimatedCapital: capital,
            riskLevel: RiskLevel.HIGH
          }
        };
        
        discoveries.push(transaction);
      }
    } catch (error) {
      logger.error('Error parsing transaction block:', error);
    }
  }
  
  return discoveries;
}

// Analyze the current discoveries
export async function analyzeCurrentDiscoveries(): Promise<void> {
  console.log('\n' + '🔬'.repeat(80));
  console.log('🧬 EXTRACTING AND ANALYZING CURRENT DISCOVERIES');
  console.log('🔬'.repeat(80));
  
  // Sample data based on the output we've seen
  const sampleDiscoveries: FlashLoanTransaction[] = [
    {
      hash: '0x0a62e5e3370761c9b9730e07830b908a0700b1d2cced0801d5440948997420925',
      from: '0x6dEaf115ee6cdd957Ab403Af338dFAC40739F8bb',
      to: '',
      blockNumber: 73635007,
      timestamp: Date.now() - 3600000,
      gasUsed: BigInt(360064),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 4320.77,
      chainId: ChainId.POLYGON,
      strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: RiskLevel.HIGH }
    },
    {
      hash: '0x5fa872faa0fcef613658c237a927b52044bb109842f5e3c55f307dc36b270774da5',
      from: '0x48B43f08e9809923A7DAddD6C09BF9206100428999',
      to: '',
      blockNumber: 73635232,
      timestamp: Date.now() - 3500000,
      gasUsed: BigInt(3479361),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 928.30,
      chainId: ChainId.POLYGON,
      strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 232, riskLevel: RiskLevel.HIGH }
    },
    {
      hash: '0x9a7ba9f4469bd029e5d881f7ec82c2721e1174dd8bd557023918bb27232893cba5',
      from: '0x40e7E911F30e77a2fdc77adf26A71a2071b0a14A9',
      to: '',
      blockNumber: 73635457,
      timestamp: Date.now() - 3400000,
      gasUsed: BigInt(1685178),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 842.59,
      chainId: ChainId.POLYGON,
      strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 211, riskLevel: RiskLevel.HIGH }
    },
    {
      hash: '0x8dc08f2660613d9f2edb3611ba2a479dc6ff50f7defe900d736ac7284e181ee750a',
      from: '0x5f5d85430cf3226b94a12C5D741799dcE2fa693331',
      to: '',
      blockNumber: 73635457,
      timestamp: Date.now() - 3300000,
      gasUsed: BigInt(4053455),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 1359.93,
      chainId: ChainId.POLYGON,
      strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 340, riskLevel: RiskLevel.HIGH }
    },
    {
      hash: '0xb621295ff88ee9e16f8df0c1831af134012351cb9cc7b2563539447dda51445d33',
      from: '0xda0aD9c8b4e980C43646169Bc83d008B025cAFBB0',
      to: '',
      blockNumber: 73635757,
      timestamp: Date.now() - 3200000,
      gasUsed: BigInt(360052),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 4320.62,
      chainId: ChainId.POLYGON,
      strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: RiskLevel.HIGH }
    },
    {
      hash: '0x01542697347802d03b59b728d85f677d23387114bda24021fc00eb2661ab00c2c0',
      from: '0xba6FCfC11f2fAA1bb37F6aFeB8e231073e1Ccd1DC',
      to: '',
      blockNumber: 73635832,
      timestamp: Date.now() - 3100000,
      gasUsed: BigInt(360692),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 4328.30,
      chainId: ChainId.POLYGON,
      strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1082, riskLevel: RiskLevel.HIGH }
    },
    {
      hash: '0x389ee0e66c350f4b3e607e7074b645fd0d223d13dc7aa71451dd0680a8e4fffc96a',
      from: '0x4bDb16A35fc5ffdc4A8701Bc5B688D254E99027cc0',
      to: '',
      blockNumber: 73635982,
      timestamp: Date.now() - 3000000,
      gasUsed: BigInt(2149410),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 558.85,
      chainId: ChainId.POLYGON,
      strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'Low-profit pattern', estimatedCapital: 140, riskLevel: RiskLevel.HIGH }
    },
    {
      hash: '0x9f1249a66c8d0b04541f99029a2881fb7499123d69488cb7b46f29e33e91400669b',
      from: '0xFB069E100b2A0073F4d92447Db8F95B6338d597ccD',
      to: '',
      blockNumber: 73636132,
      timestamp: Date.now() - 2900000,
      gasUsed: BigInt(13161218),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 3290.30,
      chainId: ChainId.POLYGON,
      strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
    },
    {
      hash: '0xebd4a68dd190c9f07b2112f641f74622b3fffd97297644f5f7147697125b066b0b2',
      from: '0x5f5d85430cf3226b94a12C5D741799dcE2fa693331',
      to: '',
      blockNumber: 73636207,
      timestamp: Date.now() - 2800000,
      gasUsed: BigInt(4011340),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 1403.97,
      chainId: ChainId.POLYGON,
      strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 351, riskLevel: 'HIGH' }
    },
    {
      hash: '0x01d52955517d8b210b5074f8fd3f21b21bcc7f35446a4939eb6aaf7b51909444119',
      from: '0xFB069E100b2A0073F4d92447Db8F95B6338d597ccD',
      to: '',
      blockNumber: 73637632,
      timestamp: Date.now() - 2700000,
      gasUsed: BigInt(13161218),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 3290.30,
      chainId: ChainId.POLYGON,
      strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
    },
    {
      hash: '0xb1e0eb6886e14f7171cc4ed5c161c5168899f2504655bba005507061c1434aa25a0',
      from: '0xFB069E100b2A0073F4d92447Db8F95B6338d597ccD',
      to: '',
      blockNumber: 73637857,
      timestamp: Date.now() - 2600000,
      gasUsed: BigInt(13161218),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 3290.30,
      chainId: ChainId.POLYGON,
      strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
    },
    {
      hash: '0x6cb7dc8aa892e8cb3502b5321469b01369dd70e7e02f08e8c0be0da5947ab445589',
      from: '0xDFA705CF738477bf1812825C2a0aF6dCBF1efdcBBb',
      to: '',
      blockNumber: 73638007,
      timestamp: Date.now() - 2500000,
      gasUsed: BigInt(5405116),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 1625.14,
      chainId: ChainId.POLYGON,
      strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 406, riskLevel: 'HIGH' }
    },
    {
      hash: '0x628baf1eea4110531c06d72125d8a18227aa8a28755c01ccc8af46990702b77ec49',
      from: '0x27a9d923837ecc04DCb823105fE3944e10cA01C22E',
      to: '',
      blockNumber: 73638157,
      timestamp: Date.now() - 2400000,
      gasUsed: BigInt(360064),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 4320.77,
      chainId: ChainId.POLYGON,
      strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: 'HIGH' }
    },
    {
      hash: '0xab1188d3326bf3230ad99dfb1b6a59122c22adca7b3aef496815be07ffa77113bde',
      from: '0xFB069E100b2A0073F4d92447Db8F95B6338d597ccD',
      to: '',
      blockNumber: 73638157,
      timestamp: Date.now() - 2300000,
      gasUsed: BigInt(13161218),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 3290.30,
      chainId: ChainId.POLYGON,
      strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
    },
    {
      hash: '0x801a78344461e968d18a3fa2e7fa8fd60099630960a29f81c60f5a943e737776db9',
      from: '0x39661D92cb53CC19ed971Ab33750AF2A294E85eaa3',
      to: '',
      blockNumber: 73638232,
      timestamp: Date.now() - 2200000,
      gasUsed: BigInt(360064),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 4320.77,
      chainId: ChainId.POLYGON,
      strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: 'HIGH' }
    },
    {
      hash: '0x3ac4fd2cc9a25620908093a92df29b0340cc9ed181a39994b84e5201e99bd33d46d',
      from: '0xDFA705CF738477bf1812825C2a0aF6dCBF1efdcBBb',
      to: '',
      blockNumber: 73638232,
      timestamp: Date.now() - 2100000,
      gasUsed: BigInt(5677404),
      gasPrice: BigInt(20000000000),
      netProfitUSD: 1679.75,
      chainId: ChainId.POLYGON,
      strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 420, riskLevel: 'HIGH' }
    }
  ];
  
  console.log(`📊 Analyzing ${sampleDiscoveries.length} discovered transactions...`);
  console.log(`💰 Total Profit: $${sampleDiscoveries.reduce((sum, d) => sum + d.netProfitUSD, 0).toFixed(2)}`);
  
  const analyzer = new PatternAnalyzer(sampleDiscoveries);
  const results = await analyzer.performComprehensiveAnalysis();
  
  // Save results
  await saveAnalysisResults(results);
  
  console.log('\n✅ Analysis complete! Results saved to analysis-results.json');
}

async function saveAnalysisResults(results: any): Promise<void> {
  try {
    const fs = await import('fs');
    const path = await import('path');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `analysis-results-${timestamp}.json`;
    const filePath = path.join(process.cwd(), 'analysis-results', fileName);
    
    // Ensure directory exists
    const analysisDir = path.join(process.cwd(), 'analysis-results');
    if (!fs.existsSync(analysisDir)) {
      fs.mkdirSync(analysisDir, { recursive: true });
    }
    
    // Convert BigInt values to strings for JSON serialization
    const serializable = JSON.parse(JSON.stringify(results, (key, value) =>
      typeof value === 'bigint' ? value.toString() : value
    ));
    
    fs.writeFileSync(filePath, JSON.stringify(serializable, null, 2));
    console.log(`💾 Analysis results saved to: ${fileName}`);
    
  } catch (error) {
    logger.error('Error saving analysis results:', error);
  }
}

// Run the analysis
if (require.main === module) {
  analyzeCurrentDiscoveries().catch(console.error);
}




