const { ethers } = require("hardhat");
require("dotenv").config();

async function main() {
    console.log("🧪 Testing PMMS Strategies...");
    
    const [deployer] = await ethers.getSigners();
    console.log("Testing with account:", deployer.address);

    // Load deployment addresses (you'll need to update these after deployment)
    const REGISTRY_ADDRESS = process.env.REGISTRY_ADDRESS;
    const PMMS_ADDRESS = process.env.PMMS_ADDRESS;
    
    if (!REGISTRY_ADDRESS || !PMMS_ADDRESS) {
        console.log("❌ Please set REGISTRY_ADDRESS and PMMS_ADDRESS in .env file");
        return;
    }

    // Get contract instances
    const registry = await ethers.getContractAt("Registry", REGISTRY_ADDRESS);
    const pmms = await ethers.getContractAt("ProfitMaximizerModularSystem", PMMS_ADDRESS);

    // Token addresses on Polygon
    const USDC = "******************************************";
    const WETH = "******************************************";

    try {
        console.log("\n1️⃣ Testing DEX Arbitrage Strategy...");
        
        // Get DEX Arbitrage strategy
        const dexArbAddress = await registry.getStrategy("DexArbitrage");
        console.log("DEX Arbitrage Strategy:", dexArbAddress);
        
        const dexArb = await ethers.getContractAt("StrategyDexArbitrage", dexArbAddress);
        
        // Check for arbitrage opportunity
        const amount = ethers.utils.parseUnits("1000", 6); // 1000 USDC
        const [profit, executionData] = await dexArb.checkOpportunity(USDC, amount);
        
        console.log("Profit potential:", ethers.utils.formatUnits(profit, 6), "USDC");
        
        if (profit.gt(0)) {
            console.log("✅ Arbitrage opportunity found!");
            console.log("Execution data:", executionData);
        } else {
            console.log("ℹ️ No arbitrage opportunity at this time");
        }

        console.log("\n2️⃣ Testing Triangular Arbitrage Strategy...");
        
        const triangularArbAddress = await registry.getStrategy("TriangularArbitrage");
        console.log("Triangular Arbitrage Strategy:", triangularArbAddress);
        
        const triangularArb = await ethers.getContractAt("StrategyTriangularArbitrage", triangularArbAddress);
        
        // Check triangular arbitrage opportunity
        const [triangularProfit, triangularData] = await triangularArb.checkOpportunity(USDC, amount);
        
        console.log("Triangular profit potential:", ethers.utils.formatUnits(triangularProfit, 6), "USDC");
        
        if (triangularProfit.gt(0)) {
            console.log("✅ Triangular arbitrage opportunity found!");
        } else {
            console.log("ℹ️ No triangular arbitrage opportunity at this time");
        }

        console.log("\n3️⃣ Testing Stablecoin Peg Arbitrage Strategy...");
        
        const stablecoinArbAddress = await registry.getStrategy("StablecoinPegArbitrage");
        console.log("Stablecoin Arbitrage Strategy:", stablecoinArbAddress);
        
        const stablecoinArb = await ethers.getContractAt("StrategyStablecoinPegArbitrage", stablecoinArbAddress);
        
        // Check stablecoin arbitrage opportunity
        const [stablecoinProfit, stablecoinData] = await stablecoinArb.checkOpportunity(USDC, amount);
        
        console.log("Stablecoin profit potential:", ethers.utils.formatUnits(stablecoinProfit, 6), "USDC");
        
        if (stablecoinProfit.gt(0)) {
            console.log("✅ Stablecoin arbitrage opportunity found!");
        } else {
            console.log("ℹ️ No stablecoin arbitrage opportunity at this time");
        }

        console.log("\n4️⃣ Testing Registry Configuration...");
        
        // Check if all addresses are properly configured
        const aaveProvider = await registry.getAddress("AAVE_ADDRESS_PROVIDER");
        const uniswapV3 = await registry.getAddress("UNISWAP_V3");
        const sushiswap = await registry.getAddress("SUSHISWAP");
        const weth = await registry.getAddress("WETH");
        const usdc = await registry.getAddress("USDC");
        
        console.log("AAVE Provider:", aaveProvider);
        console.log("Uniswap V3:", uniswapV3);
        console.log("SushiSwap:", sushiswap);
        console.log("WETH:", weth);
        console.log("USDC:", usdc);
        
        // Verify all addresses are set
        const requiredAddresses = [aaveProvider, uniswapV3, sushiswap, weth, usdc];
        const allConfigured = requiredAddresses.every(addr => addr !== ethers.constants.AddressZero);
        
        if (allConfigured) {
            console.log("✅ All registry addresses properly configured");
        } else {
            console.log("❌ Some registry addresses not configured");
        }

        console.log("\n5️⃣ Simulating Flash Loan Execution (DRY RUN)...");
        
        // This is a dry run - we won't actually execute
        console.log("Would execute flash loan with:");
        console.log("- Asset:", USDC);
        console.log("- Amount:", ethers.utils.formatUnits(amount, 6), "USDC");
        console.log("- Strategy: DexArbitrage");
        
        // In a real scenario, you would call:
        // await pmms.executeStrategy("DexArbitrage", USDC, amount, executionData);
        
        console.log("✅ Dry run completed successfully");

        console.log("\n🎉 STRATEGY TESTING COMPLETE!");
        console.log("=".repeat(50));
        console.log("✅ All strategies deployed and accessible");
        console.log("✅ Registry properly configured");
        console.log("✅ Ready for live trading!");
        console.log("=".repeat(50));

    } catch (error) {
        console.error("❌ Strategy testing failed:", error);
        process.exit(1);
    }
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });
