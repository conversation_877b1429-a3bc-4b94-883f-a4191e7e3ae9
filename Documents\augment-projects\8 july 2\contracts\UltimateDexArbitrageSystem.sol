// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * 🚀 ULTIMATE DEX ARBITRAGE SYSTEM - POLYGON POWERHOUSE
 * 15 DEXes integrated for maximum arbitrage opportunities
 * Using CONFIRMED WORKING flash loan mechanism
 * Goal: Generate ETH profits to fund Arbitrum eMode strategy
 */

interface IERC20 {
    function transfer(address to, uint256 amount) external returns (bool);
    function balanceOf(address account) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
}

interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external;
}

interface IUniswapV2Router {
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);
    
    function getAmountsOut(uint amountIn, address[] calldata path)
        external view returns (uint[] memory amounts);
}

interface IUniswapV3Router {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }
    
    function exactInputSingle(ExactInputSingleParams calldata params)
        external payable returns (uint256 amountOut);
}

contract UltimateDexArbitrageSystem is IFlashLoanRecipient {
    
    // 🎯 POLYGON TOKENS
    IERC20 public constant USDC = IERC20(******************************************);
    IERC20 public constant WETH = IERC20(******************************************);
    IERC20 public constant WMATIC = IERC20(******************************************);
    IERC20 public constant WBTC = IERC20(******************************************);
    IERC20 public constant DAI = IERC20(******************************************);
    IERC20 public constant USDT = IERC20(******************************************);
    
    // 🏦 FLASH LOAN PROVIDER
    IBalancerVault public constant BALANCER = IBalancerVault(******************************************);
    
    // 🔥 15 MAJOR DEXES ON POLYGON
    IUniswapV2Router public constant QUICKSWAP = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant SUSHISWAP = IUniswapV2Router(******************************************);
    IUniswapV3Router public constant UNISWAP_V3 = IUniswapV3Router(******************************************);
    IUniswapV2Router public constant DFYN = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant POLYCAT = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant WAULTSWAP = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant JETSWAP = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant APESWAP = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant HONEYSWAP = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant COMETHSWAP = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant POLYDEX = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant MESHSWAP = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant RADIOSHACK = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant FIREBIRD = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant POLYSWAP = IUniswapV2Router(******************************************);
    
    address public constant PROFIT_WALLET = ******************************************;
    
    // 🔥 ULTIMATE ARBITRAGE PARAMETERS
    uint256 public constant FLASH_AMOUNT = 50000e6;    // $50K USDC (scale for serious profits)
    uint256 public constant MIN_PROFIT_BASIS = 20;     // 0.2% minimum profit
    uint256 public constant SLIPPAGE_TOLERANCE = 300;  // 3% slippage tolerance
    uint256 public constant MAX_GAS_PRICE = 100e9;     // 100 gwei max
    
    // 📊 RESULTS
    uint256 public lastProfit;
    bool public lastSuccess;
    uint256 public totalExecutions;
    uint256 public totalProfit;
    uint256 public successfulArbitrages;
    
    // 🎯 DEX TRACKING
    mapping(address => string) public dexNames;
    mapping(address => bool) public activeDexes;
    address[] public allDexes;
    
    event FlashLoanResult(uint256 profit, bool success, uint256 execution);
    event ArbitrageExecuted(
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        uint256 profit,
        address buyDex,
        address sellDex,
        string strategy
    );
    event DebugStep(string step, uint256 value);
    event ProfitExtracted(uint256 amount, address wallet, uint256 execution);
    event DexOpportunityFound(address dex1, address dex2, uint256 priceDiff, address token);
    
    constructor() {
        // Initialize DEX registry
        _initializeDexRegistry();
    }
    
    /**
     * 🏗️ INITIALIZE DEX REGISTRY
     */
    function _initializeDexRegistry() internal {
        // Register all 15 DEXes
        _registerDex(address(QUICKSWAP), "QuickSwap");
        _registerDex(address(SUSHISWAP), "SushiSwap");
        _registerDex(address(UNISWAP_V3), "Uniswap V3");
        _registerDex(address(DFYN), "DFYN");
        _registerDex(address(POLYCAT), "PolyCat");
        _registerDex(address(WAULTSWAP), "WaultSwap");
        _registerDex(address(JETSWAP), "JetSwap");
        _registerDex(address(APESWAP), "ApeSwap");
        _registerDex(address(HONEYSWAP), "HoneySwap");
        _registerDex(address(COMETHSWAP), "ComethSwap");
        _registerDex(address(POLYDEX), "PolyDEX");
        _registerDex(address(MESHSWAP), "MeshSwap");
        _registerDex(address(RADIOSHACK), "RadioShack");
        _registerDex(address(FIREBIRD), "Firebird");
        _registerDex(address(POLYSWAP), "PolySwap");
    }
    
    function _registerDex(address dex, string memory name) internal {
        dexNames[dex] = name;
        activeDexes[dex] = true;
        allDexes.push(dex);
    }
    
    /**
     * 🚀 EXECUTE ULTIMATE DEX ARBITRAGE
     */
    function executeUltimateDexArbitrage() external {
        require(tx.gasprice <= MAX_GAS_PRICE, "Gas price too high");
        
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        
        tokens[0] = address(USDC);
        amounts[0] = FLASH_AMOUNT;
        
        BALANCER.flashLoan(address(this), tokens, amounts, "");
    }
    
    /**
     * 🎯 FLASH LOAN CALLBACK - CONFIRMED WORKING INTERFACE
     */
    function receiveFlashLoan(
        address[] memory,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory
    ) external override {
        require(msg.sender == address(BALANCER), "Only Balancer");
        
        uint256 flashAmount = amounts[0];
        uint256 initialBalance = USDC.balanceOf(address(this));
        
        emit DebugStep("Flash loan received", initialBalance);
        
        // Verify CONFIRMED WORKING flash loan
        require(initialBalance >= flashAmount, "Flash loan not received");
        require(feeAmounts[0] == 0, "Expected 0% fee");
        
        // Execute ULTIMATE arbitrage strategies across 15 DEXes
        executeUltimateArbitrageStrategies(flashAmount);
        
        // Repay flash loan
        USDC.transfer(address(BALANCER), flashAmount);
        emit DebugStep("Flash loan repaid", flashAmount);
        
        // Extract profit
        uint256 finalBalance = USDC.balanceOf(address(this));
        totalExecutions++;
        
        if (finalBalance > 0) {
            lastProfit = finalBalance;
            totalProfit += finalBalance;
            successfulArbitrages++;
            USDC.transfer(PROFIT_WALLET, finalBalance);
            lastSuccess = true;
            emit ProfitExtracted(finalBalance, PROFIT_WALLET, totalExecutions);
        } else {
            lastProfit = 0;
            lastSuccess = false;
        }
        
        emit FlashLoanResult(lastProfit, lastSuccess, totalExecutions);
    }
    
    /**
     * 💰 ULTIMATE ARBITRAGE STRATEGIES
     */
    function executeUltimateArbitrageStrategies(uint256 flashAmount) internal {
        emit DebugStep("Starting Ultimate DEX arbitrage", flashAmount);
        
        uint256 strategyProfit = 0;
        
        // Strategy 1: USDC → WETH arbitrage across all DEXes
        strategyProfit += executeMultiDexArbitrage(
            address(USDC),
            address(WETH),
            flashAmount / 5, // 20% of flash loan
            "USDC-WETH"
        );

        // Strategy 2: USDC → WMATIC arbitrage
        strategyProfit += executeMultiDexArbitrage(
            address(USDC),
            address(WMATIC),
            flashAmount / 5,
            "USDC-WMATIC"
        );

        // Strategy 3: USDC → WBTC arbitrage
        strategyProfit += executeMultiDexArbitrage(
            address(USDC),
            address(WBTC),
            flashAmount / 5,
            "USDC-WBTC"
        );

        // Strategy 4: Stablecoin arbitrage (USDC → DAI → USDT → USDC) WITH ERROR HANDLING
        try this.executeStablecoinTriangularArbitrageWithErrorHandling(flashAmount / 5) returns (uint256 stablecoinProfit) {
            strategyProfit += stablecoinProfit;
            emit DebugStep("Stablecoin strategy profit", stablecoinProfit);
        } catch Error(string memory reason) {
            emit DebugStep(string(abi.encodePacked("Stablecoin strategy failed: ", reason)), 0);
        } catch {
            emit DebugStep("Stablecoin strategy failed: unknown", 0);
        }

        // Strategy 5: Multi-hop arbitrage (USDC → WETH → WMATIC → USDC) WITH ERROR HANDLING
        try this.executeMultiHopArbitrageWithErrorHandling(flashAmount / 5) returns (uint256 multihopProfit) {
            strategyProfit += multihopProfit;
            emit DebugStep("Multi-hop strategy profit", multihopProfit);
        } catch Error(string memory reason) {
            emit DebugStep(string(abi.encodePacked("Multi-hop strategy failed: ", reason)), 0);
        } catch {
            emit DebugStep("Multi-hop strategy failed: unknown", 0);
        }

        emit DebugStep("Total arbitrage profit", strategyProfit);
    }
    
    /**
     * 🔄 MULTI-DEX ARBITRAGE WITH ERROR HANDLING
     */
    function executeMultiDexArbitrage(
        address tokenA,
        address tokenB,
        uint256 amount,
        string memory strategy
    ) internal returns (uint256 profit) {
        if (amount == 0) {
            emit DebugStep("Amount is zero", 0);
            return 0;
        }

        emit DebugStep(string(abi.encodePacked("Starting ", strategy)), amount);

        // Find best buy and sell DEXes WITH ERROR HANDLING
        (address bestBuyDex, address bestSellDex, uint256 maxProfit) =
            findBestArbitrageOpportunityWithErrorHandling(tokenA, tokenB, amount);

        if (bestBuyDex == address(0) || maxProfit == 0) {
            emit DebugStep(string(abi.encodePacked(strategy, " - No opportunity")), 0);
            return 0;
        }

        emit DexOpportunityFound(bestBuyDex, bestSellDex, maxProfit, tokenA);
        emit DebugStep(string(abi.encodePacked(strategy, " - Opportunity found")), maxProfit);

        // Execute arbitrage WITH ERROR HANDLING
        try this.executeArbitrageBetweenDexesWithErrorHandling(
            tokenA,
            tokenB,
            amount,
            bestBuyDex,
            bestSellDex,
            strategy
        ) returns (uint256 arbitrageProfit) {
            profit = arbitrageProfit;
            emit DebugStep(string(abi.encodePacked(strategy, " - Success")), profit);
        } catch Error(string memory reason) {
            emit DebugStep(string(abi.encodePacked(strategy, " - Failed: ", reason)), 0);
            profit = 0;
        } catch {
            emit DebugStep(string(abi.encodePacked(strategy, " - Unknown error")), 0);
            profit = 0;
        }
    }
    
    /**
     * 🔍 FIND BEST ARBITRAGE OPPORTUNITY WITH COMPREHENSIVE ERROR HANDLING
     */
    function findBestArbitrageOpportunityWithErrorHandling(
        address tokenA,
        address tokenB,
        uint256 amount
    ) internal returns (
        address bestBuyDex,
        address bestSellDex,
        uint256 maxProfit
    ) {
        uint256 bestBuyPrice = 0;
        uint256 bestSellPrice = type(uint256).max;
        uint256 workingDexes = 0;
        uint256 failedDexes = 0;

        address[] memory path = new address[](2);
        path[0] = tokenA;
        path[1] = tokenB;

        emit DebugStep("Checking DEXes for opportunities", allDexes.length);

        // Check all DEXes for best prices WITH ERROR HANDLING
        for (uint i = 0; i < allDexes.length; i++) {
            if (!activeDexes[allDexes[i]]) {
                emit DebugStep(string(abi.encodePacked("DEX inactive: ", dexNames[allDexes[i]])), i);
                continue;
            }

            try IUniswapV2Router(allDexes[i]).getAmountsOut(amount, path)
                returns (uint256[] memory amounts) {

                uint256 outputAmount = amounts[1];
                workingDexes++;

                emit DebugStep(string(abi.encodePacked(dexNames[allDexes[i]], " output")), outputAmount);

                // Check if this is the best buy price (highest output)
                if (outputAmount > bestBuyPrice) {
                    bestBuyPrice = outputAmount;
                    bestBuyDex = allDexes[i];
                    emit DebugStep(string(abi.encodePacked("New best buy: ", dexNames[allDexes[i]])), outputAmount);
                }

                // Check if this is the best sell price (lowest input needed)
                if (outputAmount < bestSellPrice && outputAmount > 0) {
                    bestSellPrice = outputAmount;
                    bestSellDex = allDexes[i];
                    emit DebugStep(string(abi.encodePacked("New best sell: ", dexNames[allDexes[i]])), outputAmount);
                }

            } catch Error(string memory reason) {
                failedDexes++;
                emit DebugStep(string(abi.encodePacked(dexNames[allDexes[i]], " failed: ", reason)), i);
            } catch {
                failedDexes++;
                emit DebugStep(string(abi.encodePacked(dexNames[allDexes[i]], " failed: unknown")), i);
            }
        }

        emit DebugStep("Working DEXes", workingDexes);
        emit DebugStep("Failed DEXes", failedDexes);
        emit DebugStep("Best buy price", bestBuyPrice);
        emit DebugStep("Best sell price", bestSellPrice);

        // Calculate potential profit
        if (bestBuyPrice > bestSellPrice && bestBuyDex != bestSellDex) {
            uint256 priceDiff = bestBuyPrice - bestSellPrice;
            uint256 profitBasisPoints = (priceDiff * 10000) / bestSellPrice;

            emit DebugStep("Profit basis points", profitBasisPoints);
            emit DebugStep("Min profit basis", MIN_PROFIT_BASIS);

            if (profitBasisPoints > MIN_PROFIT_BASIS) {
                maxProfit = priceDiff;
                emit DebugStep("Profitable opportunity confirmed", maxProfit);
            } else {
                emit DebugStep("Profit too small", profitBasisPoints);
            }
        } else {
            emit DebugStep("No price difference or same DEX", 0);
        }
    }
    
    /**
     * 📊 VIEW FUNCTIONS
     */
    function getResults() external view returns (
        uint256 profit,
        bool success,
        uint256 executions,
        uint256 totalProfits,
        uint256 successRate
    ) {
        uint256 rate = totalExecutions > 0 ? (successfulArbitrages * 10000) / totalExecutions : 0;
        return (lastProfit, lastSuccess, totalExecutions, totalProfit, rate);
    }
    
    function getAllDexes() external view returns (address[] memory) {
        return allDexes;
    }
    
    function getDexInfo(address dex) external view returns (string memory name, bool active) {
        return (dexNames[dex], activeDexes[dex]);
    }

    /**
     * 🔄 EXECUTE ARBITRAGE BETWEEN DEXES WITH ERROR HANDLING (EXTERNAL FOR TRY-CATCH)
     */
    function executeArbitrageBetweenDexesWithErrorHandling(
        address tokenA,
        address tokenB,
        uint256 amount,
        address buyDex,
        address sellDex,
        string memory strategy
    ) external returns (uint256 profit) {
        require(msg.sender == address(this), "Internal only");

        address[] memory path = new address[](2);
        path[0] = tokenA;
        path[1] = tokenB;

        address[] memory reversePath = new address[](2);
        reversePath[0] = tokenB;
        reversePath[1] = tokenA;

        uint256 balanceBefore = IERC20(tokenA).balanceOf(address(this));

        emit DebugStep(string(abi.encodePacked(strategy, " - Starting arbitrage")), amount);
        emit DebugStep(string(abi.encodePacked("Buy DEX: ", dexNames[buyDex])), 0);
        emit DebugStep(string(abi.encodePacked("Sell DEX: ", dexNames[sellDex])), 0);

        try this.executeSafeArbitrageSwapsWithErrorHandling(
            buyDex,
            sellDex,
            path,
            reversePath,
            amount,
            strategy
        ) {
            uint256 balanceAfter = IERC20(tokenA).balanceOf(address(this));
            profit = balanceAfter > balanceBefore ? balanceAfter - balanceBefore : 0;

            emit DebugStep(string(abi.encodePacked(strategy, " - Balance before")), balanceBefore);
            emit DebugStep(string(abi.encodePacked(strategy, " - Balance after")), balanceAfter);
            emit DebugStep(string(abi.encodePacked(strategy, " - Profit")), profit);

            if (profit > 0) {
                emit ArbitrageExecuted(
                    tokenA,
                    tokenB,
                    amount,
                    profit,
                    buyDex,
                    sellDex,
                    strategy
                );
            }
        } catch Error(string memory reason) {
            emit DebugStep(string(abi.encodePacked(strategy, " - Swap failed: ", reason)), 0);
            profit = 0;
        } catch {
            emit DebugStep(string(abi.encodePacked(strategy, " - Swap failed: unknown")), 0);
            profit = 0;
        }
    }

    /**
     * 🔄 EXECUTE SAFE ARBITRAGE SWAPS WITH COMPREHENSIVE ERROR HANDLING
     */
    function executeSafeArbitrageSwapsWithErrorHandling(
        address buyDex,
        address sellDex,
        address[] memory path,
        address[] memory reversePath,
        uint256 amountIn,
        string memory strategy
    ) external {
        require(msg.sender == address(this), "Internal only");

        emit DebugStep(string(abi.encodePacked(strategy, " - Starting swaps")), amountIn);

        // Buy tokenB on buyDex WITH ERROR HANDLING
        IERC20(path[0]).approve(buyDex, amountIn);
        emit DebugStep(string(abi.encodePacked(strategy, " - Approved for buy")), amountIn);

        uint256[] memory amounts1;
        try IUniswapV2Router(buyDex).swapExactTokensForTokens(
            amountIn,
            0, // Accept any amount for now
            path,
            address(this),
            block.timestamp + 300
        ) returns (uint256[] memory _amounts1) {
            amounts1 = _amounts1;
            emit DebugStep(string(abi.encodePacked(strategy, " - Buy successful")), amounts1[1]);
        } catch Error(string memory reason) {
            emit DebugStep(string(abi.encodePacked(strategy, " - Buy failed: ", reason)), 0);
            revert(string(abi.encodePacked("Buy failed: ", reason)));
        } catch {
            emit DebugStep(string(abi.encodePacked(strategy, " - Buy failed: unknown")), 0);
            revert("Buy failed: unknown error");
        }

        uint256 tokenBReceived = amounts1[1];
        emit DebugStep(string(abi.encodePacked(strategy, " - Token B received")), tokenBReceived);

        // Sell tokenB on sellDex WITH ERROR HANDLING
        IERC20(path[1]).approve(sellDex, tokenBReceived);
        emit DebugStep(string(abi.encodePacked(strategy, " - Approved for sell")), tokenBReceived);

        uint256 minAmountOut = (amountIn * (10000 - SLIPPAGE_TOLERANCE)) / 10000;
        emit DebugStep(string(abi.encodePacked(strategy, " - Min amount out")), minAmountOut);

        try IUniswapV2Router(sellDex).swapExactTokensForTokens(
            tokenBReceived,
            minAmountOut,
            reversePath,
            address(this),
            block.timestamp + 300
        ) returns (uint256[] memory amounts2) {
            emit DebugStep(string(abi.encodePacked(strategy, " - Sell successful")), amounts2[1]);
        } catch Error(string memory reason) {
            emit DebugStep(string(abi.encodePacked(strategy, " - Sell failed: ", reason)), 0);
            revert(string(abi.encodePacked("Sell failed: ", reason)));
        } catch {
            emit DebugStep(string(abi.encodePacked(strategy, " - Sell failed: unknown")), 0);
            revert("Sell failed: unknown error");
        }

        emit DebugStep(string(abi.encodePacked(strategy, " - Swaps completed")), 1);
    }

    /**
     * 🔺 STABLECOIN TRIANGULAR ARBITRAGE WITH ERROR HANDLING (EXTERNAL FOR TRY-CATCH)
     */
    function executeStablecoinTriangularArbitrageWithErrorHandling(uint256 amount) external returns (uint256 profit) {
        require(msg.sender == address(this), "Internal only");

        if (amount == 0) {
            emit DebugStep("Stablecoin amount is zero", 0);
            return 0;
        }

        uint256 balanceBefore = USDC.balanceOf(address(this));
        emit DebugStep("Stablecoin - Starting triangular arbitrage", amount);
        emit DebugStep("Stablecoin - Balance before", balanceBefore);

        try this.executeStablecoinTriangleWithErrorHandling(amount) {
            uint256 balanceAfter = USDC.balanceOf(address(this));
            profit = balanceAfter > balanceBefore ? balanceAfter - balanceBefore : 0;

            emit DebugStep("Stablecoin - Balance after", balanceAfter);
            emit DebugStep("Stablecoin - Profit", profit);

            if (profit > 0) {
                emit ArbitrageExecuted(
                    address(USDC),
                    address(DAI),
                    amount,
                    profit,
                    address(QUICKSWAP),
                    address(SUSHISWAP),
                    "Stablecoin-Triangle"
                );
            }
        } catch Error(string memory reason) {
            emit DebugStep(string(abi.encodePacked("Stablecoin arbitrage failed: ", reason)), 0);
            profit = 0;
        } catch {
            emit DebugStep("Stablecoin arbitrage failed: unknown", 0);
            profit = 0;
        }
    }

    /**
     * 🔺 EXECUTE STABLECOIN TRIANGLE WITH COMPREHENSIVE ERROR HANDLING
     */
    function executeStablecoinTriangleWithErrorHandling(uint256 amount) external {
        require(msg.sender == address(this), "Internal only");

        emit DebugStep("Stablecoin triangle - Starting", amount);

        // USDC → DAI on QuickSwap WITH ERROR HANDLING
        address[] memory path1 = new address[](2);
        path1[0] = address(USDC);
        path1[1] = address(DAI);

        USDC.approve(address(QUICKSWAP), amount);
        emit DebugStep("Stablecoin triangle - USDC approved for QuickSwap", amount);

        uint256[] memory amounts1;
        try QUICKSWAP.swapExactTokensForTokens(
            amount,
            0,
            path1,
            address(this),
            block.timestamp + 300
        ) returns (uint256[] memory _amounts1) {
            amounts1 = _amounts1;
            emit DebugStep("Stablecoin triangle - USDC to DAI successful", amounts1[1]);
        } catch Error(string memory reason) {
            emit DebugStep(string(abi.encodePacked("USDC to DAI failed: ", reason)), 0);
            revert(string(abi.encodePacked("USDC to DAI failed: ", reason)));
        }

        // DAI → USDT on SushiSwap WITH ERROR HANDLING
        address[] memory path2 = new address[](2);
        path2[0] = address(DAI);
        path2[1] = address(USDT);

        uint256 daiReceived = amounts1[1];
        DAI.approve(address(SUSHISWAP), daiReceived);
        emit DebugStep("Stablecoin triangle - DAI approved for SushiSwap", daiReceived);

        uint256[] memory amounts2;
        try SUSHISWAP.swapExactTokensForTokens(
            daiReceived,
            0,
            path2,
            address(this),
            block.timestamp + 300
        ) returns (uint256[] memory _amounts2) {
            amounts2 = _amounts2;
            emit DebugStep("Stablecoin triangle - DAI to USDT successful", amounts2[1]);
        } catch Error(string memory reason) {
            emit DebugStep(string(abi.encodePacked("DAI to USDT failed: ", reason)), 0);
            revert(string(abi.encodePacked("DAI to USDT failed: ", reason)));
        }

        // USDT → USDC on DFYN WITH ERROR HANDLING
        address[] memory path3 = new address[](2);
        path3[0] = address(USDT);
        path3[1] = address(USDC);

        uint256 usdtReceived = amounts2[1];
        USDT.approve(address(DFYN), usdtReceived);
        emit DebugStep("Stablecoin triangle - USDT approved for DFYN", usdtReceived);

        try DFYN.swapExactTokensForTokens(
            usdtReceived,
            0,
            path3,
            address(this),
            block.timestamp + 300
        ) returns (uint256[] memory amounts3) {
            emit DebugStep("Stablecoin triangle - USDT to USDC successful", amounts3[1]);
        } catch Error(string memory reason) {
            emit DebugStep(string(abi.encodePacked("USDT to USDC failed: ", reason)), 0);
            revert(string(abi.encodePacked("USDT to USDC failed: ", reason)));
        }

        emit DebugStep("Stablecoin triangle - Completed successfully", 1);
    }

    /**
     * 🌀 MULTI-HOP ARBITRAGE WITH ERROR HANDLING (EXTERNAL FOR TRY-CATCH)
     */
    function executeMultiHopArbitrageWithErrorHandling(uint256 amount) external returns (uint256 profit) {
        require(msg.sender == address(this), "Internal only");

        if (amount == 0) {
            emit DebugStep("Multi-hop amount is zero", 0);
            return 0;
        }

        uint256 balanceBefore = USDC.balanceOf(address(this));
        emit DebugStep("Multi-hop - Starting arbitrage", amount);
        emit DebugStep("Multi-hop - Balance before", balanceBefore);

        try this.executeMultiHopSwapWithErrorHandling(amount) {
            uint256 balanceAfter = USDC.balanceOf(address(this));
            profit = balanceAfter > balanceBefore ? balanceAfter - balanceBefore : 0;

            emit DebugStep("Multi-hop - Balance after", balanceAfter);
            emit DebugStep("Multi-hop - Profit", profit);

            if (profit > 0) {
                emit ArbitrageExecuted(
                    address(USDC),
                    address(WMATIC),
                    amount,
                    profit,
                    address(QUICKSWAP),
                    address(SUSHISWAP),
                    "Multi-Hop"
                );
            }
        } catch Error(string memory reason) {
            emit DebugStep(string(abi.encodePacked("Multi-hop arbitrage failed: ", reason)), 0);
            profit = 0;
        } catch {
            emit DebugStep("Multi-hop arbitrage failed: unknown", 0);
            profit = 0;
        }
    }

    /**
     * 🌀 EXECUTE MULTI-HOP SWAP WITH COMPREHENSIVE ERROR HANDLING
     */
    function executeMultiHopSwapWithErrorHandling(uint256 amount) external {
        require(msg.sender == address(this), "Internal only");

        emit DebugStep("Multi-hop swap - Starting", amount);

        // USDC → WETH → WMATIC → USDC WITH ERROR HANDLING
        address[] memory multiPath = new address[](4);
        multiPath[0] = address(USDC);
        multiPath[1] = address(WETH);
        multiPath[2] = address(WMATIC);
        multiPath[3] = address(USDC);

        USDC.approve(address(QUICKSWAP), amount);
        emit DebugStep("Multi-hop swap - USDC approved for QuickSwap", amount);

        try QUICKSWAP.swapExactTokensForTokens(
            amount,
            0,
            multiPath,
            address(this),
            block.timestamp + 300
        ) returns (uint256[] memory amounts) {
            emit DebugStep("Multi-hop swap - Successful", amounts[amounts.length - 1]);
        } catch Error(string memory reason) {
            emit DebugStep(string(abi.encodePacked("Multi-hop swap failed: ", reason)), 0);
            revert(string(abi.encodePacked("Multi-hop swap failed: ", reason)));
        } catch {
            emit DebugStep("Multi-hop swap failed: unknown", 0);
            revert("Multi-hop swap failed: unknown error");
        }

        emit DebugStep("Multi-hop swap - Completed successfully", 1);
    }

    /**
     * 🚨 EMERGENCY FUNCTIONS
     */
    function emergencyWithdraw() external {
        require(msg.sender == PROFIT_WALLET, "Only profit wallet");

        // Withdraw all tokens
        uint256 usdcBalance = USDC.balanceOf(address(this));
        if (usdcBalance > 0) USDC.transfer(PROFIT_WALLET, usdcBalance);

        uint256 wethBalance = WETH.balanceOf(address(this));
        if (wethBalance > 0) WETH.transfer(PROFIT_WALLET, wethBalance);

        uint256 wmaticBalance = WMATIC.balanceOf(address(this));
        if (wmaticBalance > 0) WMATIC.transfer(PROFIT_WALLET, wmaticBalance);

        uint256 wbtcBalance = WBTC.balanceOf(address(this));
        if (wbtcBalance > 0) WBTC.transfer(PROFIT_WALLET, wbtcBalance);

        uint256 daiBalance = DAI.balanceOf(address(this));
        if (daiBalance > 0) DAI.transfer(PROFIT_WALLET, daiBalance);

        uint256 usdtBalance = USDT.balanceOf(address(this));
        if (usdtBalance > 0) USDT.transfer(PROFIT_WALLET, usdtBalance);
    }

    /**
     * 🔧 ADMIN FUNCTIONS
     */
    function toggleDex(address dex) external {
        require(msg.sender == PROFIT_WALLET, "Only admin");
        activeDexes[dex] = !activeDexes[dex];
    }
}
