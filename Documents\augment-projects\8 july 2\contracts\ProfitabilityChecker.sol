// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * 🔍 PROFITABILITY CHECKER
 * Check if arbitrage is profitable BEFORE executing flash loan
 */

interface IERC20 {
    function balanceOf(address account) external view returns (uint256);
}

interface IUniswapV2Router {
    function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts);
}

contract ProfitabilityChecker {
    
    IERC20 public constant USDC = IERC20(******************************************);
    IERC20 public constant WETH = IERC20(******************************************);
    
    // DEX Routers
    IUniswapV2Router public constant QUICKSWAP = IUniswapV2Router(******************************************);
    IUniswapV2Router public constant SUSHISWAP = IUniswapV2Router(******************************************);
    
    uint256 public constant FLASH_AMOUNT = 1000e6; // $1K USDC
    
    event ProfitabilityCheck(
        string dexPair,
        uint256 amountIn,
        uint256 expectedOut,
        int256 profit,
        bool profitable
    );
    
    event DetailedCheck(
        string step,
        address dex,
        uint256 amountIn,
        uint256 amountOut,
        uint256 price
    );
    
    /**
     * 🔍 CHECK ALL ARBITRAGE OPPORTUNITIES
     */
    function checkAllArbitrageOpportunities() external returns (bool anyProfitable) {
        uint256 amount = FLASH_AMOUNT;
        
        // Check QuickSwap round-trip
        bool quickProfitable = checkRoundTripProfitability("QuickSwap", QUICKSWAP, amount);
        
        // Check SushiSwap round-trip  
        bool sushiProfitable = checkRoundTripProfitability("SushiSwap", SUSHISWAP, amount);
        
        // Check cross-DEX arbitrage
        bool crossProfitable1 = checkCrossDexArbitrage("Quick-to-Sushi", QUICKSWAP, SUSHISWAP, amount);
        bool crossProfitable2 = checkCrossDexArbitrage("Sushi-to-Quick", SUSHISWAP, QUICKSWAP, amount);
        
        anyProfitable = quickProfitable || sushiProfitable || crossProfitable1 || crossProfitable2;
        
        return anyProfitable;
    }
    
    /**
     * 🔄 CHECK ROUND-TRIP PROFITABILITY (USDC → WETH → USDC)
     */
    function checkRoundTripProfitability(
        string memory dexName,
        IUniswapV2Router router,
        uint256 amount
    ) public returns (bool profitable) {
        
        // Step 1: USDC → WETH
        address[] memory path1 = new address[](2);
        path1[0] = address(USDC);
        path1[1] = address(WETH);
        
        uint256[] memory amounts1 = router.getAmountsOut(amount, path1);
        uint256 wethOut = amounts1[1];
        
        emit DetailedCheck("USDC-to-WETH", address(router), amount, wethOut, (wethOut * 1e18) / amount);
        
        // Step 2: WETH → USDC
        address[] memory path2 = new address[](2);
        path2[0] = address(WETH);
        path2[1] = address(USDC);
        
        uint256[] memory amounts2 = router.getAmountsOut(wethOut, path2);
        uint256 usdcOut = amounts2[1];
        
        emit DetailedCheck("WETH-to-USDC", address(router), wethOut, usdcOut, (usdcOut * 1e18) / wethOut);
        
        // Calculate profit/loss
        int256 profit = int256(usdcOut) - int256(amount);
        profitable = profit > 0;
        
        emit ProfitabilityCheck(dexName, amount, usdcOut, profit, profitable);
        
        return profitable;
    }
    
    /**
     * 🔀 CHECK CROSS-DEX ARBITRAGE
     */
    function checkCrossDexArbitrage(
        string memory pairName,
        IUniswapV2Router router1,
        IUniswapV2Router router2,
        uint256 amount
    ) public returns (bool profitable) {
        
        // Step 1: USDC → WETH on router1
        address[] memory path1 = new address[](2);
        path1[0] = address(USDC);
        path1[1] = address(WETH);
        
        uint256[] memory amounts1 = router1.getAmountsOut(amount, path1);
        uint256 wethOut = amounts1[1];
        
        emit DetailedCheck("Cross-USDC-to-WETH", address(router1), amount, wethOut, (wethOut * 1e18) / amount);

        // Step 2: WETH → USDC on router2
        address[] memory path2 = new address[](2);
        path2[0] = address(WETH);
        path2[1] = address(USDC);

        uint256[] memory amounts2 = router2.getAmountsOut(wethOut, path2);
        uint256 usdcOut = amounts2[1];

        emit DetailedCheck("Cross-WETH-to-USDC", address(router2), wethOut, usdcOut, (usdcOut * 1e18) / wethOut);
        
        // Calculate profit/loss
        int256 profit = int256(usdcOut) - int256(amount);
        profitable = profit > 0;
        
        emit ProfitabilityCheck(pairName, amount, usdcOut, profit, profitable);
        
        return profitable;
    }
    
    /**
     * 📊 GET CURRENT PRICES
     */
    function getCurrentPrices() external view returns (
        uint256 quickUsdcToWeth,
        uint256 quickWethToUsdc,
        uint256 sushiUsdcToWeth,
        uint256 sushiWethToUsdc
    ) {
        uint256 testAmount = 1000e6; // $1K USDC
        
        // QuickSwap prices
        address[] memory path1 = new address[](2);
        path1[0] = address(USDC);
        path1[1] = address(WETH);
        uint256[] memory amounts1 = QUICKSWAP.getAmountsOut(testAmount, path1);
        quickUsdcToWeth = amounts1[1];
        
        address[] memory path2 = new address[](2);
        path2[0] = address(WETH);
        path2[1] = address(USDC);
        uint256[] memory amounts2 = QUICKSWAP.getAmountsOut(amounts1[1], path2);
        quickWethToUsdc = amounts2[1];
        
        // SushiSwap prices
        uint256[] memory amounts3 = SUSHISWAP.getAmountsOut(testAmount, path1);
        sushiUsdcToWeth = amounts3[1];
        
        uint256[] memory amounts4 = SUSHISWAP.getAmountsOut(amounts3[1], path2);
        sushiWethToUsdc = amounts4[1];
        
        return (quickUsdcToWeth, quickWethToUsdc, sushiUsdcToWeth, sushiWethToUsdc);
    }
    
    /**
     * 💰 CALCULATE POTENTIAL PROFIT
     */
    function calculatePotentialProfit(uint256 amount) external view returns (
        int256 quickRoundTrip,
        int256 sushiRoundTrip,
        int256 quickToSushi,
        int256 sushiToQuick
    ) {
        // QuickSwap round-trip
        address[] memory path = new address[](2);
        path[0] = address(USDC);
        path[1] = address(WETH);
        uint256[] memory amounts1 = QUICKSWAP.getAmountsOut(amount, path);
        
        path[0] = address(WETH);
        path[1] = address(USDC);
        uint256[] memory amounts2 = QUICKSWAP.getAmountsOut(amounts1[1], path);
        quickRoundTrip = int256(amounts2[1]) - int256(amount);
        
        // SushiSwap round-trip
        path[0] = address(USDC);
        path[1] = address(WETH);
        uint256[] memory amounts3 = SUSHISWAP.getAmountsOut(amount, path);
        
        path[0] = address(WETH);
        path[1] = address(USDC);
        uint256[] memory amounts4 = SUSHISWAP.getAmountsOut(amounts3[1], path);
        sushiRoundTrip = int256(amounts4[1]) - int256(amount);
        
        // Cross-DEX arbitrage
        path[0] = address(USDC);
        path[1] = address(WETH);
        uint256[] memory amounts5 = QUICKSWAP.getAmountsOut(amount, path);
        
        path[0] = address(WETH);
        path[1] = address(USDC);
        uint256[] memory amounts6 = SUSHISWAP.getAmountsOut(amounts5[1], path);
        quickToSushi = int256(amounts6[1]) - int256(amount);
        
        // Reverse
        path[0] = address(USDC);
        path[1] = address(WETH);
        uint256[] memory amounts7 = SUSHISWAP.getAmountsOut(amount, path);
        
        path[0] = address(WETH);
        path[1] = address(USDC);
        uint256[] memory amounts8 = QUICKSWAP.getAmountsOut(amounts7[1], path);
        sushiToQuick = int256(amounts8[1]) - int256(amount);
        
        return (quickRoundTrip, sushiRoundTrip, quickToSushi, sushiToQuick);
    }
    
    /**
     * 🎯 CHECK SPECIFIC AMOUNT PROFITABILITY
     */
    function checkSpecificAmount(uint256 amount) external returns (bool anyProfitable) {
        // Check QuickSwap round-trip
        bool quickProfitable = checkRoundTripProfitability("QuickSwap", QUICKSWAP, amount);

        // Check SushiSwap round-trip
        bool sushiProfitable = checkRoundTripProfitability("SushiSwap", SUSHISWAP, amount);

        // Check cross-DEX arbitrage
        bool crossProfitable1 = checkCrossDexArbitrage("Quick-to-Sushi", QUICKSWAP, SUSHISWAP, amount);
        bool crossProfitable2 = checkCrossDexArbitrage("Sushi-to-Quick", SUSHISWAP, QUICKSWAP, amount);

        anyProfitable = quickProfitable || sushiProfitable || crossProfitable1 || crossProfitable2;

        return anyProfitable;
    }
}
