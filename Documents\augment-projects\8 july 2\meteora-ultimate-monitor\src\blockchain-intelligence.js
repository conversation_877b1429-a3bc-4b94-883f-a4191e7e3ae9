// 🧠 BLOCKCHAIN INTELLIGENCE - The ultimate on-chain analysis system
import axios from 'axios';
import { CONFIG } from './config.js';

class BlockchainIntelligence {
  constructor() {
    this.solanaRPC = 'https://api.mainnet-beta.solana.com';
    this.heliusAPI = 'https://api.helius.xyz/v0';
    this.transactionCache = new Map();
    this.walletProfiles = new Map();
    this.smartContracts = new Map();
    this.profitLeaderboard = new Map();
    this.lastProcessedSlot = 0;
  }

  // 🔍 MONITOR ALL METEORA TRANSACTIONS IN REAL-TIME
  async startTransactionMonitoring() {
    console.log('🧠 Starting Blockchain Intelligence Network...');
    
    // Monitor new transactions every 10 seconds
    setInterval(async () => {
      await this.scanRecentTransactions();
    }, 10000);
    
    // Update profit leaderboard every 30 minutes
    setInterval(async () => {
      await this.updateProfitLeaderboard();
    }, 30 * 60 * 1000);
    
    // Scan for new smart contracts every 5 minutes
    setInterval(async () => {
      await this.scanNewSmartContracts();
    }, 5 * 60 * 1000);
  }

  // 📊 SCAN RECENT TRANSACTIONS
  async scanRecentTransactions() {
    try {
      console.log('🔍 Scanning recent Meteora transactions...');
      
      // Get recent transactions from Helius API
      const transactions = await this.getRecentTransactions();
      
      for (const tx of transactions) {
        await this.analyzeTransaction(tx);
      }
      
      console.log(`✅ Analyzed ${transactions.length} transactions`);
    } catch (error) {
      console.error('❌ Error scanning transactions:', error.message);
    }
  }

  // 🔍 GET RECENT TRANSACTIONS
  async getRecentTransactions() {
    try {
      // Use Helius API to get recent transactions
      const response = await axios.post(`${this.heliusAPI}/transactions`, {
        jsonrpc: '2.0',
        id: 'meteora-intelligence',
        method: 'getSignaturesForAddress',
        params: [
          'LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo', // Meteora DLMM program
          {
            limit: 100,
            before: null
          }
        ]
      });
      
      if (response.data && response.data.result) {
        return response.data.result;
      }
      
      return [];
    } catch (error) {
      console.error('❌ Error fetching transactions:', error.message);
      return [];
    }
  }

  // 🧠 ANALYZE INDIVIDUAL TRANSACTION
  async analyzeTransaction(txSignature) {
    try {
      // Get detailed transaction data
      const txData = await this.getTransactionDetails(txSignature.signature);
      
      if (!txData) return;
      
      const analysis = {
        signature: txSignature.signature,
        timestamp: Date.now(),
        slot: txData.slot,
        
        // Extract key information
        wallets: this.extractWallets(txData),
        amounts: this.extractAmounts(txData),
        tokens: this.extractTokens(txData),
        programs: this.extractPrograms(txData),
        
        // Calculate profit/loss
        profitLoss: this.calculateProfitLoss(txData),
        
        // Detect patterns
        strategy: this.detectStrategy(txData),
        riskLevel: this.assessRisk(txData),
        
        // Smart money indicators
        isWhale: this.isWhaleTransaction(txData),
        isArbitrage: this.isArbitrageTransaction(txData),
        isNewStrategy: this.isNewStrategy(txData)
      };
      
      // Store analysis
      this.transactionCache.set(txSignature.signature, analysis);
      
      // Update wallet profiles
      this.updateWalletProfiles(analysis);
      
      // Check for alerts
      this.checkTransactionAlerts(analysis);
      
      return analysis;
    } catch (error) {
      console.error(`❌ Error analyzing transaction ${txSignature.signature}:`, error.message);
      return null;
    }
  }

  // 📋 GET TRANSACTION DETAILS
  async getTransactionDetails(signature) {
    try {
      const response = await axios.post(this.solanaRPC, {
        jsonrpc: '2.0',
        id: 1,
        method: 'getTransaction',
        params: [
          signature,
          {
            encoding: 'jsonParsed',
            maxSupportedTransactionVersion: 0
          }
        ]
      });
      
      return response.data.result;
    } catch (error) {
      console.error(`❌ Error fetching transaction details:`, error.message);
      return null;
    }
  }

  // 💰 EXTRACT WALLETS FROM TRANSACTION
  extractWallets(txData) {
    const wallets = new Set();
    
    if (txData.transaction && txData.transaction.message) {
      const accounts = txData.transaction.message.accountKeys;
      accounts.forEach(account => {
        if (typeof account === 'string') {
          wallets.add(account);
        } else if (account.pubkey) {
          wallets.add(account.pubkey);
        }
      });
    }
    
    return Array.from(wallets);
  }

  // 💵 EXTRACT AMOUNTS FROM TRANSACTION
  extractAmounts(txData) {
    const amounts = [];
    
    if (txData.meta && txData.meta.preBalances && txData.meta.postBalances) {
      for (let i = 0; i < txData.meta.preBalances.length; i++) {
        const preBalance = txData.meta.preBalances[i];
        const postBalance = txData.meta.postBalances[i];
        const change = postBalance - preBalance;
        
        if (Math.abs(change) > 0) {
          amounts.push({
            account: i,
            preBalance,
            postBalance,
            change,
            changeSOL: change / 1e9 // Convert lamports to SOL
          });
        }
      }
    }
    
    return amounts;
  }

  // 🪙 EXTRACT TOKENS FROM TRANSACTION
  extractTokens(txData) {
    const tokens = new Set();
    
    if (txData.meta && txData.meta.preTokenBalances) {
      txData.meta.preTokenBalances.forEach(balance => {
        if (balance.mint) {
          tokens.add(balance.mint);
        }
      });
    }
    
    if (txData.meta && txData.meta.postTokenBalances) {
      txData.meta.postTokenBalances.forEach(balance => {
        if (balance.mint) {
          tokens.add(balance.mint);
        }
      });
    }
    
    return Array.from(tokens);
  }

  // 🔧 EXTRACT PROGRAMS FROM TRANSACTION
  extractPrograms(txData) {
    const programs = new Set();
    
    if (txData.transaction && txData.transaction.message && txData.transaction.message.instructions) {
      txData.transaction.message.instructions.forEach(instruction => {
        if (instruction.programId) {
          programs.add(instruction.programId);
        }
      });
    }
    
    return Array.from(programs);
  }

  // 💰 CALCULATE PROFIT/LOSS
  calculateProfitLoss(txData) {
    let totalProfitSOL = 0;
    let totalProfitUSD = 0;
    
    if (txData.meta && txData.meta.preBalances && txData.meta.postBalances) {
      for (let i = 0; i < txData.meta.preBalances.length; i++) {
        const change = txData.meta.postBalances[i] - txData.meta.preBalances[i];
        totalProfitSOL += change / 1e9; // Convert to SOL
      }
    }
    
    // Estimate USD value (you'd want to get real SOL price)
    const solPriceUSD = 100; // Placeholder - get real price
    totalProfitUSD = totalProfitSOL * solPriceUSD;
    
    return {
      solAmount: totalProfitSOL,
      usdAmount: totalProfitUSD,
      isProfitable: totalProfitUSD > 0
    };
  }

  // 🎯 DETECT STRATEGY
  detectStrategy(txData) {
    const programs = this.extractPrograms(txData);
    const tokens = this.extractTokens(txData);
    
    // Check for common DeFi patterns
    if (programs.includes('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo')) {
      if (tokens.length >= 2) {
        return 'DLMM_LIQUIDITY_PROVISION';
      }
      return 'DLMM_SWAP';
    }
    
    if (programs.length > 3 && tokens.length > 2) {
      return 'ARBITRAGE';
    }
    
    if (tokens.length === 1) {
      return 'SINGLE_TOKEN_OPERATION';
    }
    
    return 'UNKNOWN';
  }

  // ⚠️ ASSESS RISK
  assessRisk(txData) {
    const amounts = this.extractAmounts(txData);
    const maxChange = Math.max(...amounts.map(a => Math.abs(a.changeSOL)));
    
    if (maxChange > 100) return 'HIGH_RISK';
    if (maxChange > 10) return 'MEDIUM_RISK';
    return 'LOW_RISK';
  }

  // 🐋 IS WHALE TRANSACTION
  isWhaleTransaction(txData) {
    const amounts = this.extractAmounts(txData);
    const maxChange = Math.max(...amounts.map(a => Math.abs(a.changeSOL)));
    
    return maxChange > 50; // 50+ SOL movement = whale
  }

  // 🔄 IS ARBITRAGE TRANSACTION
  isArbitrageTransaction(txData) {
    const programs = this.extractPrograms(txData);
    const tokens = this.extractTokens(txData);
    
    // Multiple programs + multiple tokens = likely arbitrage
    return programs.length >= 3 && tokens.length >= 2;
  }

  // 🆕 IS NEW STRATEGY
  isNewStrategy(txData) {
    const strategy = this.detectStrategy(txData);
    const programs = this.extractPrograms(txData);
    
    // Check if we've seen this program combination before
    const programSignature = programs.sort().join('|');
    
    if (!this.smartContracts.has(programSignature)) {
      this.smartContracts.set(programSignature, {
        firstSeen: Date.now(),
        programs,
        strategy,
        count: 1
      });
      return true;
    }
    
    this.smartContracts.get(programSignature).count++;
    return false;
  }

  // 👤 UPDATE WALLET PROFILES
  updateWalletProfiles(analysis) {
    analysis.wallets.forEach(wallet => {
      if (!this.walletProfiles.has(wallet)) {
        this.walletProfiles.set(wallet, {
          address: wallet,
          firstSeen: Date.now(),
          transactionCount: 0,
          totalProfitSOL: 0,
          totalProfitUSD: 0,
          strategies: new Set(),
          riskProfile: 'UNKNOWN',
          isWhale: false
        });
      }
      
      const profile = this.walletProfiles.get(wallet);
      profile.transactionCount++;
      profile.totalProfitSOL += analysis.profitLoss.solAmount;
      profile.totalProfitUSD += analysis.profitLoss.usdAmount;
      profile.strategies.add(analysis.strategy);
      
      if (analysis.isWhale) {
        profile.isWhale = true;
      }
      
      // Update risk profile
      if (analysis.riskLevel === 'HIGH_RISK') {
        profile.riskProfile = 'HIGH_RISK';
      } else if (analysis.riskLevel === 'MEDIUM_RISK' && profile.riskProfile !== 'HIGH_RISK') {
        profile.riskProfile = 'MEDIUM_RISK';
      } else if (profile.riskProfile === 'UNKNOWN') {
        profile.riskProfile = 'LOW_RISK';
      }
    });
  }

  // 🚨 CHECK TRANSACTION ALERTS
  checkTransactionAlerts(analysis) {
    const alerts = [];
    
    // Whale transaction alert
    if (analysis.isWhale) {
      alerts.push({
        type: 'WHALE_TRANSACTION',
        message: `🐋 WHALE DETECTED: ${Math.abs(analysis.profitLoss.usdAmount).toFixed(2)} USD movement`,
        priority: 'HIGH'
      });
    }
    
    // Large profit alert
    if (analysis.profitLoss.usdAmount > 1000) {
      alerts.push({
        type: 'LARGE_PROFIT',
        message: `💰 BIG WIN: $${analysis.profitLoss.usdAmount.toFixed(2)} profit detected`,
        priority: 'HIGH'
      });
    }
    
    // New strategy alert
    if (analysis.isNewStrategy) {
      alerts.push({
        type: 'NEW_STRATEGY',
        message: `🆕 NEW STRATEGY: ${analysis.strategy} detected`,
        priority: 'MEDIUM'
      });
    }
    
    // Arbitrage alert
    if (analysis.isArbitrage) {
      alerts.push({
        type: 'ARBITRAGE_DETECTED',
        message: `🔄 ARBITRAGE: Multi-protocol transaction detected`,
        priority: 'MEDIUM'
      });
    }
    
    // Log alerts
    alerts.forEach(alert => {
      console.log(`🚨 ${alert.message}`);
    });
    
    return alerts;
  }

  // 🏆 UPDATE PROFIT LEADERBOARD
  async updateProfitLeaderboard() {
    console.log('🏆 Updating profit leaderboard...');
    
    // Sort wallets by profit
    const sortedWallets = Array.from(this.walletProfiles.values())
      .filter(profile => profile.totalProfitUSD > 0)
      .sort((a, b) => b.totalProfitUSD - a.totalProfitUSD)
      .slice(0, 50); // Top 50
    
    console.log('🏆 TOP PROFIT MAKERS (Last 30 minutes):');
    sortedWallets.slice(0, 10).forEach((wallet, index) => {
      console.log(`${index + 1}. ${wallet.address.substring(0, 8)}... - $${wallet.totalProfitUSD.toFixed(2)} (${wallet.transactionCount} txs)`);
    });
    
    return sortedWallets;
  }

  // 🔍 SCAN NEW SMART CONTRACTS
  async scanNewSmartContracts() {
    console.log('🔍 Scanning for new smart contracts...');
    
    const newContracts = Array.from(this.smartContracts.values())
      .filter(contract => Date.now() - contract.firstSeen < 5 * 60 * 1000) // Last 5 minutes
      .sort((a, b) => b.count - a.count);
    
    if (newContracts.length > 0) {
      console.log('🆕 NEW SMART CONTRACTS DETECTED:');
      newContracts.forEach(contract => {
        console.log(`• Strategy: ${contract.strategy}, Usage: ${contract.count} times`);
        console.log(`  Programs: ${contract.programs.slice(0, 2).join(', ')}...`);
      });
    }
    
    return newContracts;
  }

  // 📊 GET INTELLIGENCE SUMMARY
  getIntelligenceSummary() {
    const totalWallets = this.walletProfiles.size;
    const totalTransactions = this.transactionCache.size;
    const whaleWallets = Array.from(this.walletProfiles.values()).filter(w => w.isWhale).length;
    const totalProfitUSD = Array.from(this.walletProfiles.values()).reduce((sum, w) => sum + w.totalProfitUSD, 0);
    
    return {
      totalWallets,
      totalTransactions,
      whaleWallets,
      totalProfitUSD,
      newStrategies: this.smartContracts.size,
      lastUpdate: Date.now()
    };
  }
}

export default BlockchainIntelligence;
