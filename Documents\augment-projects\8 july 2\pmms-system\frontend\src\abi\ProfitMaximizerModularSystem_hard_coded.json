[{"inputs": [{"internalType": "address", "name": "_registry", "type": "address"}, {"internalType": "address", "name": "_flashloanExecutor", "type": "address"}, {"internalType": "address", "name": "_owner", "type": "address"}], "name": "initialize", "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "address", "name": "asset", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "bytes", "name": "params", "type": "bytes"}], "name": "executeStrategy", "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "address", "name": "strategyAddress", "type": "address"}], "name": "addStrategy", "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}], "name": "removeStrategy", "stateMutability": "nonpayable", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "string", "name": "name", "type": "string"}, {"indexed": true, "internalType": "address", "name": "strategy", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "success", "type": "bool"}], "name": "StrategyExecuted", "type": "event"}]