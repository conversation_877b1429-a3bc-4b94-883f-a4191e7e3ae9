const { ethers } = require('hardhat');

/**
 * 🚀 TEST SUPER SIMPLE FLASH LOAN
 * Just test if flash loan mechanism works
 */

async function testSuperSimple() {
  console.log('\n🚀 TESTING SUPER SIMPLE FLASH LOAN');
  console.log('💰 JUST TESTING FLASH LOAN RECEPTION');
  console.log('⚡ NO STRATEGY - JUST RECEIVE AND REPAY');
  console.log('🎯 WILL SHOW IF BALANCER FLASH LOAN WORKS!');
  console.log('=' .repeat(80));

  try {
    const [deployer] = await ethers.getSigners();
    const currentGasPrice = await ethers.provider.getGasPrice();
    const highGasPrice = currentGasPrice.mul(200).div(100);
    
    console.log(`👤 Deployer: ${deployer.address}`);
    console.log(`💰 Balance: ${ethers.utils.formatEther(await deployer.getBalance())} POL`);

    console.log('\n🔥 DEPLOYING SUPER SIMPLE CONTRACT...');
    
    const SuperSimpleFlashLoan = await ethers.getContractFactory('SuperSimpleFlashLoan');
    const superSimple = await SuperSimpleFlashLoan.deploy({
      gasLimit: 1000000,
      gasPrice: highGasPrice
    });

    await superSimple.deployed();
    console.log(`✅ Contract deployed: ${superSimple.address}`);

    console.log('\n🚀 EXECUTING SUPER SIMPLE FLASH LOAN...');
    
    const executionTx = await superSimple.executeSuperSimpleFlashLoan({
      gasLimit: 1000000,
      gasPrice: highGasPrice
    });
    
    console.log(`📋 Execution TX: ${executionTx.hash}`);
    const execReceipt = await executionTx.wait();
    
    console.log(`\n📊 EXECUTION RESULTS:`);
    console.log(`   Status: ${execReceipt.status === 1 ? '✅ SUCCESS' : '❌ FAILED'}`);
    console.log(`   Gas Used: ${execReceipt.gasUsed.toLocaleString()}`);
    
    // Parse events
    console.log('\n🔍 FLASH LOAN EVENTS:');
    for (const log of execReceipt.logs) {
      try {
        const event = superSimple.interface.parseLog(log);
        if (event.name === 'FlashLoanReceived') {
          console.log(`   💰 Received: ${ethers.utils.formatUnits(event.args.amount, 6)} USDC`);
        } else if (event.name === 'FlashLoanRepaid') {
          console.log(`   💸 Repaid: ${ethers.utils.formatUnits(event.args.amount, 6)} USDC`);
        } else if (event.name === 'FlashLoanResult') {
          console.log(`   🎯 Success: ${event.args.success}`);
        }
      } catch {
        // Skip unparseable logs
      }
    }
    
    const results = await superSimple.getResults();
    
    console.log(`\n💰 FINAL RESULTS:`);
    console.log(`   Received: ${ethers.utils.formatUnits(results.received, 6)} USDC`);
    console.log(`   Final Balance: ${ethers.utils.formatUnits(results.finalBal, 6)} USDC`);
    console.log(`   Success: ${results.success ? '✅' : '❌'}`);
    
    if (results.success) {
      console.log('\n🎉🎉🎉 FLASH LOAN MECHANISM WORKS! 🎉🎉🎉');
      console.log('💰 BALANCER FLASH LOAN CONFIRMED WORKING!');
      console.log('🔥 ZERO UPFRONT CAPITAL VALIDATED!');
      console.log('⚡ READY TO ADD PROFITABLE STRATEGY!');
      
      console.log('\n🚀 NEXT STEPS:');
      console.log('1. ✅ Flash loan mechanism works');
      console.log('2. 🔧 Add conservative Aave strategy');
      console.log('3. 💰 Start with smaller amounts');
      console.log('4. 🚀 Scale to massive profits');
      
      return {
        success: true,
        flashLoanWorks: true
      };
      
    } else {
      console.log('\n🔧 FLASH LOAN MECHANISM FAILED');
      console.log('💡 ISSUE WITH BALANCER INTEGRATION');
      
      return {
        success: false,
        flashLoanWorks: false
      };
    }
    
  } catch (error) {
    console.error('\n💥 SUPER SIMPLE TEST FAILED:', error.message);
    
    if (error.reason) {
      console.log(`💥 Revert Reason: ${error.reason}`);
    }
    
    return {
      success: false,
      error: error.message
    };
  }
}

// Execute test
if (require.main === module) {
  testSuperSimple()
    .then((result) => {
      console.log('\n🎉 SUPER SIMPLE TEST COMPLETED!');
      if (result.success) {
        console.log('💰 FLASH LOAN MECHANISM WORKS!');
      } else {
        console.log('🔧 Found flash loan issue');
      }
    })
    .catch((error) => {
      console.error('Test failed:', error.message);
    });
}

module.exports = { testSuperSimple };
