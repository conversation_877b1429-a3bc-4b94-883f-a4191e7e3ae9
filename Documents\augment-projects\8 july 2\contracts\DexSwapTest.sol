// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * 🔍 DEX SWAP TEST
 * Test individual DEX swaps to find the exact issue
 */

interface IERC20 {
    function transfer(address to, uint256 amount) external returns (bool);
    function balanceOf(address account) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
}

interface IBalancerVault {
    function flashLoan(address recipient, address[] memory tokens, uint256[] memory amounts, bytes memory userData) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(address[] memory tokens, uint256[] memory amounts, uint256[] memory feeAmounts, bytes memory userData) external;
}

interface IUniswapV2Router {
    function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts);
    function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts);
}

contract DexSwapTest is IFlashLoanRecipient {
    
    IERC20 public constant USDC = IERC20(******************************************);
    IERC20 public constant WETH = IERC20(******************************************);
    IBalancerVault public constant BALANCER = IBalancerVault(******************************************);
    
    // Test with QuickSwap (most reliable DEX)
    IUniswapV2Router public constant QUICKSWAP = IUniswapV2Router(******************************************);
    
    address public constant PROFIT_WALLET = ******************************************;
    uint256 public constant FLASH_AMOUNT = 1000e6; // $1K USDC
    
    uint256 public lastProfit;
    bool public lastSuccess;
    uint256 public totalExecutions;
    
    event FlashLoanResult(uint256 profit, bool success, uint256 execution);
    event DebugStep(string step, uint256 value);
    event SwapResult(string step, uint256 amountIn, uint256 amountOut, address token);
    
    /**
     * 🚀 EXECUTE DEX SWAP TEST
     */
    function executeDexSwapTest() external {
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        
        tokens[0] = address(USDC);
        amounts[0] = FLASH_AMOUNT;
        
        emit DebugStep("Starting DEX swap test", FLASH_AMOUNT);
        BALANCER.flashLoan(address(this), tokens, amounts, "");
    }
    
    /**
     * 🎯 FLASH LOAN CALLBACK - DEX SWAP TEST
     */
    function receiveFlashLoan(
        address[] memory,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory
    ) external override {
        require(msg.sender == address(BALANCER), "Only Balancer");
        
        uint256 flashAmount = amounts[0];
        uint256 initialBalance = USDC.balanceOf(address(this));
        
        emit DebugStep("Flash loan received", initialBalance);
        require(initialBalance >= flashAmount, "Flash loan not received");
        require(feeAmounts[0] == 0, "Expected 0% fee");
        
        // TEST DEX SWAP: USDC → WETH → USDC
        testDexSwap(flashAmount);
        
        // Repay flash loan
        uint256 balanceBeforeRepay = USDC.balanceOf(address(this));
        emit DebugStep("Balance before repay", balanceBeforeRepay);
        
        USDC.transfer(address(BALANCER), flashAmount);
        emit DebugStep("Flash loan repaid", flashAmount);
        
        // Extract any remaining balance
        uint256 finalBalance = USDC.balanceOf(address(this));
        totalExecutions++;
        
        if (finalBalance > 0) {
            lastProfit = finalBalance;
            USDC.transfer(PROFIT_WALLET, finalBalance);
            lastSuccess = true;
        } else {
            lastProfit = 0;
            lastSuccess = true; // Success even with no profit
        }
        
        emit FlashLoanResult(lastProfit, lastSuccess, totalExecutions);
        emit DebugStep("DEX swap test completed", finalBalance);
    }
    
    /**
     * 🔄 TEST DEX SWAP
     */
    function testDexSwap(uint256 amount) internal {
        emit DebugStep("Starting DEX swap test", amount);
        
        // Use smaller amount for testing (50% of flash loan)
        uint256 swapAmount = amount / 2;
        
        try this.executeSafeSwap(swapAmount) {
            emit DebugStep("DEX swap completed successfully", 1);
        } catch Error(string memory reason) {
            emit DebugStep("DEX swap failed", 0);
            // Log the reason but don't revert the flash loan
        } catch {
            emit DebugStep("DEX swap failed unknown", 0);
        }
    }
    
    /**
     * 🔄 EXECUTE SAFE SWAP (EXTERNAL FOR TRY-CATCH)
     */
    function executeSafeSwap(uint256 amount) external {
        require(msg.sender == address(this), "Internal only");
        
        emit DebugStep("Executing safe swap", amount);
        
        // Step 1: Check QuickSwap price
        address[] memory path = new address[](2);
        path[0] = address(USDC);
        path[1] = address(WETH);
        
        uint256[] memory amountsOut;
        try QUICKSWAP.getAmountsOut(amount, path) returns (uint256[] memory _amountsOut) {
            amountsOut = _amountsOut;
            emit DebugStep("QuickSwap price check", amountsOut[1]);
        } catch {
            emit DebugStep("QuickSwap price check failed", 0);
            revert("Price check failed");
        }
        
        // Step 2: Approve USDC for QuickSwap
        emit DebugStep("Approving USDC for QuickSwap", amount);
        USDC.approve(address(QUICKSWAP), amount);
        
        // Step 3: Swap USDC → WETH
        uint256 usdcBefore = USDC.balanceOf(address(this));
        uint256 wethBefore = WETH.balanceOf(address(this));
        
        emit DebugStep("USDC before swap", usdcBefore);
        emit DebugStep("WETH before swap", wethBefore);
        
        uint256[] memory amounts1;
        try QUICKSWAP.swapExactTokensForTokens(
            amount,
            (amountsOut[1] * 95) / 100, // 5% slippage tolerance
            path,
            address(this),
            block.timestamp + 300
        ) returns (uint256[] memory _amounts1) {
            amounts1 = _amounts1;
            emit SwapResult("USDC to WETH", amounts1[0], amounts1[1], address(WETH));
        } catch Error(string memory reason) {
            emit DebugStep("USDC to WETH swap failed", 0);
            revert(string(abi.encodePacked("USDC to WETH failed: ", reason)));
        }
        
        uint256 wethReceived = amounts1[1];
        uint256 usdcAfterFirst = USDC.balanceOf(address(this));
        uint256 wethAfterFirst = WETH.balanceOf(address(this));
        
        emit DebugStep("USDC after first swap", usdcAfterFirst);
        emit DebugStep("WETH after first swap", wethAfterFirst);
        emit DebugStep("WETH received", wethReceived);
        
        // Step 4: Swap WETH → USDC (reverse)
        address[] memory reversePath = new address[](2);
        reversePath[0] = address(WETH);
        reversePath[1] = address(USDC);
        
        // Check reverse price
        uint256[] memory reverseAmountsOut;
        try QUICKSWAP.getAmountsOut(wethReceived, reversePath) returns (uint256[] memory _reverseAmountsOut) {
            reverseAmountsOut = _reverseAmountsOut;
            emit DebugStep("Reverse price check", reverseAmountsOut[1]);
        } catch {
            emit DebugStep("Reverse price check failed", 0);
            revert("Reverse price check failed");
        }
        
        // Approve WETH for QuickSwap
        emit DebugStep("Approving WETH for QuickSwap", wethReceived);
        WETH.approve(address(QUICKSWAP), wethReceived);
        
        uint256[] memory amounts2;
        try QUICKSWAP.swapExactTokensForTokens(
            wethReceived,
            (reverseAmountsOut[1] * 95) / 100, // 5% slippage tolerance
            reversePath,
            address(this),
            block.timestamp + 300
        ) returns (uint256[] memory _amounts2) {
            amounts2 = _amounts2;
            emit SwapResult("WETH to USDC", amounts2[0], amounts2[1], address(USDC));
        } catch Error(string memory reason) {
            emit DebugStep("WETH to USDC swap failed", 0);
            revert(string(abi.encodePacked("WETH to USDC failed: ", reason)));
        }
        
        uint256 usdcFinal = USDC.balanceOf(address(this));
        uint256 wethFinal = WETH.balanceOf(address(this));
        
        emit DebugStep("USDC final", usdcFinal);
        emit DebugStep("WETH final", wethFinal);
        
        // Calculate profit/loss
        if (usdcFinal > usdcBefore) {
            uint256 profit = usdcFinal - usdcBefore;
            emit DebugStep("Swap profit", profit);
        } else {
            uint256 loss = usdcBefore - usdcFinal;
            emit DebugStep("Swap loss", loss);
        }
        
        emit DebugStep("DEX swap test completed", 1);
    }
    
    /**
     * 📊 VIEW FUNCTIONS
     */
    function getResults() external view returns (uint256 profit, bool success, uint256 executions) {
        return (lastProfit, lastSuccess, totalExecutions);
    }
}
