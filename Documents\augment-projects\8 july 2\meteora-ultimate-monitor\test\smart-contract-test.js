// 🧪 SMART CONTRACT TESTING SUITE
// Comprehensive tests for the Meteora Gold Extractor Intelligence System

import SmartContractIntelligence from '../src/smart-contract-client.js';
import { expect } from 'chai';

describe('🧠 Meteora Gold Extractor Intelligence System', () => {
  let smartContract;
  
  beforeEach(async () => {
    smartContract = new SmartContractIntelligence();
    // Initialize with fallback data for testing
    smartContract.isInitialized = false; // Force fallback mode for testing
  });

  describe('🏆 Gold Opportunity Detection', () => {
    it('should retrieve gold opportunities with enhanced metrics', async () => {
      const goldPools = await smartContract.getTopPools();
      
      expect(goldPools).to.be.an('array');
      expect(goldPools.length).to.be.greaterThan(0);
      
      const topPool = goldPools[0];
      expect(topPool).to.have.property('goldScore');
      expect(topPool).to.have.property('profitPotential');
      expect(topPool).to.have.property('entryConfidence');
      expect(topPool).to.have.property('optimalEntrySize');
      expect(topPool).to.have.property('successProbability');
      
      console.log(`✅ Top Gold Pool: ${topPool.tokenX}/${topPool.tokenY}`);
      console.log(`   Gold Score: ${topPool.goldScore}`);
      console.log(`   Profit Potential: ${(topPool.profitPotential * 100).toFixed(1)}%`);
      console.log(`   Success Probability: ${(topPool.successProbability * 100).toFixed(1)}%`);
    });

    it('should filter opportunities by criteria', async () => {
      const criteria = {
        minGoldScore: 50,
        minSuccessProbability: 0.8,
        minProfitPotential: 0.5,
        maxRisk: 'MEDIUM'
      };
      
      const filteredPools = await smartContract.findBestPools(criteria);
      
      expect(filteredPools).to.be.an('array');
      
      filteredPools.forEach(pool => {
        expect(pool.goldScore).to.be.at.least(criteria.minGoldScore);
        expect(pool.successProbability).to.be.at.least(criteria.minSuccessProbability);
        expect(pool.profitPotential).to.be.at.least(criteria.minProfitPotential);
      });
      
      console.log(`✅ Filtered to ${filteredPools.length} high-quality opportunities`);
    });
  });

  describe('💎 Pool Analysis', () => {
    it('should provide detailed gold analysis for specific pools', async () => {
      const goldPools = await smartContract.getTopPools();
      const testPool = goldPools[0];
      
      const analysis = await smartContract.getPoolRanking(testPool.poolAddress);
      
      if (analysis) {
        expect(analysis).to.have.property('goldScore');
        expect(analysis).to.have.property('recommendedAction');
        expect(analysis.goldScore).to.be.a('number');
        
        console.log(`✅ Pool Analysis for ${testPool.tokenX}/${testPool.tokenY}:`);
        console.log(`   Gold Score: ${analysis.goldScore}`);
        console.log(`   Recommended Action: ${analysis.recommendedAction}`);
        console.log(`   Entry Confidence: ${(analysis.entryConfidence * 100).toFixed(1)}%`);
      }
    });
  });

  describe('🧠 Learning System', () => {
    it('should handle trade result feedback', async () => {
      const tradeResult = {
        poolAddress: 'test_pool_address',
        entryPrice: 100,
        exitPrice: 125,
        profitPercentage: 0.25, // 25% profit
        volumeAtEntry: 1000000,
        feeConcentrationAtEntry: 0.15,
        timestamp: Date.now()
      };
      
      const result = await smartContract.feedTradeResult(tradeResult);
      
      expect(result).to.be.a('boolean');
      expect(smartContract.tradeHistory).to.include(tradeResult);
      
      console.log(`✅ Trade result recorded: ${(tradeResult.profitPercentage * 100).toFixed(1)}% profit`);
    });

    it('should provide learning insights', async () => {
      // Add some mock trade history
      smartContract.tradeHistory = [
        { profitPercentage: 0.25 },
        { profitPercentage: 0.15 },
        { profitPercentage: -0.05 },
        { profitPercentage: 0.30 }
      ];
      
      const insights = await smartContract.getHoneyLeaderboard(); // Returns learning insights
      
      expect(insights).to.have.property('totalTradesLearned');
      expect(insights).to.have.property('successRate');
      expect(insights).to.have.property('systemStatus');
      
      console.log(`✅ Learning Insights:`);
      console.log(`   Total Trades: ${insights.totalTradesLearned}`);
      console.log(`   Success Rate: ${(insights.successRate * 100).toFixed(1)}%`);
      console.log(`   System Status: ${insights.systemStatus}`);
    });
  });

  describe('🎯 Best Opportunity Detection', () => {
    it('should identify the absolute best opportunity', async () => {
      const bestOpportunity = await smartContract.getBestOpportunity();
      
      expect(bestOpportunity).to.be.an('object');
      expect(bestOpportunity).to.have.property('goldScore');
      expect(bestOpportunity).to.have.property('recommendation');
      
      const recommendation = bestOpportunity.recommendation;
      expect(recommendation).to.have.property('action');
      expect(recommendation).to.have.property('confidence');
      expect(recommendation).to.have.property('entrySize');
      expect(recommendation).to.have.property('reasoning');
      
      console.log(`✅ Best Opportunity: ${bestOpportunity.tokenX}/${bestOpportunity.tokenY}`);
      console.log(`   Action: ${recommendation.action}`);
      console.log(`   Confidence: ${recommendation.confidence}`);
      console.log(`   Entry Size: ${(recommendation.entrySize * 100).toFixed(1)}% of capital`);
      console.log(`   Reasoning: ${recommendation.reasoning.join(', ')}`);
    });
  });

  describe('🚨 Alert System', () => {
    it('should generate enhanced real-time alerts', async () => {
      const alerts = await smartContract.getAlerts();
      
      expect(alerts).to.be.an('array');
      
      alerts.forEach(alert => {
        expect(alert).to.have.property('type');
        expect(alert).to.have.property('message');
        expect(alert).to.have.property('priority');
        expect(alert).to.have.property('recommendation');
      });
      
      console.log(`✅ Generated ${alerts.length} alerts:`);
      alerts.forEach(alert => {
        console.log(`   ${alert.priority}: ${alert.message}`);
        console.log(`   Recommendation: ${alert.recommendation}`);
      });
    });

    it('should prioritize critical opportunities', async () => {
      const alerts = await smartContract.getAlerts();
      const criticalAlerts = alerts.filter(alert => alert.priority === 'CRITICAL');
      
      criticalAlerts.forEach(alert => {
        expect(alert.type).to.be.oneOf(['ULTIMATE_GOLD_OPPORTUNITY']);
        expect(alert.recommendation).to.be.oneOf(['IMMEDIATE_ACTION']);
      });
      
      console.log(`✅ Found ${criticalAlerts.length} critical opportunities`);
    });
  });

  describe('💰 Profit Calculation Validation', () => {
    it('should calculate realistic profit potentials', async () => {
      const goldPools = await smartContract.getTopPools();
      
      goldPools.forEach(pool => {
        // Profit potential should be reasonable (0-500% annual)
        expect(pool.profitPotential).to.be.at.least(0);
        expect(pool.profitPotential).to.be.at.most(5); // 500% max
        
        // Success probability should be between 0 and 1
        expect(pool.successProbability).to.be.at.least(0);
        expect(pool.successProbability).to.be.at.most(1);
        
        // Entry confidence should be between 0 and 1
        expect(pool.entryConfidence).to.be.at.least(0);
        expect(pool.entryConfidence).to.be.at.most(1);
        
        // Optimal entry size should be reasonable (0.1% to 10%)
        expect(pool.optimalEntrySize).to.be.at.least(0.001);
        expect(pool.optimalEntrySize).to.be.at.most(0.1);
      });
      
      console.log(`✅ All ${goldPools.length} pools have realistic metrics`);
    });
  });

  describe('🔒 Risk Management', () => {
    it('should properly assess and categorize risks', async () => {
      const goldPools = await smartContract.getTopPools();
      
      goldPools.forEach(pool => {
        expect(pool.riskLevel).to.be.oneOf(['VERY_LOW', 'LOW', 'MEDIUM', 'HIGH', 'VERY_HIGH']);
        
        // High risk pools should have smaller optimal entry sizes
        if (pool.riskLevel === 'HIGH' || pool.riskLevel === 'VERY_HIGH') {
          expect(pool.optimalEntrySize).to.be.at.most(0.03); // Max 3% for high risk
        }
      });
      
      console.log(`✅ Risk assessment validated for all pools`);
    });

    it('should generate appropriate warnings', async () => {
      const bestOpportunity = await smartContract.getBestOpportunity();
      const recommendation = bestOpportunity.recommendation;
      
      if (bestOpportunity.riskLevel === 'HIGH') {
        expect(recommendation.warnings).to.not.be.empty;
        expect(recommendation.warnings.some(w => w.includes('risk'))).to.be.true;
      }
      
      console.log(`✅ Risk warnings properly generated`);
    });
  });

  describe('📊 System Integration', () => {
    it('should handle smart contract unavailability gracefully', async () => {
      // Test fallback functionality
      smartContract.isInitialized = false;
      
      const goldPools = await smartContract.getTopPools();
      const insights = await smartContract.getHoneyLeaderboard();
      const alerts = await smartContract.getAlerts();
      
      expect(goldPools).to.be.an('array');
      expect(insights).to.be.an('object');
      expect(alerts).to.be.an('array');
      
      console.log(`✅ Fallback systems working correctly`);
    });
  });
});

// 🚀 RUN COMPREHENSIVE TEST SUITE
async function runComprehensiveTests() {
  console.log('🧪 Starting Meteora Gold Extractor Test Suite...\n');
  
  const smartContract = new SmartContractIntelligence();
  
  try {
    // Test 1: Basic Functionality
    console.log('📋 Test 1: Basic Gold Opportunity Detection');
    const goldPools = await smartContract.getTopPools();
    console.log(`   Found ${goldPools.length} gold opportunities`);
    
    if (goldPools.length > 0) {
      const top = goldPools[0];
      console.log(`   Top: ${top.tokenX}/${top.tokenY} - Gold Score: ${top.goldScore}`);
    }
    
    // Test 2: Best Opportunity
    console.log('\n🎯 Test 2: Best Opportunity Analysis');
    const best = await smartContract.getBestOpportunity();
    if (best) {
      console.log(`   Best: ${best.tokenX}/${best.tokenY}`);
      console.log(`   Action: ${best.recommendation.action}`);
      console.log(`   Entry Size: ${(best.recommendation.entrySize * 100).toFixed(1)}%`);
    }
    
    // Test 3: Alert System
    console.log('\n🚨 Test 3: Alert System');
    const alerts = await smartContract.getAlerts();
    console.log(`   Generated ${alerts.length} alerts`);
    alerts.slice(0, 3).forEach(alert => {
      console.log(`   ${alert.priority}: ${alert.type}`);
    });
    
    // Test 4: Learning System
    console.log('\n🧠 Test 4: Learning System');
    const insights = await smartContract.getHoneyLeaderboard();
    console.log(`   System Status: ${insights.systemStatus}`);
    console.log(`   Confidence Level: ${(insights.confidenceLevel * 100).toFixed(1)}%`);
    
    console.log('\n✅ All tests completed successfully!');
    console.log('🚀 Smart Contract Intelligence System is ready for deployment!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Export for use in other files
export { runComprehensiveTests };
export default SmartContractIntelligence;
