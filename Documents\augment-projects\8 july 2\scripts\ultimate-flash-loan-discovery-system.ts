import { ComprehensiveFlashLoanMonitor } from '../src/monitoring/ComprehensiveFlashLoanMonitor';
import { OpportunityScanner } from '../src/monitoring/OpportunityScanner';
import { TransactionAnalyzer } from '../src/monitoring/TransactionAnalyzer';
import { ethers } from 'ethers';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

dotenv.config();

/**
 * 🚀 ULTIMATE FLASH LOAN DISCOVERY SYSTEM
 * 
 * This comprehensive system combines multiple monitoring approaches:
 * 1. Real-time transaction monitoring and analysis
 * 2. Opportunity scanning across multiple strategies
 * 3. Pattern recognition from successful transactions
 * 4. Automated profit validation and risk assessment
 * 
 * The goal is to discover profitable flash loan opportunities that
 * are actually being executed by successful traders.
 */

interface SystemConfig {
  arbitrumRpcUrl: string;
  polygonRpcUrl: string;
  alchemyApiKey: string;
  minProfitUSD: number;
  maxGasPrice: number;
  enabledFeatures: {
    transactionMonitoring: boolean;
    opportunityScanning: boolean;
    patternAnalysis: boolean;
    realTimeAlerts: boolean;
  };
}

class UltimateFlashLoanDiscoverySystem {
  private config: SystemConfig;
  private monitor: ComprehensiveFlashLoanMonitor;
  private scanner: OpportunityScanner;
  private arbitrumAnalyzer: TransactionAnalyzer;
  private polygonAnalyzer: TransactionAnalyzer;
  private discoveredStrategies: Map<string, any> = new Map();
  private profitableTransactions: any[] = [];
  private alertCount = 0;

  constructor(config: SystemConfig) {
    this.config = config;
    
    // Initialize providers
    const arbitrumProvider = new ethers.providers.JsonRpcProvider(config.arbitrumRpcUrl);
    const polygonProvider = new ethers.providers.JsonRpcProvider(config.polygonRpcUrl);

    // Initialize monitoring components
    this.monitor = new ComprehensiveFlashLoanMonitor({
      arbitrumRpcUrl: config.arbitrumRpcUrl,
      polygonRpcUrl: config.polygonRpcUrl,
      alchemyApiKey: config.alchemyApiKey,
      minProfitUSD: config.minProfitUSD,
      maxGasPrice: config.maxGasPrice,
      monitoringInterval: 5000,
      enableMEVDetection: true,
      enableLiquidationDetection: true,
      enableArbitrageDetection: true
    });

    this.scanner = new OpportunityScanner({
      providers: {
        arbitrum: arbitrumProvider,
        polygon: polygonProvider
      },
      minProfitUSD: config.minProfitUSD,
      maxGasPrice: config.maxGasPrice,
      scanInterval: 10000,
      enabledStrategies: {
        dexArbitrage: true,
        liquidations: true,
        yieldFarming: true,
        mevOpportunities: true
      }
    });

    this.arbitrumAnalyzer = new TransactionAnalyzer(arbitrumProvider, 42161);
    this.polygonAnalyzer = new TransactionAnalyzer(polygonProvider, 137);
  }

  /**
   * Start the complete discovery system
   */
  public async start(): Promise<void> {
    console.log('🚀 STARTING ULTIMATE FLASH LOAN DISCOVERY SYSTEM');
    console.log('=' .repeat(70));
    
    this.displaySystemInfo();
    
    // Set up event handlers
    this.setupEventHandlers();

    // Start all monitoring components
    const startPromises = [];

    if (this.config.enabledFeatures.transactionMonitoring) {
      console.log('📡 Starting transaction monitoring...');
      startPromises.push(this.monitor.startMonitoring());
    }

    if (this.config.enabledFeatures.opportunityScanning) {
      console.log('🔍 Starting opportunity scanning...');
      startPromises.push(this.scanner.startScanning());
    }

    await Promise.all(startPromises);

    // Start periodic analysis
    if (this.config.enabledFeatures.patternAnalysis) {
      this.startPatternAnalysis();
    }

    // Start periodic reporting
    this.startPeriodicReporting();

    console.log('\n✅ ULTIMATE DISCOVERY SYSTEM IS NOW ACTIVE!');
    console.log('🎯 Monitoring both Arbitrum and Polygon for profitable opportunities');
    console.log('📊 Real-time analysis and pattern recognition enabled');
    console.log('🚨 Automated alerts for high-profit opportunities');
    console.log('\n🛑 Press Ctrl+C to stop the system\n');

    // Graceful shutdown
    process.on('SIGINT', () => {
      this.shutdown();
    });
  }

  /**
   * Set up event handlers for all monitoring components
   */
  private setupEventHandlers(): void {
    // Monitor events
    this.monitor.on('opportunityDetected', (opportunity) => {
      this.handleMonitorOpportunity(opportunity);
    });

    this.monitor.on('urgentOpportunity', (opportunity) => {
      this.handleUrgentOpportunity(opportunity, 'MONITOR');
    });

    // Scanner events
    this.scanner.on('opportunityFound', (opportunity) => {
      this.handleScannerOpportunity(opportunity);
    });

    this.scanner.on('urgentOpportunity', (opportunity) => {
      this.handleUrgentOpportunity(opportunity, 'SCANNER');
    });
  }

  /**
   * Handle opportunity detected by monitor
   */
  private handleMonitorOpportunity(opportunity: any): void {
    console.log(`\n📡 MONITOR DETECTED: ${opportunity.strategy} - $${opportunity.netProfitUSD.toFixed(2)}`);
    
    // Store for analysis
    this.profitableTransactions.push({
      source: 'MONITOR',
      timestamp: Date.now(),
      ...opportunity
    });

    // Analyze the transaction in detail
    this.analyzeDetectedTransaction(opportunity);

    // Save to discoveries
    this.saveDiscovery(opportunity, 'monitor');
  }

  /**
   * Handle opportunity found by scanner
   */
  private handleScannerOpportunity(opportunity: any): void {
    console.log(`\n🔍 SCANNER FOUND: ${opportunity.type} - $${opportunity.estimatedProfitUSD.toFixed(2)}`);
    
    // Store for analysis
    this.profitableTransactions.push({
      source: 'SCANNER',
      timestamp: Date.now(),
      ...opportunity
    });

    // Save to discoveries
    this.saveDiscovery(opportunity, 'scanner');
  }

  /**
   * Handle urgent high-profit opportunities
   */
  private handleUrgentOpportunity(opportunity: any, source: string): void {
    this.alertCount++;
    
    console.log('\n🚨🚨🚨 URGENT HIGH-PROFIT OPPORTUNITY! 🚨🚨🚨');
    console.log(`Source: ${source}`);
    console.log(`Type: ${opportunity.strategy || opportunity.type}`);
    console.log(`Profit: $${(opportunity.netProfitUSD || opportunity.estimatedProfitUSD).toFixed(2)}`);
    console.log(`Chain: ${opportunity.chainId === 42161 ? 'Arbitrum' : 'Polygon'}`);
    console.log(`Alert #${this.alertCount}`);
    console.log('🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨');

    // Save urgent opportunity with special marking
    this.saveDiscovery(opportunity, `urgent-${source.toLowerCase()}`);

    // Here you could implement:
    // - Email/SMS alerts
    // - Automatic execution (with proper safety checks)
    // - Integration with trading bots
  }

  /**
   * Analyze a detected transaction in detail
   */
  private async analyzeDetectedTransaction(opportunity: any): Promise<void> {
    try {
      const analyzer = opportunity.chainId === 42161 ? this.arbitrumAnalyzer : this.polygonAnalyzer;
      const analysis = await analyzer.analyzeTransaction(opportunity.txHash);
      
      if (analysis) {
        console.log(`🔬 DETAILED ANALYSIS: ${opportunity.txHash}`);
        console.log(`   Strategy Pattern: ${analysis.flashLoanDetails?.provider || 'N/A'}`);
        console.log(`   Risk Score: ${analysis.profitAnalysis.riskScore}/100`);
        console.log(`   ROI: ${analysis.profitAnalysis.roi.toFixed(2)}%`);
        
        // Store strategy pattern
        const strategyKey = `${opportunity.strategy}_${opportunity.chainId}`;
        const existingStrategy = this.discoveredStrategies.get(strategyKey) || { count: 0, totalProfit: 0 };
        existingStrategy.count++;
        existingStrategy.totalProfit += opportunity.netProfitUSD;
        existingStrategy.lastSeen = Date.now();
        existingStrategy.analysis = analysis;
        
        this.discoveredStrategies.set(strategyKey, existingStrategy);
      }
    } catch (error) {
      console.error(`❌ Error analyzing transaction ${opportunity.txHash}:`, error);
    }
  }

  /**
   * Start pattern analysis
   */
  private startPatternAnalysis(): void {
    console.log('🧠 Starting pattern analysis...');
    
    setInterval(() => {
      this.analyzePatterns();
    }, 300000); // Every 5 minutes
  }

  /**
   * Analyze patterns in discovered opportunities
   */
  private analyzePatterns(): void {
    if (this.profitableTransactions.length === 0) return;

    console.log('\n🧠 PATTERN ANALYSIS REPORT');
    console.log('=' .repeat(50));

    // Analyze by strategy type
    const strategyStats = new Map<string, {count: number, totalProfit: number, avgProfit: number}>();
    
    this.profitableTransactions.forEach(tx => {
      const strategy = tx.strategy || tx.type;
      const profit = tx.netProfitUSD || tx.estimatedProfitUSD;
      
      const stats = strategyStats.get(strategy) || { count: 0, totalProfit: 0, avgProfit: 0 };
      stats.count++;
      stats.totalProfit += profit;
      stats.avgProfit = stats.totalProfit / stats.count;
      
      strategyStats.set(strategy, stats);
    });

    // Display top strategies
    console.log('🏆 TOP PROFITABLE STRATEGIES:');
    const sortedStrategies = Array.from(strategyStats.entries())
      .sort((a, b) => b[1].totalProfit - a[1].totalProfit)
      .slice(0, 5);

    sortedStrategies.forEach(([strategy, stats], index) => {
      console.log(`${index + 1}. ${strategy}:`);
      console.log(`   Instances: ${stats.count}`);
      console.log(`   Total Profit: $${stats.totalProfit.toFixed(2)}`);
      console.log(`   Avg Profit: $${stats.avgProfit.toFixed(2)}`);
    });

    // Analyze by chain
    const arbitrumTxs = this.profitableTransactions.filter(tx => tx.chainId === 42161);
    const polygonTxs = this.profitableTransactions.filter(tx => tx.chainId === 137);
    
    console.log('\n⛓️ CHAIN ANALYSIS:');
    console.log(`Arbitrum: ${arbitrumTxs.length} opportunities`);
    console.log(`Polygon: ${polygonTxs.length} opportunities`);

    // Time-based analysis
    const recentTxs = this.profitableTransactions.filter(tx => 
      Date.now() - tx.timestamp < 3600000 // Last hour
    );
    
    console.log(`\n⏰ RECENT ACTIVITY (Last Hour): ${recentTxs.length} opportunities`);
    
    console.log('=' .repeat(50));
  }

  /**
   * Start periodic reporting
   */
  private startPeriodicReporting(): void {
    setInterval(() => {
      this.generateReport();
    }, 600000); // Every 10 minutes
  }

  /**
   * Generate comprehensive report
   */
  private generateReport(): void {
    const monitorStats = this.monitor.getStats();
    const scannerStats = this.scanner.getStats();
    
    console.log('\n📊 SYSTEM PERFORMANCE REPORT');
    console.log('=' .repeat(60));
    console.log(`Uptime: ${this.getUptime()}`);
    console.log(`Total Alerts: ${this.alertCount}`);
    console.log('\n📡 MONITOR STATS:');
    console.log(`  Opportunities: ${monitorStats.totalOpportunities}`);
    console.log(`  Avg Profit: $${monitorStats.avgProfit.toFixed(2)}`);
    console.log(`  Replicable: ${monitorStats.replicableCount}`);
    
    console.log('\n🔍 SCANNER STATS:');
    console.log(`  Opportunities: ${scannerStats.totalOpportunities}`);
    console.log(`  Avg Profit: $${scannerStats.avgProfitUSD.toFixed(2)}`);
    console.log(`  High Confidence: ${scannerStats.highConfidenceCount}`);
    
    console.log('\n🧠 DISCOVERED STRATEGIES:');
    if (this.discoveredStrategies.size > 0) {
      Array.from(this.discoveredStrategies.entries())
        .sort((a, b) => b[1].totalProfit - a[1].totalProfit)
        .slice(0, 3)
        .forEach(([strategy, data]) => {
          console.log(`  ${strategy}: ${data.count} instances, $${data.totalProfit.toFixed(2)} total`);
        });
    } else {
      console.log('  No strategies discovered yet');
    }
    
    console.log('=' .repeat(60));
  }

  /**
   * Save discovery to file
   */
  private saveDiscovery(opportunity: any, source: string): void {
    try {
      const discoveriesDir = path.join(__dirname, '../discoveries');
      if (!fs.existsSync(discoveriesDir)) {
        fs.mkdirSync(discoveriesDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${source}-discovery-${timestamp}.json`;
      const filepath = path.join(discoveriesDir, filename);

      const discoveryData = {
        timestamp: new Date().toISOString(),
        source,
        opportunity,
        systemStats: {
          monitorStats: this.monitor.getStats(),
          scannerStats: this.scanner.getStats(),
          totalDiscoveries: this.profitableTransactions.length
        }
      };

      fs.writeFileSync(filepath, JSON.stringify(discoveryData, null, 2));
      console.log(`💾 Discovery saved: ${filename}`);
    } catch (error) {
      console.error('❌ Error saving discovery:', error);
    }
  }

  /**
   * Display system information
   */
  private displaySystemInfo(): void {
    console.log('🔧 SYSTEM CONFIGURATION:');
    console.log(`  Min Profit: $${this.config.minProfitUSD}`);
    console.log(`  Max Gas Price: ${this.config.maxGasPrice} gwei`);
    console.log(`  Transaction Monitoring: ${this.config.enabledFeatures.transactionMonitoring ? '✅' : '❌'}`);
    console.log(`  Opportunity Scanning: ${this.config.enabledFeatures.opportunityScanning ? '✅' : '❌'}`);
    console.log(`  Pattern Analysis: ${this.config.enabledFeatures.patternAnalysis ? '✅' : '❌'}`);
    console.log(`  Real-time Alerts: ${this.config.enabledFeatures.realTimeAlerts ? '✅' : '❌'}`);
    
    console.log('\n🎯 MONITORING SCOPE:');
    console.log('  • Arbitrum & Polygon flash loans');
    console.log('  • DEX arbitrage opportunities');
    console.log('  • Liquidation opportunities');
    console.log('  • MEV opportunities');
    console.log('  • Yield farming strategies');
    console.log('  • Real-time transaction analysis');
  }

  /**
   * Get system uptime
   */
  private getUptime(): string {
    const uptime = process.uptime();
    const hours = Math.floor(uptime / 3600);
    const minutes = Math.floor((uptime % 3600) / 60);
    return `${hours}h ${minutes}m`;
  }

  /**
   * Shutdown the system gracefully
   */
  private shutdown(): void {
    console.log('\n🛑 Shutting down Ultimate Flash Loan Discovery System...');
    
    this.monitor.stopMonitoring();
    this.scanner.stopScanning();
    
    // Generate final report
    console.log('\n📈 FINAL SESSION REPORT');
    console.log('=' .repeat(50));
    console.log(`Session Duration: ${this.getUptime()}`);
    console.log(`Total Opportunities: ${this.profitableTransactions.length}`);
    console.log(`Total Alerts: ${this.alertCount}`);
    console.log(`Strategies Discovered: ${this.discoveredStrategies.size}`);
    
    if (this.profitableTransactions.length > 0) {
      const totalProfit = this.profitableTransactions.reduce((sum, tx) => 
        sum + (tx.netProfitUSD || tx.estimatedProfitUSD), 0
      );
      const avgProfit = totalProfit / this.profitableTransactions.length;
      console.log(`Total Potential Profit: $${totalProfit.toFixed(2)}`);
      console.log(`Average Profit: $${avgProfit.toFixed(2)}`);
    }
    
    console.log('\n💡 Check ./discoveries/ folder for detailed opportunity data');
    console.log('🚀 System shutdown complete');
    
    process.exit(0);
  }
}

// Main execution
async function main() {
  const config: SystemConfig = {
    arbitrumRpcUrl: `https://arb-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
    polygonRpcUrl: `https://polygon-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
    alchemyApiKey: process.env.ALCHEMY_API_KEY || '',
    minProfitUSD: 10,
    maxGasPrice: 100,
    enabledFeatures: {
      transactionMonitoring: true,
      opportunityScanning: true,
      patternAnalysis: true,
      realTimeAlerts: true
    }
  };

  if (!config.alchemyApiKey) {
    console.error('❌ ALCHEMY_API_KEY not found in environment variables');
    console.log('Please set your Alchemy API key in the .env file');
    process.exit(1);
  }

  const system = new UltimateFlashLoanDiscoverySystem(config);
  await system.start();
}

main().catch(console.error);
