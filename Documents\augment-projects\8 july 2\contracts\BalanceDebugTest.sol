// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * 🔍 BALANCE DEBUG TEST
 * Debug EXACT USDC balance issues in flash loan
 */

interface IERC20 {
    function transfer(address to, uint256 amount) external returns (bool);
    function balanceOf(address account) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
}

interface IBalancerVault {
    function flashLoan(address recipient, address[] memory tokens, uint256[] memory amounts, bytes memory userData) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(address[] memory tokens, uint256[] memory amounts, uint256[] memory feeAmounts, bytes memory userData) external;
}

contract BalanceDebugTest is <PERSON>lashLoanRecipient {
    
    IERC20 public constant USDC = IERC20(******************************************);
    IBalancerVault public constant BALANCER = IBalancerVault(******************************************);
    
    address public constant PROFIT_WALLET = ******************************************;
    uint256 public constant FLASH_AMOUNT = 1000e6; // $1K USDC
    
    uint256 public lastProfit;
    bool public lastSuccess;
    uint256 public totalExecutions;
    
    event FlashLoanResult(uint256 profit, bool success, uint256 execution);
    event DebugStep(string step, uint256 value);
    event BalanceCheck(string step, uint256 balance, uint256 expected);
    
    /**
     * 🚀 EXECUTE BALANCE DEBUG TEST
     */
    function executeBalanceDebugTest() external {
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        
        tokens[0] = address(USDC);
        amounts[0] = FLASH_AMOUNT;
        
        emit DebugStep("Starting balance debug test", FLASH_AMOUNT);
        
        // Check balance before flash loan
        uint256 balanceBefore = USDC.balanceOf(address(this));
        emit BalanceCheck("Before flash loan", balanceBefore, 0);
        
        BALANCER.flashLoan(address(this), tokens, amounts, "");
    }
    
    /**
     * 🎯 FLASH LOAN CALLBACK - BALANCE DEBUG
     */
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory
    ) external override {
        require(msg.sender == address(BALANCER), "Only Balancer");
        
        uint256 flashAmount = amounts[0];
        uint256 initialBalance = USDC.balanceOf(address(this));
        
        emit DebugStep("Flash loan callback started", flashAmount);
        emit BalanceCheck("Flash loan received", initialBalance, flashAmount);
        
        // Verify flash loan
        require(initialBalance >= flashAmount, "Flash loan not received");
        require(feeAmounts[0] == 0, "Expected 0% fee");
        
        emit DebugStep("Flash loan verified", flashAmount);
        
        // TEST DIFFERENT TRANSFER AMOUNTS
        testTransferAmounts(flashAmount, initialBalance);
        
        // Check balance before repay
        uint256 balanceBeforeRepay = USDC.balanceOf(address(this));
        emit BalanceCheck("Before repay", balanceBeforeRepay, flashAmount);
        
        // Repay flash loan
        emit DebugStep("Attempting to repay flash loan", flashAmount);
        
        // Check if we have enough to repay
        if (balanceBeforeRepay >= flashAmount) {
            USDC.transfer(address(BALANCER), flashAmount);
            emit DebugStep("Flash loan repaid successfully", flashAmount);
        } else {
            emit DebugStep("INSUFFICIENT BALANCE FOR REPAY", balanceBeforeRepay);
            // Try to repay what we have
            if (balanceBeforeRepay > 0) {
                USDC.transfer(address(BALANCER), balanceBeforeRepay);
                emit DebugStep("Partial repay attempted", balanceBeforeRepay);
            }
        }
        
        // Extract any remaining balance
        uint256 finalBalance = USDC.balanceOf(address(this));
        emit BalanceCheck("Final balance", finalBalance, 0);
        
        totalExecutions++;
        
        if (finalBalance > 0) {
            lastProfit = finalBalance;
            USDC.transfer(PROFIT_WALLET, finalBalance);
            lastSuccess = true;
        } else {
            lastProfit = 0;
            lastSuccess = true; // Success even with no profit
        }
        
        emit FlashLoanResult(lastProfit, lastSuccess, totalExecutions);
        emit DebugStep("Balance debug test completed", finalBalance);
    }
    
    /**
     * 🔍 TEST DIFFERENT TRANSFER AMOUNTS
     */
    function testTransferAmounts(uint256 flashAmount, uint256 currentBalance) internal {
        emit DebugStep("Testing transfer amounts", currentBalance);
        
        // Test 1: Try to transfer 10% of flash amount
        uint256 testAmount1 = flashAmount / 10; // 10%
        emit DebugStep("Test 1: Attempting 10% transfer", testAmount1);
        
        if (currentBalance >= testAmount1) {
            emit DebugStep("Test 1: Balance sufficient", currentBalance);
            
            try this.testTransfer(testAmount1) {
                emit DebugStep("Test 1: Transfer successful", testAmount1);
            } catch {
                emit DebugStep("Test 1: Transfer failed", testAmount1);
            }
        } else {
            emit DebugStep("Test 1: Insufficient balance", currentBalance);
        }
        
        // Test 2: Try to transfer 50% of flash amount
        uint256 testAmount2 = flashAmount / 2; // 50%
        uint256 balanceAfterTest1 = USDC.balanceOf(address(this));
        emit DebugStep("Test 2: Attempting 50% transfer", testAmount2);
        emit BalanceCheck("After test 1", balanceAfterTest1, 0);
        
        if (balanceAfterTest1 >= testAmount2) {
            emit DebugStep("Test 2: Balance sufficient", balanceAfterTest1);
            
            try this.testTransfer(testAmount2) {
                emit DebugStep("Test 2: Transfer successful", testAmount2);
            } catch {
                emit DebugStep("Test 2: Transfer failed", testAmount2);
            }
        } else {
            emit DebugStep("Test 2: Insufficient balance", balanceAfterTest1);
        }
        
        // Test 3: Try to transfer 100% of flash amount
        uint256 balanceAfterTest2 = USDC.balanceOf(address(this));
        emit DebugStep("Test 3: Attempting 100% transfer", flashAmount);
        emit BalanceCheck("After test 2", balanceAfterTest2, 0);
        
        if (balanceAfterTest2 >= flashAmount) {
            emit DebugStep("Test 3: Balance sufficient", balanceAfterTest2);
            
            try this.testTransfer(flashAmount) {
                emit DebugStep("Test 3: Transfer successful", flashAmount);
            } catch {
                emit DebugStep("Test 3: Transfer failed", flashAmount);
            }
        } else {
            emit DebugStep("Test 3: Insufficient balance", balanceAfterTest2);
        }
        
        uint256 finalTestBalance = USDC.balanceOf(address(this));
        emit BalanceCheck("After all tests", finalTestBalance, 0);
    }
    
    /**
     * 🔄 TEST TRANSFER (EXTERNAL FOR TRY-CATCH)
     */
    function testTransfer(uint256 amount) external {
        require(msg.sender == address(this), "Internal only");
        
        uint256 balanceBefore = USDC.balanceOf(address(this));
        emit DebugStep("Test transfer starting", amount);
        emit BalanceCheck("Before test transfer", balanceBefore, 0);
        
        // Transfer to self (should always work if balance is sufficient)
        USDC.transfer(address(this), amount);
        
        uint256 balanceAfter = USDC.balanceOf(address(this));
        emit BalanceCheck("After test transfer", balanceAfter, balanceBefore);
        emit DebugStep("Test transfer completed", amount);
    }
    
    /**
     * 📊 VIEW FUNCTIONS
     */
    function getResults() external view returns (uint256 profit, bool success, uint256 executions) {
        return (lastProfit, lastSuccess, totalExecutions);
    }
    
    /**
     * 🔍 CHECK CURRENT BALANCE
     */
    function getCurrentBalance() external view returns (uint256) {
        return USDC.balanceOf(address(this));
    }
}
