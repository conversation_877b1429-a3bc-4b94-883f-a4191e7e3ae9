/**
 * 🔬 TEST TIMING DELAY BORROWING FIX
 * Deploy and test the timing delay hypothesis
 */

const { ethers } = require('hardhat');

async function testTimingDelayBorrowingFix() {
    console.log('🔬 TEST TIMING DELAY BORROWING FIX');
    console.log('🎯 HYPOTHESIS: Adding delay after supply fixes borrowing');
    console.log('✅ BASED ON PROVEN WORKING FOUNDATION');
    console.log('🔧 TEST 1 OF 5 BORROWING RESEARCH TESTS');
    console.log('=' .repeat(80));
    
    const [deployer] = await ethers.getSigners();
    const network = await ethers.provider.getNetwork();
    
    console.log(`👤 Deployer: ${deployer.address}`);
    console.log(`🌐 Network: ${network.name} (${network.chainId})`);
    
    const ethBalance = await deployer.getBalance();
    console.log(`💰 ETH Balance: ${ethers.utils.formatEther(ethBalance)} ETH`);
    
    try {
        console.log('\n🚀 DEPLOYING TIMING DELAY BORROWING FIX...');
        
        const gasPrice = await ethers.provider.getGasPrice();
        const adjustedGasPrice = gasPrice.mul(120).div(100); // 20% higher
        console.log(`⛽ Adjusted Gas Price: ${ethers.utils.formatUnits(adjustedGasPrice, 'gwei')} gwei`);
        
        const TimingDelayBorrowingFix = await ethers.getContractFactory('TimingDelayBorrowingFix');
        
        const timingDelayContract = await TimingDelayBorrowingFix.deploy({
            gasPrice: adjustedGasPrice
        });
        
        console.log(`📝 Deployment TX: ${timingDelayContract.deployTransaction.hash}`);
        console.log('⏳ Waiting for confirmation...');
        
        await timingDelayContract.deployed();
        const receipt = await timingDelayContract.deployTransaction.wait();
        
        console.log(`🎉 TIMING DELAY CONTRACT DEPLOYED: ${timingDelayContract.address}`);
        console.log(`⛽ Gas Used: ${receipt.gasUsed.toLocaleString()}`);
        console.log(`💸 Deployment Cost: ${ethers.utils.formatEther(receipt.gasUsed.mul(adjustedGasPrice))} ETH`);
        
        // Test contract info
        console.log('\n🔧 TESTING CONTRACT INFO...');
        
        try {
            const contractInfo = await timingDelayContract.getContractInfo();
            console.log('✅ TIMING DELAY CONTRACT CONFIGURATION:');
            console.log(`   USDC: ${contractInfo.usdc}`);
            console.log(`   Balancer: ${contractInfo.balancer}`);
            console.log(`   Aave Pool: ${contractInfo.aavePool}`);
            console.log(`   Profit Wallet: ${contractInfo.profitWallet}`);
            
        } catch (error) {
            console.log(`⚠️ Contract info test failed: ${error.message.substring(0, 50)}...`);
        }
        
        // Execute the timing delay borrowing test
        console.log('\n🔬 EXECUTING TIMING DELAY BORROWING TEST...');
        console.log('🎯 THIS TESTS IF TIMING DELAY FIXES BORROWING!');
        console.log('💸 Flash loan: $10,000 USDC (0% fee)');
        console.log('🏦 Supply: $8,000 USDC to Aave');
        console.log('⏰ TIMING DELAY: Add computational delay');
        console.log('💳 Borrow: ~$2,176 USDC (32% LTV × 85% safety)');
        console.log('💸 Repay: ~$2,176 USDC immediately');
        console.log('💸 Withdraw: $8,000 USDC from Aave');
        console.log('🎯 Expected: BORROWING SUCCESS with timing delay!');
        
        try {
            const tx = await timingDelayContract.executeTimingDelayTest({
                gasLimit: 6000000,
                gasPrice: adjustedGasPrice
            });
            
            console.log(`📝 TIMING DELAY TEST TX Hash: ${tx.hash}`);
            console.log('⏳ Waiting for confirmation...');
            
            const timingDelayReceipt = await tx.wait();
            
            console.log(`📊 TIMING DELAY TEST STATUS: ${timingDelayReceipt.status === 1 ? '🎉 SUCCESS!' : '❌ FAILED'}`);
            console.log(`⛽ Gas Used: ${timingDelayReceipt.gasUsed.toLocaleString()}`);
            console.log(`💸 Execution Cost: ${ethers.utils.formatEther(timingDelayReceipt.gasUsed.mul(adjustedGasPrice))} ETH`);
            
            if (timingDelayReceipt.status === 1) {
                console.log('\n🎉🎉🎉 TIMING DELAY TEST SUCCESS! 🎉🎉🎉');
                
                // Check execution stats
                const stats = await timingDelayContract.getExecutionStats();
                console.log('\n📊 TIMING DELAY TEST STATS:');
                console.log(`   Last Profit: $${ethers.utils.formatUnits(stats.lastProfitAmount, 6)}`);
                console.log(`   Last Success: ${stats.lastExecutionSuccess}`);
                console.log(`   Total Executions: ${stats.totalExecutionCount}`);
                console.log(`   Total Profit: $${ethers.utils.formatUnits(stats.totalProfits, 6)}`);
                
                // Analyze events to see if borrowing worked
                console.log('\n📋 ANALYZING TIMING DELAY TEST EVENTS...');
                if (timingDelayReceipt.logs && timingDelayReceipt.logs.length > 0) {
                    
                    let borrowingWorked = false;
                    let borrowAmount = '0';
                    let profitGenerated = false;
                    let profitAmount = '0';
                    let timingDelaySteps = [];
                    
                    for (const log of timingDelayReceipt.logs) {
                        try {
                            const decoded = timingDelayContract.interface.parseLog(log);
                            
                            if (decoded.name === 'TimingDelayTest') {
                                const step = decoded.args.step;
                                const value = decoded.args.value.toString();
                                timingDelaySteps.push(`${step}: ${value}`);
                                
                            } else if (decoded.name === 'BorrowingSuccess') {
                                borrowAmount = ethers.utils.formatUnits(decoded.args.amount, 6);
                                console.log(`   🎉 BORROWING SUCCESS: $${borrowAmount} USDC`);
                                borrowingWorked = true;
                                
                            } else if (decoded.name === 'BorrowingFailed') {
                                const reason = decoded.args.reason;
                                console.log(`   ❌ BORROWING FAILED: ${reason}`);
                                
                            } else if (decoded.name === 'ProfitExtracted') {
                                profitAmount = ethers.utils.formatUnits(decoded.args.profit, 6);
                                console.log(`   💰 PROFIT EXTRACTED: $${profitAmount} USDC`);
                                profitGenerated = true;
                            }
                        } catch (error) {
                            // Skip unparseable events
                        }
                    }
                    
                    // Show key timing delay steps
                    console.log('\n⏰ TIMING DELAY EXECUTION STEPS:');
                    timingDelaySteps.slice(0, 10).forEach((step, index) => {
                        console.log(`   ${index + 1}. ${step}`);
                    });
                    if (timingDelaySteps.length > 10) {
                        console.log(`   ... and ${timingDelaySteps.length - 10} more steps`);
                    }
                    
                    console.log('\n🎯 TIMING DELAY TEST RESULTS:');
                    console.log(`   Borrowing Worked: ${borrowingWorked ? '✅ YES' : '❌ NO'}`);
                    console.log(`   Profit Generated: ${profitGenerated ? '✅ YES' : '❌ NO'}`);
                    
                    if (borrowingWorked) {
                        console.log('\n🎉🎉🎉 TIMING DELAY FIX SUCCESSFUL! 🎉🎉🎉');
                        console.log(`💰 Borrowed Amount: $${borrowAmount} USDC`);
                        console.log(`💰 Profit Generated: $${profitAmount} USDC`);
                        console.log('🔧 BORROWING ISSUE IS FIXED!');
                        console.log('🚀 Ready to scale up borrowing amounts!');
                        
                        console.log('\n🎯 NEXT STEPS:');
                        console.log('1. 🚀 Scale up borrowing amounts for higher profits');
                        console.log('2. 💰 Execute multiple times daily');
                        console.log('3. 📈 Optimize timing delay for maximum efficiency');
                        console.log('4. 🔧 Deploy production version with borrowing');
                        
                    } else {
                        console.log('\n💡 TIMING DELAY DID NOT FIX BORROWING');
                        console.log('🔬 Need to test next hypothesis');
                        console.log('🎯 Moving to TEST 2: Health Factor Validation');
                        
                        console.log('\n🔬 RESEARCH STATUS:');
                        console.log('   ❌ Test 1: Timing Delay - FAILED');
                        console.log('   ⏳ Test 2: Health Factor Validation - NEXT');
                        console.log('   ⏳ Test 3: Different Borrow Asset - PENDING');
                        console.log('   ⏳ Test 4: Larger Borrow Amounts - PENDING');
                        console.log('   ⏳ Test 5: Non-Flash Loan Context - PENDING');
                    }
                }
                
            } else {
                console.log('\n❌ TIMING DELAY TEST FAILED');
                console.log('💡 Check transaction details for failure reason');
            }
            
        } catch (error) {
            console.log('❌ TIMING DELAY TEST EXECUTION FAILED');
            console.log(`Error: ${error.message}`);
            
            if (error.message.includes('revert')) {
                console.log('💡 Transaction reverted - check contract logic');
            } else if (error.message.includes('insufficient')) {
                console.log('💡 Insufficient funds for execution');
            }
        }
        
        console.log('\n🎯 TIMING DELAY BORROWING TEST COMPLETE');
        console.log(`🔬 Test Contract: ${timingDelayContract.address}`);
        console.log('📊 This validates whether timing delay fixes borrowing');
        
        return {
            contract: timingDelayContract,
            address: timingDelayContract.address,
            testType: 'Timing Delay Borrowing Fix'
        };
        
    } catch (error) {
        console.error('💥 TIMING DELAY BORROWING TEST FAILED:', error.message);
        throw error;
    }
}

// Execute timing delay borrowing test
if (require.main === module) {
    testTimingDelayBorrowingFix()
        .then((result) => {
            console.log('\n🎉 TIMING DELAY BORROWING TEST COMPLETED!');
            console.log(`🔬 Test Contract: ${result.address}`);
            console.log(`🧪 Test Type: ${result.testType}`);
            console.log('🔬 BORROWING RESEARCH IN PROGRESS!');
        })
        .catch((error) => {
            console.error('Timing delay borrowing test failed:', error.message);
        });
}

module.exports = { testTimingDelayBorrowingFix };
