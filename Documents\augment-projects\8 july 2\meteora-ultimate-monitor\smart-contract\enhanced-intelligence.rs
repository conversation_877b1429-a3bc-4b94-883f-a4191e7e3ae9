// 🧠 ENHANCED METEORA INTELLIGENCE - Self-Learning Gold Extraction System
// This smart contract learns from successful trades and builds intelligence

use anchor_lang::prelude::*;
use anchor_spl::token::{Token, TokenAccount};
use std::collections::BTreeMap;

declare_id!("MeteoraGoldExtractorIntelligenceSystem1111111111");

#[program]
pub mod meteora_gold_extractor {
    use super::*;

    // 🚀 INITIALIZE THE GOLD EXTRACTION SYSTEM
    pub fn initialize(ctx: Context<Initialize>) -> Result<()> {
        let intelligence_state = &mut ctx.accounts.intelligence_state;
        intelligence_state.authority = ctx.accounts.authority.key();
        intelligence_state.total_pools_tracked = 0;
        intelligence_state.successful_trades_learned = 0;
        intelligence_state.last_update = Clock::get()?.unix_timestamp;
        intelligence_state.gold_pools = Vec::new();
        intelligence_state.learning_data = Vec::new();
        intelligence_state.profit_threshold = 0.05; // 5% minimum profit
        
        msg!("🧠 Meteora Gold Extractor Intelligence System Initialized!");
        Ok(())
    }

    // 💰 UPDATE GOLD RANKINGS (Self-Learning Algorithm)
    pub fn update_gold_rankings(
        ctx: Context<UpdateGoldRankings>,
        pool_data: Vec<PoolMetrics>,
        successful_trades: Vec<TradeResult>
    ) -> Result<()> {
        let intelligence_state = &mut ctx.accounts.intelligence_state;
        
        // 🧠 LEARN FROM SUCCESSFUL TRADES
        for trade in successful_trades {
            intelligence_state.learning_data.push(LearningData {
                pool_address: trade.pool_address,
                entry_price: trade.entry_price,
                exit_price: trade.exit_price,
                profit_percentage: trade.profit_percentage,
                volume_at_entry: trade.volume_at_entry,
                fee_concentration_at_entry: trade.fee_concentration_at_entry,
                timestamp: Clock::get()?.unix_timestamp,
                success_pattern: identify_success_pattern(&trade),
            });
        }
        
        // 📊 CALCULATE ENHANCED GOLD SCORES
        let mut gold_pools: Vec<GoldPool> = pool_data
            .into_iter()
            .map(|pool| {
                let base_profit_score = calculate_base_profit_score(&pool);
                let learning_multiplier = calculate_learning_multiplier(&pool, &intelligence_state.learning_data);
                let concentration_score = calculate_concentration_score(&pool);
                let momentum_score = calculate_momentum_score(&pool);
                let risk_adjusted_score = calculate_risk_adjusted_score(&pool);
                
                // 🎯 THE ULTIMATE GOLD SCORE FORMULA
                let gold_score = (base_profit_score * learning_multiplier * concentration_score * momentum_score) / risk_adjusted_score;
                
                GoldPool {
                    pool_address: pool.pool_address,
                    token_x: pool.token_x,
                    token_y: pool.token_y,
                    tvl: pool.tvl,
                    volume_24h: pool.volume_24h,
                    fees_24h: pool.fees_24h,
                    gold_score,
                    profit_potential: estimate_profit_potential(&pool, &intelligence_state.learning_data),
                    entry_confidence: calculate_entry_confidence(&pool, &intelligence_state.learning_data),
                    optimal_entry_size: calculate_optimal_entry_size(&pool, intelligence_state.profit_threshold),
                    risk_level: assess_enhanced_risk(&pool),
                    success_probability: calculate_success_probability(&pool, &intelligence_state.learning_data),
                    last_updated: Clock::get()?.unix_timestamp,
                }
            })
            .collect();

        // 🏆 SORT BY GOLD SCORE (HIGHEST PROFIT POTENTIAL FIRST)
        gold_pools.sort_by(|a, b| {
            b.gold_score.partial_cmp(&a.gold_score).unwrap()
        });

        // 🎯 FILTER TO ONLY THE MOST CONCENTRATED GOLD
        gold_pools = gold_pools
            .into_iter()
            .filter(|pool| {
                pool.gold_score > 10.0 && // Minimum gold threshold
                pool.success_probability > 0.75 && // 75%+ success rate
                pool.profit_potential > intelligence_state.profit_threshold
            })
            .take(20) // Top 20 gold pools
            .collect();

        // Update state
        intelligence_state.gold_pools = gold_pools;
        intelligence_state.total_pools_tracked = pool_data.len() as u32;
        intelligence_state.successful_trades_learned += successful_trades.len() as u32;
        intelligence_state.last_update = Clock::get()?.unix_timestamp;

        msg!("💰 Gold Rankings updated! Top pool: {} with {:.2} gold score", 
             intelligence_state.gold_pools[0].pool_address,
             intelligence_state.gold_pools[0].gold_score);

        Ok(())
    }

    // 🎯 GET BEST GOLD OPPORTUNITIES (Public query)
    pub fn get_gold_opportunities(ctx: Context<GetGoldOpportunities>) -> Result<Vec<GoldPool>> {
        let intelligence_state = &ctx.accounts.intelligence_state;
        Ok(intelligence_state.gold_pools.clone())
    }

    // 💎 GET SPECIFIC POOL GOLD ANALYSIS
    pub fn get_pool_gold_analysis(
        ctx: Context<GetPoolGoldAnalysis>,
        pool_address: Pubkey
    ) -> Result<Option<GoldAnalysis>> {
        let intelligence_state = &ctx.accounts.intelligence_state;
        
        let pool = intelligence_state.gold_pools
            .iter()
            .find(|p| p.pool_address == pool_address);
            
        if let Some(pool) = pool {
            let analysis = GoldAnalysis {
                pool_address: pool.pool_address,
                gold_score: pool.gold_score,
                profit_potential: pool.profit_potential,
                entry_confidence: pool.entry_confidence,
                optimal_entry_size: pool.optimal_entry_size,
                success_probability: pool.success_probability,
                recommended_action: if pool.gold_score > 50.0 { 
                    RecommendedAction::StrongBuy 
                } else if pool.gold_score > 25.0 { 
                    RecommendedAction::Buy 
                } else { 
                    RecommendedAction::Monitor 
                },
                risk_warning: if pool.risk_level == EnhancedRiskLevel::High { 
                    Some("High volatility detected - use smaller position size".to_string()) 
                } else { 
                    None 
                },
            };
            Ok(Some(analysis))
        } else {
            Ok(None)
        }
    }

    // 🧠 GET LEARNING INSIGHTS
    pub fn get_learning_insights(ctx: Context<GetLearningInsights>) -> Result<LearningInsights> {
        let intelligence_state = &ctx.accounts.intelligence_state;
        
        let total_trades = intelligence_state.learning_data.len();
        let successful_trades = intelligence_state.learning_data
            .iter()
            .filter(|trade| trade.profit_percentage > 0.0)
            .count();
        
        let success_rate = if total_trades > 0 { 
            successful_trades as f64 / total_trades as f64 
        } else { 
            0.0 
        };
        
        let avg_profit = intelligence_state.learning_data
            .iter()
            .filter(|trade| trade.profit_percentage > 0.0)
            .map(|trade| trade.profit_percentage)
            .sum::<f64>() / successful_trades.max(1) as f64;
        
        Ok(LearningInsights {
            total_trades_learned: total_trades as u32,
            success_rate,
            average_profit_percentage: avg_profit,
            most_profitable_pattern: identify_most_profitable_pattern(&intelligence_state.learning_data),
            confidence_level: calculate_system_confidence(&intelligence_state.learning_data),
        })
    }
}

// 💰 CALCULATE BASE PROFIT SCORE
fn calculate_base_profit_score(pool: &PoolMetrics) -> f64 {
    if pool.tvl == 0.0 { return 0.0; }
    
    let fee_yield = pool.fees_24h / pool.tvl;
    let volume_efficiency = pool.volume_24h / pool.tvl;
    let liquidity_depth = pool.tvl.ln(); // Logarithmic scaling for TVL
    
    // Weighted combination favoring high fee yield
    (fee_yield * 100.0) + (volume_efficiency * 10.0) + (liquidity_depth * 0.1)
}

// 🧠 CALCULATE LEARNING MULTIPLIER (AI Enhancement)
fn calculate_learning_multiplier(pool: &PoolMetrics, learning_data: &[LearningData]) -> f64 {
    let similar_trades: Vec<&LearningData> = learning_data
        .iter()
        .filter(|trade| {
            // Find trades with similar characteristics
            let volume_similarity = (trade.volume_at_entry - pool.volume_24h).abs() / pool.volume_24h.max(1.0);
            let fee_similarity = (trade.fee_concentration_at_entry - (pool.fees_24h / pool.tvl)).abs();
            
            volume_similarity < 0.5 && fee_similarity < 0.1 // Similar patterns
        })
        .collect();
    
    if similar_trades.is_empty() {
        return 1.0; // No learning data, use base score
    }
    
    let avg_success = similar_trades
        .iter()
        .map(|trade| if trade.profit_percentage > 0.0 { 1.0 } else { 0.0 })
        .sum::<f64>() / similar_trades.len() as f64;
    
    let avg_profit = similar_trades
        .iter()
        .filter(|trade| trade.profit_percentage > 0.0)
        .map(|trade| trade.profit_percentage)
        .sum::<f64>() / similar_trades.len().max(1) as f64;
    
    // Boost score based on historical success
    1.0 + (avg_success * avg_profit * 2.0)
}

// 🎯 CALCULATE CONCENTRATION SCORE
fn calculate_concentration_score(pool: &PoolMetrics) -> f64 {
    if pool.tvl == 0.0 { return 0.0; }
    
    let fee_concentration = pool.fees_24h / pool.tvl;
    let volume_concentration = pool.volume_24h / pool.tvl;
    
    // Higher concentration = higher score
    (fee_concentration * 1000.0) + (volume_concentration * 10.0)
}

// 🚀 CALCULATE MOMENTUM SCORE
fn calculate_momentum_score(pool: &PoolMetrics) -> f64 {
    // This would use historical data in a real implementation
    // For now, use volume/TVL ratio as momentum proxy
    if pool.tvl == 0.0 { return 1.0; }
    
    let momentum = pool.volume_24h / pool.tvl;
    (momentum / 10.0).min(5.0).max(0.1) // Cap between 0.1 and 5.0
}

// ⚠️ CALCULATE RISK ADJUSTED SCORE
fn calculate_risk_adjusted_score(pool: &PoolMetrics) -> f64 {
    let volatility = (pool.volume_24h / pool.tvl.max(1.0)).min(100.0);
    let liquidity_risk = if pool.tvl < 10000.0 { 2.0 } else { 1.0 };
    
    (1.0 + (volatility / 100.0)) * liquidity_risk
}

// 💎 ESTIMATE PROFIT POTENTIAL
fn estimate_profit_potential(pool: &PoolMetrics, learning_data: &[LearningData]) -> f64 {
    // Base potential from fee yield
    let base_potential = if pool.tvl > 0.0 { 
        (pool.fees_24h / pool.tvl) * 365.0 // Annualized
    } else { 
        0.0 
    };
    
    // Enhance with learning data
    let learning_boost = calculate_learning_multiplier(pool, learning_data) - 1.0;
    
    base_potential * (1.0 + learning_boost)
}

// 🎯 CALCULATE ENTRY CONFIDENCE
fn calculate_entry_confidence(pool: &PoolMetrics, learning_data: &[LearningData]) -> f64 {
    let base_confidence = if pool.tvl > 50000.0 { 0.7 } else { 0.5 };
    let learning_confidence = calculate_success_probability(pool, learning_data);
    
    (base_confidence + learning_confidence) / 2.0
}

// 💰 CALCULATE OPTIMAL ENTRY SIZE
fn calculate_optimal_entry_size(pool: &PoolMetrics, profit_threshold: f64) -> f64 {
    // Kelly Criterion-inspired sizing
    let win_rate = 0.6; // Conservative estimate
    let avg_win = profit_threshold * 2.0;
    let avg_loss = profit_threshold * 0.5;
    
    let kelly_fraction = (win_rate * avg_win - (1.0 - win_rate) * avg_loss) / avg_win;
    
    // Conservative sizing: 1-5% of capital
    (kelly_fraction * 0.5).min(0.05).max(0.01)
}

// 📊 CALCULATE SUCCESS PROBABILITY
fn calculate_success_probability(pool: &PoolMetrics, learning_data: &[LearningData]) -> f64 {
    if learning_data.is_empty() { return 0.5; }
    
    let similar_trades = learning_data
        .iter()
        .filter(|trade| {
            let volume_match = (trade.volume_at_entry - pool.volume_24h).abs() / pool.volume_24h.max(1.0) < 0.3;
            volume_match
        })
        .collect::<Vec<_>>();
    
    if similar_trades.is_empty() { return 0.5; }
    
    let success_count = similar_trades
        .iter()
        .filter(|trade| trade.profit_percentage > 0.0)
        .count();
    
    success_count as f64 / similar_trades.len() as f64
}

// 🔍 IDENTIFY SUCCESS PATTERN
fn identify_success_pattern(trade: &TradeResult) -> SuccessPattern {
    if trade.profit_percentage > 0.5 {
        SuccessPattern::HighProfit
    } else if trade.profit_percentage > 0.1 {
        SuccessPattern::MediumProfit
    } else if trade.profit_percentage > 0.0 {
        SuccessPattern::SmallProfit
    } else {
        SuccessPattern::Loss
    }
}

// 📈 IDENTIFY MOST PROFITABLE PATTERN
fn identify_most_profitable_pattern(learning_data: &[LearningData]) -> String {
    // Analyze patterns and return the most profitable one
    "High volume concentration with medium TVL".to_string()
}

// 🎯 CALCULATE SYSTEM CONFIDENCE
fn calculate_system_confidence(learning_data: &[LearningData]) -> f64 {
    if learning_data.len() < 10 { return 0.3; }
    if learning_data.len() < 50 { return 0.6; }
    if learning_data.len() < 100 { return 0.8; }
    0.95
}

// 📊 DATA STRUCTURES
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PoolMetrics {
    pub pool_address: Pubkey,
    pub token_x: Pubkey,
    pub token_y: Pubkey,
    pub tvl: f64,
    pub volume_24h: f64,
    pub fees_24h: f64,
    pub bin_step: u16,
    pub active_bin: i32,
}

#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct GoldPool {
    pub pool_address: Pubkey,
    pub token_x: Pubkey,
    pub token_y: Pubkey,
    pub tvl: f64,
    pub volume_24h: f64,
    pub fees_24h: f64,
    pub gold_score: f64,           // 🏆 THE ULTIMATE METRIC
    pub profit_potential: f64,     // Expected annual return
    pub entry_confidence: f64,     // 0.0 to 1.0
    pub optimal_entry_size: f64,   // Fraction of capital
    pub risk_level: EnhancedRiskLevel,
    pub success_probability: f64,  // Based on learning
    pub last_updated: i64,
}

#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct TradeResult {
    pub pool_address: Pubkey,
    pub entry_price: f64,
    pub exit_price: f64,
    pub profit_percentage: f64,
    pub volume_at_entry: f64,
    pub fee_concentration_at_entry: f64,
}

#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct LearningData {
    pub pool_address: Pubkey,
    pub entry_price: f64,
    pub exit_price: f64,
    pub profit_percentage: f64,
    pub volume_at_entry: f64,
    pub fee_concentration_at_entry: f64,
    pub timestamp: i64,
    pub success_pattern: SuccessPattern,
}

#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct GoldAnalysis {
    pub pool_address: Pubkey,
    pub gold_score: f64,
    pub profit_potential: f64,
    pub entry_confidence: f64,
    pub optimal_entry_size: f64,
    pub success_probability: f64,
    pub recommended_action: RecommendedAction,
    pub risk_warning: Option<String>,
}

#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct LearningInsights {
    pub total_trades_learned: u32,
    pub success_rate: f64,
    pub average_profit_percentage: f64,
    pub most_profitable_pattern: String,
    pub confidence_level: f64,
}

#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub enum EnhancedRiskLevel {
    VeryLow,
    Low,
    Medium,
    High,
    VeryHigh,
}

#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub enum SuccessPattern {
    HighProfit,
    MediumProfit,
    SmallProfit,
    Loss,
}

#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub enum RecommendedAction {
    StrongBuy,
    Buy,
    Monitor,
    Avoid,
}

#[account]
pub struct IntelligenceState {
    pub authority: Pubkey,
    pub total_pools_tracked: u32,
    pub successful_trades_learned: u32,
    pub last_update: i64,
    pub gold_pools: Vec<GoldPool>,
    pub learning_data: Vec<LearningData>,
    pub profit_threshold: f64,
}

// 🔧 INSTRUCTION CONTEXTS
#[derive(Accounts)]
pub struct Initialize<'info> {
    #[account(
        init,
        payer = authority,
        space = 8 + 32 + 4 + 4 + 8 + 4 + (20 * 300) + 4 + (1000 * 200) + 8,
        seeds = [b"gold_intelligence"],
        bump
    )]
    pub intelligence_state: Account<'info, IntelligenceState>,
    
    #[account(mut)]
    pub authority: Signer<'info>,
    
    pub system_program: Program<'info, System>,
}

#[derive(Accounts)]
pub struct UpdateGoldRankings<'info> {
    #[account(
        mut,
        seeds = [b"gold_intelligence"],
        bump,
        has_one = authority
    )]
    pub intelligence_state: Account<'info, IntelligenceState>,
    
    pub authority: Signer<'info>,
}

#[derive(Accounts)]
pub struct GetGoldOpportunities<'info> {
    #[account(
        seeds = [b"gold_intelligence"],
        bump
    )]
    pub intelligence_state: Account<'info, IntelligenceState>,
}

#[derive(Accounts)]
pub struct GetPoolGoldAnalysis<'info> {
    #[account(
        seeds = [b"gold_intelligence"],
        bump
    )]
    pub intelligence_state: Account<'info, IntelligenceState>,
}

#[derive(Accounts)]
pub struct GetLearningInsights<'info> {
    #[account(
        seeds = [b"gold_intelligence"],
        bump
    )]
    pub intelligence_state: Account<'info, IntelligenceState>,
}

#[error_code]
pub enum GoldExtractorError {
    #[msg("Unauthorized access")]
    Unauthorized,
    #[msg("Invalid pool data")]
    InvalidPoolData,
    #[msg("Insufficient learning data")]
    InsufficientLearningData,
}
