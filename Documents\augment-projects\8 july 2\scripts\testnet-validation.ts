import { ethers } from 'hardhat';
import { Contract } from 'ethers';
import { deployUltraEfficientSystem } from './deploy-ultra-efficient';

interface ValidationResult {
  testName: string;
  passed: boolean;
  gasUsed?: number;
  error?: string;
  details?: any;
}

interface TestnetValidationReport {
  network: string;
  timestamp: string;
  contractAddresses: {
    flashLoan: string;
    executor: string;
  };
  validationResults: ValidationResult[];
  overallSuccess: boolean;
  totalGasUsed: number;
  estimatedMainnetCost: string;
  recommendations: string[];
}

class TestnetValidator {
  private flashLoanContract!: Contract;
  private arbitrageExecutor!: Contract;
  private network!: string;
  private results: ValidationResult[] = [];

  async runFullValidation(): Promise<TestnetValidationReport> {
    console.log('\n' + '🧪'.repeat(80));
    console.log('🔬 ULTRA-EFFICIENT TESTNET VALIDATION SUITE');
    console.log('🧪'.repeat(80));

    // Step 1: Deploy contracts to testnet
    console.log('\n🚀 STEP 1: Deploying to testnet...');
    const deployment = await deployUltraEfficientSystem();
    
    this.flashLoanContract = deployment.flashLoanContract;
    this.arbitrageExecutor = deployment.arbitrageExecutor;
    this.network = (await ethers.provider.getNetwork()).name;

    console.log(`✅ Contracts deployed successfully!`);
    console.log(`   Flash Loan: ${deployment.addresses.flashLoan}`);
    console.log(`   Executor: ${deployment.addresses.executor}`);

    // Step 2: Run validation tests
    console.log('\n🧪 STEP 2: Running validation tests...');
    
    await this.validateContractConfiguration();
    await this.validateSafetyFeatures();
    await this.validateGasOptimization();
    await this.validateStrategyConfiguration();
    await this.validateAccessControl();
    await this.validateEventEmission();
    await this.validateEdgeCases();

    // Step 3: Generate report
    const report = this.generateReport(deployment.addresses);
    await this.saveReport(report);
    this.displayReport(report);

    return report;
  }

  private async validateContractConfiguration(): Promise<void> {
    console.log('   🔧 Testing contract configuration...');
    
    try {
      const safetyConfig = await this.flashLoanContract.getSafetyConfig();
      
      const passed = 
        safetyConfig.maxGasUsed.eq(400000) &&
        safetyConfig.minProfitRequired.eq(ethers.utils.parseUnits('500', 6)) &&
        !safetyConfig.emergencyStop;

      this.results.push({
        testName: 'Contract Configuration',
        passed,
        details: {
          maxGasUsed: safetyConfig.maxGasUsed.toString(),
          minProfitRequired: ethers.utils.formatUnits(safetyConfig.minProfitRequired, 6),
          emergencyStop: safetyConfig.emergencyStop
        }
      });

      console.log(`      ${passed ? '✅' : '❌'} Contract configuration`);
    } catch (error) {
      this.results.push({
        testName: 'Contract Configuration',
        passed: false,
        error: error.message
      });
      console.log(`      ❌ Contract configuration failed: ${error.message}`);
    }
  }

  private async validateSafetyFeatures(): Promise<void> {
    console.log('   🛡️  Testing safety features...');
    
    // Test emergency stop
    try {
      const tx = await this.flashLoanContract.emergencyStop('Test emergency stop');
      const receipt = await tx.wait();
      
      const safetyConfig = await this.flashLoanContract.getSafetyConfig();
      const isPaused = await this.flashLoanContract.paused();
      
      const passed = safetyConfig.emergencyStop && isPaused;
      
      this.results.push({
        testName: 'Emergency Stop',
        passed,
        gasUsed: receipt.gasUsed.toNumber(),
        details: {
          emergencyStop: safetyConfig.emergencyStop,
          paused: isPaused
        }
      });

      // Resume operations for further tests
      await this.flashLoanContract.resumeOperations();
      
      console.log(`      ${passed ? '✅' : '❌'} Emergency stop functionality`);
    } catch (error) {
      this.results.push({
        testName: 'Emergency Stop',
        passed: false,
        error: error.message
      });
      console.log(`      ❌ Emergency stop failed: ${error.message}`);
    }
  }

  private async validateGasOptimization(): Promise<void> {
    console.log('   ⚡ Testing gas optimization...');
    
    try {
      // Test gas usage for key functions
      const estimateGas = await this.flashLoanContract.estimateGas.getSafetyConfig();
      
      const passed = estimateGas.toNumber() < 50000; // Should be very low for view function
      
      this.results.push({
        testName: 'Gas Optimization',
        passed,
        gasUsed: estimateGas.toNumber(),
        details: {
          viewFunctionGas: estimateGas.toNumber(),
          threshold: 50000
        }
      });

      console.log(`      ${passed ? '✅' : '❌'} Gas optimization (${estimateGas.toNumber()} gas)`);
    } catch (error) {
      this.results.push({
        testName: 'Gas Optimization',
        passed: false,
        error: error.message
      });
      console.log(`      ❌ Gas optimization test failed: ${error.message}`);
    }
  }

  private async validateStrategyConfiguration(): Promise<void> {
    console.log('   📊 Testing strategy configuration...');
    
    try {
      const strategyCount = await this.arbitrageExecutor.strategyCount();
      const activeStrategies = await this.arbitrageExecutor.getActiveStrategies();
      
      const passed = strategyCount.eq(3) && activeStrategies.length === 3;
      
      // Test individual strategies
      const strategy0 = await this.arbitrageExecutor.getStrategy(0);
      const strategy0Valid = 
        strategy0.name === 'USDC-WETH-Ultra' &&
        strategy0.expectedProfit.eq(ethers.utils.parseUnits('4320', 6)) &&
        strategy0.active;

      this.results.push({
        testName: 'Strategy Configuration',
        passed: passed && strategy0Valid,
        details: {
          totalStrategies: strategyCount.toNumber(),
          activeStrategies: activeStrategies.length,
          strategy0Name: strategy0.name,
          strategy0Profit: ethers.utils.formatUnits(strategy0.expectedProfit, 6),
          strategy0Active: strategy0.active
        }
      });

      console.log(`      ${passed && strategy0Valid ? '✅' : '❌'} Strategy configuration`);
    } catch (error) {
      this.results.push({
        testName: 'Strategy Configuration',
        passed: false,
        error: error.message
      });
      console.log(`      ❌ Strategy configuration failed: ${error.message}`);
    }
  }

  private async validateAccessControl(): Promise<void> {
    console.log('   🔐 Testing access control...');
    
    try {
      const [owner] = await ethers.getSigners();
      
      // Test authorized caller
      const isAuthorized = await this.flashLoanContract.authorizedCallers(this.arbitrageExecutor.address);
      const isOwnerAuthorized = await this.flashLoanContract.authorizedCallers(owner.address);
      
      const passed = isAuthorized && isOwnerAuthorized;
      
      this.results.push({
        testName: 'Access Control',
        passed,
        details: {
          executorAuthorized: isAuthorized,
          ownerAuthorized: isOwnerAuthorized
        }
      });

      console.log(`      ${passed ? '✅' : '❌'} Access control`);
    } catch (error) {
      this.results.push({
        testName: 'Access Control',
        passed: false,
        error: error.message
      });
      console.log(`      ❌ Access control failed: ${error.message}`);
    }
  }

  private async validateEventEmission(): Promise<void> {
    console.log('   📡 Testing event emission...');
    
    try {
      // Test emergency stop event
      const tx = await this.flashLoanContract.emergencyStop('Test event emission');
      const receipt = await tx.wait();
      
      const emergencyEvent = receipt.events?.find(e => e.event === 'EmergencyStop');
      const passed = !!emergencyEvent;
      
      // Resume operations
      await this.flashLoanContract.resumeOperations();
      
      this.results.push({
        testName: 'Event Emission',
        passed,
        gasUsed: receipt.gasUsed.toNumber(),
        details: {
          emergencyEventEmitted: !!emergencyEvent,
          eventArgs: emergencyEvent?.args
        }
      });

      console.log(`      ${passed ? '✅' : '❌'} Event emission`);
    } catch (error) {
      this.results.push({
        testName: 'Event Emission',
        passed: false,
        error: error.message
      });
      console.log(`      ❌ Event emission failed: ${error.message}`);
    }
  }

  private async validateEdgeCases(): Promise<void> {
    console.log('   🎯 Testing edge cases...');
    
    try {
      // Test invalid strategy ID
      let edgeCasePassed = false;
      try {
        await this.arbitrageExecutor.getStrategy(999);
      } catch (error) {
        edgeCasePassed = true; // Should revert
      }

      // Test strategy profitability check
      const [profitable, estimatedProfit, estimatedGas] = await this.arbitrageExecutor.checkStrategyProfitability(0);
      
      const passed = edgeCasePassed && profitable && estimatedGas.eq(360000);
      
      this.results.push({
        testName: 'Edge Cases',
        passed,
        details: {
          invalidStrategyReverts: edgeCasePassed,
          profitabilityCheck: profitable,
          estimatedGas: estimatedGas.toNumber()
        }
      });

      console.log(`      ${passed ? '✅' : '❌'} Edge cases`);
    } catch (error) {
      this.results.push({
        testName: 'Edge Cases',
        passed: false,
        error: error.message
      });
      console.log(`      ❌ Edge cases failed: ${error.message}`);
    }
  }

  private generateReport(addresses: { flashLoan: string; executor: string }): TestnetValidationReport {
    const passedTests = this.results.filter(r => r.passed).length;
    const totalTests = this.results.length;
    const overallSuccess = passedTests === totalTests;
    
    const totalGasUsed = this.results
      .filter(r => r.gasUsed)
      .reduce((sum, r) => sum + (r.gasUsed || 0), 0);

    // Estimate mainnet cost (assuming 30 gwei gas price and $2000 ETH)
    const estimatedMainnetCost = ethers.utils.formatEther(
      ethers.BigNumber.from(totalGasUsed).mul(ethers.utils.parseUnits('30', 'gwei'))
    );

    const recommendations: string[] = [];
    
    if (!overallSuccess) {
      recommendations.push('❌ Fix failing tests before mainnet deployment');
    }
    
    if (totalGasUsed > 1000000) {
      recommendations.push('⚡ Consider further gas optimizations');
    }
    
    if (overallSuccess) {
      recommendations.push('✅ Ready for mainnet deployment with small amounts');
      recommendations.push('📊 Start with minimum profitable opportunity size');
      recommendations.push('🔍 Monitor first few transactions closely');
      recommendations.push('📈 Scale up gradually after confirming profitability');
    }

    return {
      network: this.network,
      timestamp: new Date().toISOString(),
      contractAddresses: addresses,
      validationResults: this.results,
      overallSuccess,
      totalGasUsed,
      estimatedMainnetCost,
      recommendations
    };
  }

  private displayReport(report: TestnetValidationReport): void {
    console.log('\n' + '📊'.repeat(80));
    console.log('🎯 TESTNET VALIDATION REPORT');
    console.log('📊'.repeat(80));
    
    console.log(`\n📋 SUMMARY:`);
    console.log(`   🌐 Network: ${report.network}`);
    console.log(`   ⏰ Timestamp: ${report.timestamp}`);
    console.log(`   ✅ Tests Passed: ${report.validationResults.filter(r => r.passed).length}/${report.validationResults.length}`);
    console.log(`   ⚡ Total Gas Used: ${report.totalGasUsed.toLocaleString()}`);
    console.log(`   💰 Estimated Mainnet Cost: ${report.estimatedMainnetCost} ETH`);
    console.log(`   🎯 Overall Success: ${report.overallSuccess ? '✅ PASSED' : '❌ FAILED'}`);

    console.log(`\n📊 DETAILED RESULTS:`);
    report.validationResults.forEach(result => {
      console.log(`   ${result.passed ? '✅' : '❌'} ${result.testName}`);
      if (result.gasUsed) {
        console.log(`      ⚡ Gas: ${result.gasUsed.toLocaleString()}`);
      }
      if (result.error) {
        console.log(`      ❌ Error: ${result.error}`);
      }
    });

    console.log(`\n🎯 RECOMMENDATIONS:`);
    report.recommendations.forEach(rec => {
      console.log(`   ${rec}`);
    });

    console.log('\n📊'.repeat(80) + '\n');
  }

  private async saveReport(report: TestnetValidationReport): Promise<void> {
    try {
      const fs = require('fs');
      const path = require('path');
      
      const reportsDir = path.join(process.cwd(), 'validation-reports');
      if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
      }
      
      const filename = `testnet-validation-${Date.now()}.json`;
      const filepath = path.join(reportsDir, filename);
      
      fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
      console.log(`💾 Validation report saved to: ${filename}`);
      
    } catch (error) {
      console.error('❌ Failed to save validation report:', error);
    }
  }
}

// Main execution function
async function runTestnetValidation() {
  try {
    const validator = new TestnetValidator();
    const report = await validator.runFullValidation();
    
    if (report.overallSuccess) {
      console.log('🎉 TESTNET VALIDATION SUCCESSFUL! Ready for mainnet deployment.');
      process.exit(0);
    } else {
      console.log('❌ TESTNET VALIDATION FAILED! Fix issues before mainnet deployment.');
      process.exit(1);
    }
  } catch (error) {
    console.error('💥 Validation failed with error:', error);
    process.exit(1);
  }
}

// Execute validation if run directly
if (require.main === module) {
  runTestnetValidation().catch(console.error);
}

export { TestnetValidator, runTestnetValidation };
