// 🔥 SIMPLE TEST - Direct Blockchain Intelligence Demo
import { Connection, PublicKey } from '@solana/web3.js';

console.log('🔥 TESTING DIRECT BLOCKCHAIN ACCESS TO METEORA DLMM');
console.log('💰 NO DEPLOYMENT COSTS - Reading directly from source!');
console.log('═'.repeat(60));

async function testDirectAccess() {
  try {
    // Connect to Solana mainnet
    const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
    console.log('✅ Connected to Solana mainnet');
    
    // Meteora DLMM program ID
    const METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
    console.log('🎯 Meteora DLMM Program:', METEORA_DLMM_PROGRAM.toString());
    
    // Get ALL accounts owned by Meteora DLMM program
    console.log('\n🔍 Scanning for DLMM pool accounts...');
    const accounts = await connection.getProgramAccounts(METEORA_DLMM_PROGRAM, {
      dataSlice: { offset: 0, length: 0 } // Just get account addresses first
    });
    
    console.log(`📊 Found ${accounts.length} accounts owned by Meteora DLMM program`);
    
    if (accounts.length > 0) {
      console.log('\n🎯 Sample Account Addresses:');
      accounts.slice(0, 10).forEach((account, index) => {
        console.log(`   ${index + 1}. ${account.pubkey.toString()}`);
      });
      
      // Get detailed data for first account
      console.log('\n📋 Getting detailed data for first account...');
      const firstAccount = accounts[0];
      const accountInfo = await connection.getAccountInfo(firstAccount.pubkey);
      
      if (accountInfo) {
        console.log(`   Owner: ${accountInfo.owner.toString()}`);
        console.log(`   Data Length: ${accountInfo.data.length} bytes`);
        console.log(`   Lamports: ${accountInfo.lamports}`);
        console.log(`   Executable: ${accountInfo.executable}`);
        
        // Show first 32 bytes of data (hex)
        const dataPreview = accountInfo.data.slice(0, 32).toString('hex');
        console.log(`   Data Preview: ${dataPreview}`);
      }
    }
    
    // Test transaction history
    console.log('\n📈 Testing transaction history access...');
    if (accounts.length > 0) {
      const testAccount = accounts[0].pubkey;
      const signatures = await connection.getSignaturesForAddress(testAccount, { limit: 5 });
      
      console.log(`📊 Found ${signatures.length} recent transactions for test account`);
      
      if (signatures.length > 0) {
        console.log('\n🔍 Recent Transaction Signatures:');
        signatures.forEach((sig, index) => {
          console.log(`   ${index + 1}. ${sig.signature}`);
          console.log(`      Block Time: ${new Date(sig.blockTime * 1000).toLocaleString()}`);
          console.log(`      Slot: ${sig.slot}`);
        });
      }
    }
    
    console.log('\n' + '═'.repeat(60));
    console.log('🎉 DIRECT BLOCKCHAIN ACCESS SUCCESSFUL!');
    console.log('✅ We can read ALL Meteora DLMM data directly from blockchain');
    console.log('💰 Cost: $0 deployment + ~$0.01/hour RPC calls');
    console.log('🧠 Intelligence: Complete access to source of truth');
    console.log('🚀 Next: Parse pool data and find profitable opportunities');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Run the test
testDirectAccess();
