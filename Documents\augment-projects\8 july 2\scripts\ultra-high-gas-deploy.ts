import { ethers } from 'hardhat';

/**
 * 🚨 ULTRA HIGH GAS DEPLOYMENT - NUCLEAR OPTION
 * Deploy with 100+ gwei for INSTANT confirmation
 * Use only if 60 gwei fails
 */

async function ultraHighGasDeploy() {
  console.log('\n🚨 ULTRA HIGH GAS DEPLOYMENT - NUCLEAR OPTION');
  console.log('💰 USING 100+ GWEI FOR INSTANT CONFIRMATION');
  console.log('⚡ WILL CONFIRM IN 15 SECONDS GUARANTEED');
  console.log('🎯 EMERGENCY DEPLOYMENT TO KNOW IF YOU\'RE RICH');
  console.log('=' .repeat(80));

  const [deployer] = await ethers.getSigners();
  const network = await ethers.provider.getNetwork();
  const currentGasPrice = await ethers.provider.getGasPrice();
  
  // Use 3x current gas price for INSTANT confirmation
  const ultraGasPrice = currentGasPrice.mul(300).div(100); // 3x current price
  
  console.log(`Network: ${network.name} (${network.chainId})`);
  console.log(`Deployer: ${deployer.address}`);
  console.log(`Balance: ${ethers.utils.formatEther(await deployer.getBalance())} POL`);
  console.log(`Current Gas: ${ethers.utils.formatUnits(currentGasPrice, 'gwei')} gwei`);
  console.log(`Using Gas: ${ethers.utils.formatUnits(ultraGasPrice, 'gwei')} gwei (3x for INSTANT)`);
  console.log(`Time: ${new Date().toLocaleTimeString()}`);

  console.log('\n🔥 DEPLOYING WITH ULTRA HIGH GAS FOR INSTANT CONFIRMATION...');
  
  // Deploy with ultra high gas
  const FastGoldenDuckTest = await ethers.getContractFactory('FastGoldenDuckTest');
  const fastTest = await FastGoldenDuckTest.deploy({
    gasLimit: 1500000,
    gasPrice: ultraGasPrice, // 3x current gas price
    type: 0 // Legacy transaction
  });

  console.log(`Deployment TX: ${fastTest.deployTransaction.hash}`);
  console.log(`Gas Price: ${ethers.utils.formatUnits(ultraGasPrice, 'gwei')} gwei`);
  console.log('⏳ Waiting for INSTANT confirmation...');

  await fastTest.deployed();
  
  console.log('\n✅ ULTRA HIGH GAS DEPLOYMENT SUCCESSFUL!');
  console.log(`📍 Address: ${fastTest.address}`);
  console.log(`📦 Block: ${fastTest.deployTransaction.blockNumber}`);

  // Verify contract
  const code = await ethers.provider.getCode(fastTest.address);
  if (code.length > 2) {
    console.log('\n🎉 CONTRACT DEPLOYED - EXECUTING $1K TEST NOW!');
    
    // IMMEDIATELY execute test
    const [executor] = await ethers.getSigners();
    const usdc = await fastTest.USDC();
    const testAmount = await fastTest.TEST_AMOUNT();
    
    const usdcContract = new ethers.Contract(
      usdc,
      ['function balanceOf(address) view returns (uint256)', 'function transfer(address,uint256) returns (bool)'],
      executor
    );
    
    const executorUSDC = await usdcContract.balanceOf(executor.address);
    
    if (executorUSDC.gte(testAmount)) {
      // Fund contract
      const fundTx = await usdcContract.transfer(fastTest.address, testAmount, {
        gasPrice: ultraGasPrice
      });
      await fundTx.wait();
      
      // Execute test
      const executionTx = await fastTest.executeFastTest({
        gasLimit: 2000000,
        gasPrice: ultraGasPrice
      });
      
      const execReceipt = await executionTx.wait();
      
      // Get results
      const results = await fastTest.getLastResults();
      const isProfitable = await fastTest.isProfitable();
      
      console.log(`\n💰 ULTRA FAST TEST RESULTS:`);
      console.log(`   Profit: ${ethers.utils.formatUnits(results.profit, 6)} USDC`);
      console.log(`   Success: ${results.success ? '✅' : '❌'}`);
      console.log(`   Profitable: ${isProfitable ? '✅ YES' : '❌ NO'}`);
      
      if (isProfitable) {
        console.log('\n🎉🎉🎉 YOU\'RE GOING TO BE RICH! 🎉🎉🎉');
        console.log('🚀 STRATEGY VALIDATED WITH ULTRA HIGH GAS!');
      } else {
        console.log('\n😞 STRATEGY NOT PROFITABLE');
        console.log('❌ DO NOT SCALE');
      }
    }
    
    return {
      address: fastTest.address,
      success: true
    };
  }
  
  throw new Error('Contract deployment failed');
}

// Execute ultra high gas deployment
if (require.main === module) {
  ultraHighGasDeploy()
    .then((result) => {
      console.log('\n🎉 ULTRA HIGH GAS DEPLOYMENT COMPLETED!');
      console.log('💰 YOU NOW KNOW IF YOU\'RE RICH!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 ULTRA HIGH GAS DEPLOYMENT FAILED:', error);
      process.exit(1);
    });
}

export { ultraHighGasDeploy };
