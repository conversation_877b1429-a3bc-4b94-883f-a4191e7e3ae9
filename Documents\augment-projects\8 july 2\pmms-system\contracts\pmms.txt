---Path and files name: pmms/FlashloanExecutor.sol
---Filename: FlashloanExecutor.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "./interfaces/IStrategy.sol";
import "./interfaces/IRegistry.sol";
import "@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract x_FlashloanExecutor is FlashLoanSimpleReceiverBase, Ownable {
    IRegistry public immutable registry;
    address public strategyExecutor;

    event FlashloanExecuted(address indexed asset, uint256 amount, uint256 premium, bool success);

    constructor(
        IPoolAddressesProvider provider,
        address _registry,
        address _strategyExecutor,
        address _owner
    ) FlashLoanSimpleReceiverBase(provider) Ownable(_owner) {
        require(_registry != address(0) && _strategyExecutor != address(0), "Invalid addresses");
        registry = IRegistry(_registry);
        strategyExecutor = _strategyExecutor;
    }

    function executeStrategy(
        address asset,
        uint256 amount,
        bytes calldata params
    ) external onlyOwner {
        POOL.flashLoanSimple(
            address(this),
            asset,
            amount,
            params,
            0
        );
    }

    function executeOperation(
        address asset,
        uint256 amount,
        uint256 premium,
        address initiator,
        bytes calldata params
    ) external override returns (bool) {
        require(msg.sender == address(POOL), "Unauthorized");
        require(initiator == address(this), "Invalid initiator");

        // Find and execute best strategy
        (bool success, bytes memory result) = strategyExecutor.call(
            abi.encodeWithSignature(
                "findAndExecute(address,uint256,uint256)",
                asset,
                amount,
                premium
            )
        );

        // Approve repayment
        uint256 totalAmount = amount + premium;
        IERC20(asset).approve(address(POOL), totalAmount);

        emit FlashloanExecuted(asset, amount, premium, success);
        return success;
    }

    function updateStrategyExecutor(address newExecutor) external onlyOwner {
        require(newExecutor != address(0), "Invalid executor address");
        strategyExecutor = newExecutor;
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockQuoter.sol
---Filename: MockQuoter.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;
contract MockQuoter {
    uint256 public constant RATE = 1e18; // 1:1 for testing
    function quoteExactInputSingle(
        address tokenIn,
        address tokenOut,
        uint24 fee,
        uint256 amountIn,
        uint160 sqrtPriceLimitX96
    ) external view returns (uint256 amountOut) {
        return (amountIn * RATE) / 1e18;
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockFlashloanExecutor.sol
---Filename: MockFlashloanExecutor.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";

contract MockFlashloanExecutor is Initializable, UUPSUpgradeable, OwnableUpgradeable {
    address public strategyExecutor;

    function initialize(address _owner, address _strategyExecutor) external initializer {
        __Ownable_init(_owner); // Pass the owner explicitly
        __UUPSUpgradeable_init();

        strategyExecutor = _strategyExecutor;
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function executeOperation(
        address[] calldata assets,
        uint256[] calldata amounts,
        uint256[] calldata premiums,
        address initiator,
        bytes calldata params
    ) external returns (bool) {
        return true;
    }
}
---File Content End---

---Path and files name: pmms/contracts/mocks/MockPool.sol
---Filename: MockPool.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@aave/core-v3/contracts/flashloan/interfaces/IFlashLoanSimpleReceiver.sol";

contract MockPool {
    function flashLoanSimple(
        address receiverAddress,
        address asset,
        uint256 amount,
        bytes calldata params,
        uint16 /* referralCode */
    ) external {
        IERC20 token = IERC20(asset);
        token.transfer(receiverAddress, amount); // Simulate loan
        require(
            IFlashLoanSimpleReceiver(receiverAddress).executeOperation(
                asset,
                amount,
                0,
                msg.sender,
                params
            ),
            "Flash loan execution failed"
        );
        token.transferFrom(receiverAddress, address(this), amount); // Simulate repayment
    }

    // Other Aave pool methods are NOT needed unless explicitly called
}
---File Content End---

---Path and files name: pmms/contracts/mocks/MockConvexBooster.sol
---Filename: MockConvexBooster.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;
contract MockConvexBooster {
    function deposit(uint256 _pid, uint256 _amount, bool _stake) external returns (bool) {
        return true;
    }
    function withdraw(uint256 _pid, uint256 _amount) external returns (bool) {
        return true;
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockCurve3Pool.sol
---Filename: MockCurve3Pool.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "../interfaces/IRegistry.sol";

contract MockCurve3Pool {
    function get_dy(int128 i, int128 j, uint256 dx) external view returns (uint256 dy) {
        return dx * 997 / 1000; // Simulate 0.3% fee
    }

    function exchange(int128 i, int128 j, uint256 dx, uint256 min_dy) external returns (uint256) {
        uint256 dy = dx * 997 / 1000;
        require(dy >= min_dy, "Insufficient output");
        address tokenIn = i == 1 ? IRegistry(msg.sender).getAddress("USDC") : IRegistry(msg.sender).getAddress("USDT");
        address tokenOut = j == 1 ? IRegistry(msg.sender).getAddress("USDC") : IRegistry(msg.sender).getAddress("USDT");
        IERC20(tokenIn).transferFrom(msg.sender, address(this), dx);
        IERC20(tokenOut).transfer(msg.sender, dy);
        return dy;
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockERC20.sol
---Filename: MockERC20.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

contract MockERC20 is ERC20 {
    constructor(string memory name, string memory symbol) ERC20(name, symbol) {
        _mint(msg.sender, 1_000_000 * 10**18); // Mint 1M tokens
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockUSDC.sol
---Filename: MockUSDC.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

contract MockUSDC is ERC20 {
    constructor() ERC20("Mock USDC", "USDC") {
        _mint(msg.sender, 1_000_000 * 10**18);
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockUSDT.sol
---Filename: MockUSDT.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

contract MockUSDT is ERC20 {
    constructor() ERC20("Mock USDT", "USDT") {
        _mint(msg.sender, 1_000_000 * 10**18);
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockGasOracle.sol
---Filename: MockGasOracle.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;
contract MockGasOracle {
    uint256 public gasPrice = **********; // 1 gwei
    function getGasPrice() external view returns (uint256) {
        return gasPrice;
    }
    function setGasPrice(uint256 _gasPrice) external {
        gasPrice = _gasPrice;
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockSwapRouter.sol
---Filename: MockSwapRouter.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
contract MockSwapRouter {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }
    uint256 public constant RATE = 1e18; // 1:1 for testing
    function exactInputSingle(ExactInputSingleParams calldata params) external returns (uint256 amountOut) {
        require(block.timestamp <= params.deadline, "Expired");
        require(params.amountOutMinimum <= params.amountIn, "Insufficient output");
        IERC20(params.tokenIn).transferFrom(msg.sender, address(this), params.amountIn);
        IERC20(params.tokenOut).transfer(params.recipient, params.amountOutMinimum);
        return params.amountOutMinimum;
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockAaveLendingPool.sol
---Filename: MockAaveLendingPool.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
contract MockAaveLendingPool {
    mapping(address => mapping(address => uint256)) public deposits;
    mapping(address => mapping(address => uint256)) public borrows;
    function deposit(address asset, uint256 amount, address onBehalfOf, uint16 referralCode) external {
        IERC20(asset).transferFrom(msg.sender, address(this), amount);
        deposits[onBehalfOf][asset] += amount;
    }
    function borrow(address asset, uint256 amount, uint256 interestRateMode, uint16 referralCode, address onBehalfOf) external {
        require(deposits[onBehalfOf][asset] >= amount, "Insufficient collateral");
        IERC20(asset).transfer(onBehalfOf, amount);
        borrows[onBehalfOf][asset] += amount;
    }
    function repay(address asset, uint256 amount, uint256 rateMode, address onBehalfOf) external returns (uint256) {
        IERC20(asset).transferFrom(msg.sender, address(this), amount);
        borrows[onBehalfOf][asset] -= amount;
        return amount;
    }
    function getUserAccountData(address user)
        external
        view
        returns (
            uint256 totalCollateralETH,
            uint256 totalDebtETH,
            uint256 availableBorrowsETH,
            uint256 currentLiquidationThreshold,
            uint256 ltv,
            uint256 healthFactor
        )
    {
        totalCollateralETH = deposits[user][address(0)] * 1e18;
        totalDebtETH = borrows[user][address(0)] * 1e18;
        availableBorrowsETH = (totalCollateralETH * 8000) / 10000 - totalDebtETH;
        currentLiquidationThreshold = 8500;
        ltv = 8000;
        healthFactor = totalDebtETH == 0 ? type(uint256).max : (totalCollateralETH * 8500) / totalDebtETH;
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockIMABridge.sol
---Filename: MockIMABridge.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;
contract MockIMABridge {
    event MessageSent(address sender, address recipient, bytes data);
    event MessageReceived(address sender, address recipient, bytes data);
    function sendMessage(address recipient, bytes calldata data) external {
        emit MessageSent(msg.sender, recipient, data);
    }
    function receiveMessage(address sender, bytes calldata data) external {
        emit MessageReceived(sender, msg.sender, data);
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockRegistry.sol
---Filename: MockRegistry.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

contract MockRegistry {
    mapping(string => address) private addresses;
    mapping(string => address) private strategies;
    address public nftFloorOracle;

    function getAddress(string memory key) external view returns (address) {
        return addresses[key]; // Returns address(0) if unset
    }

    function setAddress(string memory key, address value) external {
        require(value != address(0), "Invalid address");
        addresses[key] = value;
    }

    function getStrategy(string memory name) external view returns (address) {
        return strategies[name];
    }

    function registerStrategy(string memory name, address strategyAddress) external {
        strategies[name] = strategyAddress;
    }

    function deregisterStrategy(string memory name) external {
        strategies[name] = address(0);
    }

    function setNftFloorOracle(address oracle) external {
        nftFloorOracle = oracle;
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockCurve3PoolToken.sol
---Filename: MockCurve3PoolToken.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
contract MockCurve3PoolToken is ERC20 {
    constructor(string memory name, string memory symbol) ERC20(name, symbol) {
        _mint(msg.sender, 1_000_000 * 10**18);
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockUniswapV2Router.sol
---Filename: MockUniswapV2Router.sol
---File Content Start---
// contracts/mocks/MockUniswapV2Router.sol
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
contract MockUniswapV2Router {
    function swapExactTokensForTokens(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external returns (uint256[] memory amounts) {
        require(block.timestamp <= deadline, "Expired");
        IERC20(path[0]).transferFrom(msg.sender, address(this), amountIn);
        IERC20(path[path.length - 1]).transfer(to, amountOutMin);
        amounts = new uint256[](path.length);
        amounts[0] = amountIn;
        amounts[path.length - 1] = amountOutMin;
    }
}
---File Content End---

---Path and files name: pmms/contracts/mocks/MockPoolAddressesProvider.sol
---Filename: MockPoolAddressesProvider.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

contract MockPoolAddressesProvider {
    address public pool;

    constructor(address _pool) {
        pool = _pool;
    }

    function getPool() external view returns (address) {
        return pool;
    }
}
---File Content End---

---Path and files name: pmms/contracts/mocks/MockUniswapV3Router.sol
---Filename: MockUniswapV3Router.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

contract MockUniswapV3Router {
    // Mock swap event
    event Swap(
        address indexed tokenIn,
        address indexed tokenOut,
        uint256 amountIn,
        uint256 amountOut
    );

    // Mock exactInputSingle function
    function exactInputSingle(
        address tokenIn,
        address tokenOut,
        uint24 fee,
        address recipient,
        uint256 deadline,
        uint256 amountIn,
        uint256 amountOutMinimum,
        uint160 sqrtPriceLimitX96
    ) external returns (uint256 amountOut) {
        require(block.timestamp <= deadline, "Transaction expired");
        require(amountIn > 0, "Invalid amount");
        // Simulate a 1:1 swap with 0.3% fee
        amountOut = (amountIn * 997) / 1000;
        emit Swap(tokenIn, tokenOut, amountIn, amountOut);
        return amountOut;
    }
}---File Content End---

---Path and files name: pmms/contracts/mocks/MockCurvePool.sol
---Filename: MockCurvePool.sol
---File Content Start---
// contracts/mocks/MockCurvePool.sol
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
contract MockCurvePool {
    address public token0;
    address public token1;
    address public token2;
    constructor(address _token0, address _token1, address _token2) {
        token0 = _token0;
        token1 = _token1;
        token2 = _token2;
    }
    function add_liquidity(uint256[3] calldata amounts, uint256 min_mint_amount) external {
        IERC20(token0).transferFrom(msg.sender, address(this), amounts[0]);
        IERC20(token1).transferFrom(msg.sender, address(this), amounts[1]);
        IERC20(token2).transferFrom(msg.sender, address(this), amounts[2]);
    }
    function exchange(int128 i, int128 j, uint256 dx, uint256 min_dy) external {
        address tokenIn = i == 0 ? token0 : i == 1 ? token1 : token2;
        address tokenOut = j == 0 ? token0 : j == 1 ? token1 : token2;
        IERC20(tokenIn).transferFrom(msg.sender, address(this), dx);
        IERC20(tokenOut).transfer(msg.sender, min_dy);
    }
}---File Content End---

---Path and files name: pmms/contracts/ProfitMaximizerModularSystem.sol
---Filename: ProfitMaximizerModularSystem.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "./core/Registry.sol";
import "./core/FlashloanExecutor.sol";
import "./interfaces/IStrategy.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";

contract ProfitMaximizerModularSystem is Initializable, OwnableUpgradeable, UUPSUpgradeable {
    Registry public registry;
    FlashloanExecutor public flashloanExecutor;

    event StrategyExecuted(string indexed name, address indexed strategy, bool success);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(address _registry, address _flashloanExecutor, address _owner) external initializer {
        require(_registry != address(0) && _flashloanExecutor != address(0), "Invalid addresses");

        __Ownable_init(_owner);
        __UUPSUpgradeable_init();

        registry = Registry(_registry);
        flashloanExecutor = FlashloanExecutor(_flashloanExecutor);
    }

    function executeStrategy(
        string calldata name,
        address asset,
        uint256 amount,
        bytes calldata params
    ) external onlyOwner {
        address strategyAddress = registry.getStrategy(name);
        require(strategyAddress != address(0), "Strategy not found");

        flashloanExecutor.initiateFlashLoan(asset, amount, params);

        emit StrategyExecuted(name, strategyAddress, true);
    }

    function addStrategy(string calldata name, address strategyAddress) external onlyOwner {
        registry.registerStrategy(name, strategyAddress);
    }

    function removeStrategy(string calldata name) external onlyOwner {
        registry.deregisterStrategy(name);
    }

    function updateFlashloanExecutor(address newExecutor) external onlyOwner {
        require(newExecutor != address(0), "Invalid executor address");
        flashloanExecutor = FlashloanExecutor(newExecutor);
    }

    function setRegistryAddress(string calldata key, address value) external onlyOwner {
        registry.setAddress(key, value);
    }

    function setNftFloorOracle(address oracle) external onlyOwner {
        registry.setNftFloorOracle(oracle);
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}
}
---File Content End---

---Path and files name: pmms/contracts/core/FlashloanExecutor.sol
---Filename: FlashloanExecutor.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IStrategy.sol";
import "../interfaces/IRegistry.sol";
import "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol";
import "@aave/core-v3/contracts/interfaces/IPool.sol";
import "./StrategyExecutor.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";

contract FlashloanExecutor is Initializable, UUPSUpgradeable, OwnableUpgradeable {
    StrategyExecutor public executor;
    IPoolAddressesProvider public addressProvider;
    IPool public pool;
    
    event FlashLoanInitiated(address indexed asset, uint256 amount);
    event StrategyExecuted(address indexed asset, uint256 amount, uint256 premium, bool success);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(
        IPoolAddressesProvider _addressProvider,
        address _initialOwner,
        address _strategyExecutor
    ) public initializer {
        __Ownable_init(_initialOwner);
        __UUPSUpgradeable_init();
        
        require(_strategyExecutor != address(0), "Invalid executor address");
        addressProvider = _addressProvider;
        pool = IPool(_addressProvider.getPool());
        executor = StrategyExecutor(_strategyExecutor);
    }

    function initiateFlashLoan(
        address asset, 
        uint256 amount,
        bytes calldata executionParams
    ) external onlyOwner {
        require(amount > 0, "Invalid amount");
        bytes memory data = abi.encode(executionParams);
        pool.flashLoanSimple(address(this), asset, amount, data, 0);
        emit FlashLoanInitiated(asset, amount);
    }


    function executeOperation(
        address asset,
        uint256 amount,
        uint256 premium,
        address, // initiator
        bytes calldata params
    ) external returns (bool) {
        require(msg.sender == address(pool), "Only Aave POOL");
        
        (bool success, ) = address(executor).call(
            abi.encodeWithSignature(
                "findAndExecute(address,uint256,uint256,bytes)",
                asset,
                amount,
                premium,
                params
            )
        );

        require(success, "Strategy execution failed");
        
        uint256 totalOwed = amount + premium;
        ERC20Upgradeable(asset).approve(address(pool), totalOwed);

        emit StrategyExecuted(asset, amount, premium, success);
        return true;
    }

    function updateExecutor(address newExecutor) external onlyOwner {
        require(newExecutor != address(0), "Invalid address");
        executor = StrategyExecutor(newExecutor);
    }

    function updatePoolAddressesProvider(IPoolAddressesProvider newProvider) external onlyOwner {
        require(address(newProvider) != address(0), "Invalid provider");
        addressProvider = newProvider;
        pool = IPool(newProvider.getPool());
    }

    // SKALE-compatible token receiver
    function receiveTokens(address token, uint256 amount) external {
        ERC20Upgradeable(token).transferFrom(msg.sender, address(this), amount);
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}
}---File Content End---

---Path and files name: pmms/contracts/core/DeFiStrategies.sol
---Filename: DeFiStrategies.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

contract DeFiStrategies {
        struct Strategy {
            uint256 id;
            string name;
            string description;
            string risk;
            string notes;
        }

        Strategy[] public strategies;

        constructor() {
            strategies.push(Strategy(1, "DEX Arbitrage", "Profit from token price discrepancies between decentralised exchanges", "Low", "Most basic, usually requires speed and liquidity"));
            strategies.push(Strategy(2, "Aave Liquidation", "Repay undercollateralised debt and receive a discounted asset", "Medium", "Requires accurate health factor monitoring; timing-sensitive"));
            strategies.push(Strategy(3, "Yield Loop (Leverage Farming)", "Deposit asset", " borrow against it", " redeposit etc., earning net yield,Medium,Profitable if APY > borrow rate; chain-specific opportunities"));
            strategies.push(Strategy(4, "Stablecoin Peg Arbitrage", "Arbitrage when stables (e.g. USDT/USDC/DAI) depeg on one DEX", "Low", "Very effective during market stress"));
            strategies.push(Strategy(5, "NFT Floor Arbitrage", "Buy NFT below floor price on one market", " sell higher on another", "Medium,Depends on floor oracle and timing"));
            strategies.push(Strategy(6, "Triangular Arbitrage", "e.g.", "ETH -> USDC -> DAI -> ETH", "Medium,Use Uniswap/Sushi/Balancer; works when paths are mispriced"));
            strategies.push(Strategy(7, "Cross-DEX Lending Arbitrage", "Borrow from one (low borrow rate)", " lend on another (high supply rate)", "High,Use Aave -> Compound / Morpho / Euler"));
            strategies.push(Strategy(8, "Governance Arbitrage", "Acquire tokens cheaply before snapshot", " vote or benefit", " dump after,High,Timing-critical; mostly DAO-reward based"));
            strategies.push(Strategy(9, "Oracle Lag Arbitrage", "When AMM price deviates before Chainlink updates", "Low", "Works especially on smaller tokens"));
            strategies.push(Strategy(10, "Stablecoin Meta-Protocol Arbitrage", "Curve pools (3Pool", " FraxBP)", " Meta Pools (Convex/Curve vaults),High,Requires deep Curve/Convex integration"));
            strategies.push(Strategy(11, "Flash-Mint Arbitrage", "Use protocols like FPI", " AMPL", " or tokens with built-in mint/redeem,High,Limited to tokens supporting flash mint"));
            strategies.push(Strategy(12, "LP Burn & Arbitrage", "Burn LP tokens to withdraw high value tokens (post-impermanent loss)", "Medium", "Time-sensitive post-shock"));
            strategies.push(Strategy(13, "MEV Capture (own mempool node)", "Detect pending tx and front-run for profit", "Very High", "Flashbots, RPC control"));
            strategies.push(Strategy(14, "Rebase Token Arbitrage", "e.g. AMPL or OHM forks before and after rebases", "Medium", "Oracle + Timing"));
            strategies.push(Strategy(15, "Bridging Latency Arbitrage", "Cross-chain token pricing (e.g.", " ETH price on Arbitrum vs ETH Mainnet)", "High,LayerZero, Wormhole"));
            strategies.push(Strategy(16, "NFT Collateral Liquidation", "Instant buyout of undercollateralised NFTs on platforms like BendDAO", "Medium", "NFT Pricing Oracle"));
            strategies.push(Strategy(17, "Liquid Staking Token Arbitrage", "Arbitrage between LSTs like stETH", " rETH", " cbETH,Medium,Market-dependent"));
            strategies.push(Strategy(18, "Flashloan Gas Arbitrage", "Arbitrage where users overpay for gas", " refund or resell via bundlers", "High,Requires bundler and custom RPC"));
        }
    }---File Content End---

---Path and files name: pmms/contracts/core/StrategyIds.sol
---Filename: StrategyIds.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

library StrategyIds {
    uint256 constant DEX_ARBITRAGE = 1;
    uint256 constant AAVE_LIQUIDATION = 2;
    uint256 constant YIELD_LOOP = 3;
    uint256 constant STABLECOIN_PEG_ARBITRAGE = 4;
    uint256 constant NFT_FLOOR_ARBITRAGE = 5;
    uint256 constant TRIANGULAR_ARBITRAGE = 6;
    uint256 constant CROSS_DEX_LENDING_ARBITRAGE = 7;
    uint256 constant GOVERNANCE_ARBITRAGE = 8;
    uint256 constant ORACLE_LAG_ARBITRAGE = 9;
    uint256 constant STABLECOIN_META_PROTOCOL_ARBITRAGE = 10;
    uint256 constant FLASH_MINT_ARBITRAGE = 11;
    uint256 constant LP_BURN_ARBITRAGE = 12;
    uint256 constant MEV_CAPTURE = 13;
    uint256 constant REBASE_TOKEN_ARBITRAGE = 14;
    uint256 constant BRIDGING_LATENCY_ARBITRAGE = 15;
    uint256 constant NFT_COLLATERAL_LIQUIDATION = 16;
    uint256 constant LIQUID_STAKING_TOKEN_ARBITRAGE = 17;
    uint256 constant FLASHLOAN_GAS_ARBITRAGE = 18;
    uint256 constant ZIGT_ARBITRAGE = 19;
}
---File Content End---

---Path and files name: pmms/contracts/core/StrategyExecutor.sol
---Filename: StrategyExecutor.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";

contract StrategyExecutor is Initializable, OwnableUpgradeable, UUPSUpgradeable {
    IRegistry public registry;
    address public flashloanExecutor;

    event StrategyExecuted(address indexed strategy, address indexed asset, uint256 profit);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(
        address _registry,
        address _flashloanExecutor,
        address _owner
    ) public initializer {
        __Ownable_init(_owner);
        __UUPSUpgradeable_init();
        
        require(_registry != address(0) && _flashloanExecutor != address(0), "Invalid addresses");
        registry = IRegistry(_registry);
        flashloanExecutor = _flashloanExecutor;
    }

    function findAndExecute(
        address asset,
        uint256 amount,
        uint256 premium,
        bytes calldata params
    ) external returns (bool success, bytes memory result) {
        require(msg.sender == flashloanExecutor, "Unauthorized");

        string[] memory strategyNames = registry.getStrategyNames();
        uint256 bestProfit = 0;
        address bestStrategy;
        bytes memory bestExecutionData;

        for (uint256 i = 0; i < strategyNames.length; i++) {
            address strategyAddr = registry.getStrategy(strategyNames[i]);
            if (strategyAddr == address(0)) continue;

            (uint256 profit, bytes memory executionData) = IStrategy(strategyAddr).checkOpportunity(asset, amount);
            
            if (profit > bestProfit && profit > premium) {
                bestProfit = profit;
                bestStrategy = strategyAddr;
                bestExecutionData = executionData;
            }
        }

        if (bestStrategy != address(0)) {
            (bool executed, bytes memory strategyResult, uint256 finalAmount) = 
                IStrategy(bestStrategy).execute(bestExecutionData, amount, premium);
            
            if (executed && finalAmount > amount + premium) {
                emit StrategyExecuted(bestStrategy, asset, finalAmount - amount - premium);
                return (true, strategyResult);
            }
        }

        return (false, "No profitable strategy found");
    }

    function updateFlashloanExecutor(address newExecutor) external onlyOwner {
        require(newExecutor != address(0), "Invalid address");
        flashloanExecutor = newExecutor;
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}
}---File Content End---

---Path and files name: pmms/contracts/core/Registry.sol
---Filename: Registry.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/INftFloorOracle.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";

contract Registry is Initializable, IRegistry, OwnableUpgradeable, UUPSUpgradeable {
    mapping(string => address) public strategies;
    string[] private strategyNames;
    mapping(string => address) private addresses;
    address public nftFloorOracle;

    event StrategyRegistered(string indexed name, address indexed strategy);
    event StrategyDeregistered(string indexed name);
    event AddressSet(string indexed key, address indexed value);
    event NftFloorOracleUpdated(address indexed oracle);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(address initialOwner) public initializer {
        __Ownable_init(initialOwner);
        __UUPSUpgradeable_init();
    }

    function registerStrategy(string calldata name, address strategy) external override onlyOwner {
        require(strategies[name] == address(0), "Strategy already registered");
        require(strategy != address(0), "Invalid strategy address");
        strategies[name] = strategy;
        strategyNames.push(name);
        emit StrategyRegistered(name, strategy);
    }

    function deregisterStrategy(string calldata name) external override onlyOwner {
        require(strategies[name] != address(0), "Strategy not found");
        delete strategies[name];
        emit StrategyDeregistered(name);
    }

    function getStrategy(string calldata name) external view override returns (address) {
        return strategies[name];
    }

    function getStrategies() external view override returns (address[] memory) {
        address[] memory strategyAddresses = new address[](strategyNames.length);
        for (uint i = 0; i < strategyNames.length; i++) {
            strategyAddresses[i] = strategies[strategyNames[i]];
        }
        return strategyAddresses;
    }

    function getStrategyNames() external view returns (string[] memory) {
        return strategyNames;
    }

    function setAddress(string memory key, address value) external onlyOwner {
        require(value != address(0), "Invalid address");
        addresses[key] = value;
        emit AddressSet(key, value);
    }

    function getAddress(string memory key) external view override returns (address) {
        return addresses[key];
    }

    function setNftFloorOracle(address oracle) external onlyOwner {
        require(oracle != address(0), "Invalid oracle address");
        nftFloorOracle = oracle;
        emit NftFloorOracleUpdated(oracle);
    }

    function getNftFloorPrice(address nftContract) external view override returns (uint256) {
        require(nftFloorOracle != address(0), "NFT floor oracle not set");
        return INftFloorOracle(nftFloorOracle).getFloorPrice(nftContract);
    }

    function addStrategy(string memory name, address strategy) external onlyOwner {
        require(strategy != address(0), "Invalid strategy address");
        strategies[name] = strategy;
        strategyNames.push(name);
        emit StrategyRegistered(name, strategy);
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}
}---File Content End---

---Path and files name: pmms/contracts/core/StrategyRegistry.sol
---Filename: StrategyRegistry.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

contract StrategyRegistry {
    mapping(string => address) public strategies;
    string[] private strategyNames;

    event StrategyRegistered(string indexed name, address indexed strategy);

    function registerStrategy(string calldata name, address strategy) external {
        require(strategies[name] == address(0), "Strategy already registered");
        strategies[name] = strategy;
        strategyNames.push(name);
        emit StrategyRegistered(name, strategy);
    }

    function getStrategy(string calldata name) external view returns (address) {
        return strategies[name];
    }

    function getStrategyNames() external view returns (string[] memory) {
        return strategyNames;
    }
}
---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyZiGTArbitrage.sol
---Filename: StrategyZiGTArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IStrategy.sol";
import "../interfaces/IRegistry.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";
import "@uniswap/v3-periphery/contracts/interfaces/IQuoter.sol";

contract StrategyZiGTArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    IRegistry public registry;
    uint256 public slippageTolerance; // 50 = 0.5%
    uint24 public poolFee; // 3000 = 0.3%
    uint160 public sqrtPriceLimitX96;
    
    // Supported ZiGT token symbols
    string[] private zigtSymbols = [
        "ZiG-R", "ZiG-N", "ZiG-RG", "ZiG-KB", 
        "ZiG-UB", "ZiG-SF", "ZiG-PC", "ZiG-MG",
        "ZiG-SH", "ZiG-CD", "ZiG-KU", "ZiG-KD",
        "ZiGT", "ZiG"
    ];
    
    // Supported stablecoins and WETH
    string[] private pairedTokens = ["USDC", "USDT", "DAI", "WETH"];
    
    event ZiGTArbitrageExecuted(
        address indexed zigtToken,
        address indexed pairedToken,
        uint256 amountIn,
        uint256 amountOut,
        uint256 profit,
        uint256 timestamp
    );

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(address _registry) external initializer {        
        require(_registry != address(0), "Invalid registry");
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        registry = IRegistry(_registry);
        slippageTolerance = 50; // 0.5%
        poolFee = 3000; // 0.3%
        sqrtPriceLimitX96 = 0;
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "ZiGTArbitrage";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        // Check if the asset is a ZiGT token
        if (!_isZiGToken(asset)) return (0, "");
        
        address uniswapRouter = registry.getAddress("UNISWAP_V3");
        address quoter = registry.getAddress("UNISWAP_V3_QUOTER");
        
        if (uniswapRouter == address(0) || quoter == address(0)) {
            return (0, "");
        }

        // Find best arbitrage opportunity across all paired tokens
        for (uint i = 0; i < pairedTokens.length; i++) {
            address pairedToken = registry.getAddress(pairedTokens[i]);
            if (pairedToken == address(0)) continue;
            
            // Get direct swap quote (ZiGT -> Paired Token)
            uint256 directOut = _getUniswapV3Quote(
                quoter,
                asset,
                pairedToken,
                amount
            );
            
            // Get reverse swap quote (Paired Token -> ZiGT)
            uint256 reverseOut = _getUniswapV3Quote(
                quoter,
                pairedToken,
                asset,
                directOut
            );
            
            // Calculate potential profit
            if (reverseOut > amount) {
                uint256 potentialProfit = reverseOut - amount;
                if (potentialProfit > profit) {
                    profit = potentialProfit;
                    executionData = abi.encode(pairedToken, directOut, reverseOut);
                }
            }
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256 profit)
    {
        (address pairedToken, uint256 expectedPairedOut, uint256 expectedZigtOut) = abi.decode(
            executionData,
            (address, uint256, uint256)
        );
        
        address zigtToken = msg.sender;
        address uniswapRouter = registry.getAddress("UNISWAP_V3");
        require(uniswapRouter != address(0), "Uniswap router not set");
        
        // Swap ZiGT -> Paired Token
        uint256 pairedOut = _executeSwap(
            zigtToken,
            pairedToken,
            amount,
            (expectedPairedOut * (10000 - slippageTolerance)) / 10000,
            uniswapRouter
        );
        
        // Swap Paired Token -> ZiGT
        uint256 finalZigtAmount = _executeSwap(
            pairedToken,
            zigtToken,
            pairedOut,
            (expectedZigtOut * (10000 - slippageTolerance)) / 10000,
            uniswapRouter
        );
        
        // Calculate profit after premium
        profit = finalZigtAmount > amount + premium ? finalZigtAmount - amount - premium : 0;
        require(profit > 0, "Insufficient profit");

        emit ZiGTArbitrageExecuted(
            zigtToken,
            pairedToken,
            amount,
            finalZigtAmount,
            profit,
            block.timestamp
        );

        return (true, abi.encode(finalZigtAmount, profit), profit);
    }

    // ========== Helper Functions ==========
    
    function _isZiGToken(address token) internal view returns (bool) {
        for (uint i = 0; i < zigtSymbols.length; i++) {
            address zigtAddr = registry.getAddress(zigtSymbols[i]);
            if (token == zigtAddr) {
                return true;
            }
        }
        return false;
    }
    
    function _getUniswapV3Quote(
        address quoter,
        address tokenIn,
        address tokenOut,
        uint256 amountIn
    ) internal view returns (uint256) {
        (bool success, bytes memory data) = quoter.staticcall(
            abi.encodeWithSelector(
                IQuoter.quoteExactInputSingle.selector,
                tokenIn,
                tokenOut,
                poolFee,
                amountIn,
                sqrtPriceLimitX96
            )
        );
        
        if (!success) return 0;
        
        try this.decodeQuoteResult(data) returns (uint256 amountOut) {
            return amountOut;
        } catch {
            return 0;
        }
    }
    
    function _executeSwap(
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        uint256 amountOutMinimum,
        address router
    ) internal returns (uint256) {
        IERC20(tokenIn).approve(router, amountIn);
        
        ISwapRouter.ExactInputSingleParams memory params = ISwapRouter.ExactInputSingleParams({
            tokenIn: tokenIn,
            tokenOut: tokenOut,
            fee: poolFee,
            recipient: address(this),
            deadline: block.timestamp + 300,
            amountIn: amountIn,
            amountOutMinimum: amountOutMinimum,
            sqrtPriceLimitX96: sqrtPriceLimitX96
        });
        
        uint256 amountOut = ISwapRouter(router).exactInputSingle(params);
        IERC20(tokenIn).approve(router, 0);
        
        return amountOut;
    }
    
    // Helper function for static call decoding
    function decodeQuoteResult(bytes memory data) external pure returns (uint256 amountOut) {
        (amountOut) = abi.decode(data, (uint256));
    }
    
    // Emergency withdraw function (owner only)
    function withdrawToken(address token, uint256 amount) external onlyOwner {
        IERC20(token).safeTransfer(owner(), amount);
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyYieldLoop.sol
---Filename: StrategyYieldLoop.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "../interfaces/IStrategy.sol";
import "../interfaces/IRegistry.sol";

interface IAaveLendingPool {
    function deposit(address asset, uint256 amount, address onBehalfOf, uint16 referralCode) external;
    function borrow(address asset, uint256 amount, uint256 interestRateMode, uint16 referralCode, address onBehalfOf) external;
    function repay(address asset, uint256 amount, uint256 rateMode, address onBehalfOf) external returns (uint256);
    function getUserAccountData(address user)
        external
        view
        returns (
            uint256 totalCollateralETH,
            uint256 totalDebtETH,
            uint256 availableBorrowsETH,
            uint256 currentLiquidationThreshold,
            uint256 ltv,
            uint256 healthFactor
        );
}

contract StrategyYieldLoop is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    IRegistry public registry;
    address public usdc;
    address public dai;
    uint256 public constant MAX_LTV = 8000; // 80% LTV (basis points)
    uint256 public constant MIN_HEALTH_FACTOR = 1.1 ether; // 1.1 in 18 decimals
    uint256 public constant LOOP_COUNT = 2; // Number of leverage loops

    event YieldLoopExecuted(
        address indexed depositAsset,
        address indexed borrowAsset,
        uint256 amountIn,
        uint256 profit,
        uint256 timestamp
    );

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(address _registry) external initializer {        
        require(_registry != address(0), "Invalid registry address");
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        registry = IRegistry(_registry);
        usdc = registry.getAddress("USDC");
        dai = registry.getAddress("DAI");
        require(usdc != address(0), "USDC address not set");
        require(dai != address(0), "DAI address not set");
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "YieldLoop";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        if (amount == 0 || (asset != usdc && asset != dai)) {
            return (0, "");
        }

        address lendingPoolAddress = registry.getAddress("AAVE_LENDING_POOL");
        if (lendingPoolAddress == address(0)) {
            return (0, "");
        }

        // Mock APY calculation (simplified for SKALE testing)
        // Assume supply APY = 5%, borrow APY = 3%, net yield = 2% per loop
        uint256 netYieldBps = 200; // 2% in basis points
        uint256 totalProfit = (amount * netYieldBps * LOOP_COUNT) / 10000;

        if (totalProfit > 0) {
            // Borrow the same asset for simplicity in mock environment
            executionData = abi.encode(asset, asset, totalProfit);
            return (totalProfit, executionData);
        }
        return (0, "");
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256 finalProfit)
    {
        (address depositAsset, address borrowAsset, uint256 expectedProfit) = abi.decode(
            executionData,
            (address, address, uint256)
        );
        require(depositAsset == usdc || depositAsset == dai, "Invalid deposit asset");
        require(borrowAsset == usdc || borrowAsset == dai, "Invalid borrow asset");

        address lendingPoolAddress = registry.getAddress("AAVE_LENDING_POOL");
        require(lendingPoolAddress != address(0), "Aave Lending Pool not set");
        IAaveLendingPool lendingPool = IAaveLendingPool(lendingPoolAddress);

        IERC20 depositToken = IERC20(depositAsset);
        require(depositToken.balanceOf(address(this)) >= amount, "Insufficient deposit balance");

        uint256 currentDeposit = amount;
        uint256 totalBorrowed = 0;

        // Perform leverage loops
        for (uint256 i = 0; i < LOOP_COUNT; i++) {
            // Deposit
            depositToken.approve(lendingPoolAddress, currentDeposit);
            try lendingPool.deposit(depositAsset, currentDeposit, address(this), 0) {
                // Success
            } catch {
                depositToken.approve(lendingPoolAddress, 0);
                revert("Deposit failed");
            }
            depositToken.approve(lendingPoolAddress, 0);

            // Check health factor and borrowing capacity
            (, ,uint256 availableBorrowsETH, , uint256 ltv, uint256 healthFactor) = lendingPool.getUserAccountData(
                address(this)
            );
            require(healthFactor >= MIN_HEALTH_FACTOR, "Health factor too low");
            require(ltv <= MAX_LTV, "LTV too high");

            // Calculate borrow amount (80% of deposited value for safety)
            uint256 borrowAmount = (currentDeposit * MAX_LTV) / 10000;
            if (borrowAmount == 0) break;

            // Borrow
            IERC20 borrowToken = IERC20(borrowAsset);
            try lendingPool.borrow(borrowAsset, borrowAmount, 2, 0, address(this)) {
                totalBorrowed += borrowAmount;
            } catch {
                revert("Borrow failed");
            }

            // Prepare for next loop
            currentDeposit = borrowAmount;
            require(borrowToken.balanceOf(address(this)) >= borrowAmount, "Insufficient borrow balance");
        }

        // Mock profit calculation (simplified for SKALE)
        finalProfit = totalBorrowed > premium ? totalBorrowed - premium : 0;
        require(finalProfit >= expectedProfit, "Profit below expected");

        // Ensure funds are available for flashloan repayment
        require(depositToken.balanceOf(address(this)) >= amount + premium, "Insufficient funds for repayment");

        emit YieldLoopExecuted(depositAsset, borrowAsset, amount, finalProfit, block.timestamp);

        return (true, abi.encode(amount + finalProfit, finalProfit), finalProfit);
    }

    function withdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).safeTransfer(msg.sender, amount);
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyStablecoinMetaProtocolArbitrage.sol
---Filename: StrategyStablecoinMetaProtocolArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "../interfaces/ICurvePool.sol";
import "../interfaces/IConvexBooster.sol";
import "../interfaces/ISwapRouter.sol";
import "../interfaces/IQuoter.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract StrategyStablecoinMetaProtocolArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    IRegistry public registry;
    uint256 public slippageTolerance; // 0.5% (50 basis points)
    uint24 public uniswapV3Fee; // 0.3% fee tier

    event ArbitrageExecuted(
        address indexed tokenIn,
        address indexed tokenOut,
        uint256 amountIn,
        uint256 amountOut,
        bool isCurve,
        uint256 profit,
        uint256 timestamp
    );

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(address _registry) external initializer {       
        require(_registry != address(0), "Invalid registry");
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        registry = IRegistry(_registry);
        slippageTolerance = 50;
        uniswapV3Fee = 3000;
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "StablecoinMetaProtocolArbitrage";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        address curvePool = registry.getAddress("CURVE_3POOL");
        address convexBooster = registry.getAddress("CONVEX");
        address usdc = registry.getAddress("USDC");
        address usdt = registry.getAddress("USDT");
        address uniswapRouter = registry.getAddress("UNISWAP_V3");
        address quoter = registry.getAddress("UNISWAP_V3_QUOTER");

        if (curvePool == address(0) || convexBooster == address(0) || usdc == address(0) || usdt == address(0) || uniswapRouter == address(0) || quoter == address(0) || asset != usdc || amount == 0) {
            return (0, "");
        }

        // Get Uniswap V3 quote (USDC -> USDT)
        uint256 expectedUsdtFromUniswap;
        (bool uniSuccess, bytes memory uniData) = address(quoter).staticcall(
            abi.encodeWithSelector(
                IQuoter.quoteExactInputSingle.selector,
                usdc,
                usdt,
                uniswapV3Fee,
                amount,
                0
            )
        );
        if (uniSuccess) {
            try this.decodeQuoteResult(uniData) returns (uint256 amountOut) {
                expectedUsdtFromUniswap = amountOut;
            } catch {
                expectedUsdtFromUniswap = 0;
            }
        }

        // Get Curve quote (USDC -> USDT)
        uint256 expectedUsdtFromCurve;
        int128 usdcIndex = _getCurveTokenIndex(curvePool, usdc);
        int128 usdtIndex = _getCurveTokenIndex(curvePool, usdt);
        if (usdcIndex >= 0 && usdtIndex >= 0) {
            (bool curveSuccess, bytes memory curveData) = address(curvePool).staticcall(
                abi.encodeWithSelector(
                    ICurvePool.get_dy.selector,
                    usdcIndex,
                    usdtIndex,
                    amount
                )
            );
            if (curveSuccess) {
                try this.decodeCurveResult(curveData) returns (uint256 amountOut) {
                    expectedUsdtFromCurve = amountOut;
                } catch {
                    expectedUsdtFromCurve = 0;
                }
            }
        }

        // Mock Convex yield check (simplified for SKALE)
        uint256 convexYieldEstimate = expectedUsdtFromCurve > 0 ? expectedUsdtFromCurve * 101 / 100 : 0; // Assume 1% yield

        // Compare paths: Uniswap vs. Curve + Convex
        if (convexYieldEstimate > expectedUsdtFromUniswap && convexYieldEstimate > amount) {
            profit = convexYieldEstimate - amount;
            executionData = abi.encode(curvePool, usdc, usdt, expectedUsdtFromCurve, true);
        } else if (expectedUsdtFromUniswap > amount) {
            profit = expectedUsdtFromUniswap - amount;
            executionData = abi.encode(uniswapRouter, usdc, usdt, expectedUsdtFromUniswap, false);
        } else {
            return (0, "");
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
       returns (bool success, bytes memory result, uint256 profit)
    {
        (address protocolAddress, address tokenIn, address tokenOut, uint256 minOutExpected, bool isCurve) = abi.decode(
            executionData,
            (address, address, address, uint256, bool)
        );
        require(protocolAddress != address(0), "Invalid protocol");
        require(tokenIn != address(0) && tokenOut != address(0), "Invalid tokens");
        require(minOutExpected > 0, "Invalid minimum output");
        require(amount > 0, "Invalid amount");

        IERC20 tokenInContract = IERC20(tokenIn);
        require(tokenInContract.balanceOf(address(this)) >= amount, "Insufficient balance");

        uint256 finalAmountReceived;
        if (isCurve) {
            address curvePool = protocolAddress;
            address usdc = registry.getAddress("USDC");
            address usdt = registry.getAddress("USDT");
            address curve3PoolToken = registry.getAddress("CURVE_3POOL_TOKEN");
            address convex = registry.getAddress("CONVEX");

            require(usdc == tokenIn && usdt == tokenOut, "Invalid Curve tokens");
            require(curve3PoolToken != address(0) && convex != address(0), "Invalid Curve/Convex config");

            int128 usdcIndex = _getCurveTokenIndex(curvePool, usdc);
            int128 usdtIndex = _getCurveTokenIndex(curvePool, usdt);
            require(usdcIndex >= 0 && usdtIndex >= 0, "Invalid Curve indices");

            // Swap USDC to USDT on Curve
            uint256 minDy = (minOutExpected * (10000 - slippageTolerance)) / 10000;
            tokenInContract.approve(curvePool, amount);
            uint256 usdtReceived;
            try ICurvePool(curvePool).exchange(usdcIndex, usdtIndex, amount, minDy) returns (uint256 out) {
                usdtReceived = out;
            } catch {
                tokenInContract.approve(curvePool, 0);
                revert("Curve swap failed");
            }
            tokenInContract.approve(curvePool, 0);

            // Add USDT to Curve 3pool
            IERC20 usdtContract = IERC20(usdt);
            usdtContract.approve(curvePool, usdtReceived);
            uint256[3] memory amounts = [0, 0, usdtReceived];
            uint256 lpReceived;
            try ICurvePool(curvePool).add_liquidity(amounts, 0) returns (uint256 out) {
                lpReceived = out;
            } catch {
                usdtContract.approve(curvePool, 0);
                revert("Curve liquidity failed");
            }
            usdtContract.approve(curvePool, 0);

            // Deposit LP tokens to Convex
            IERC20 lpToken = IERC20(curve3PoolToken);
            lpToken.approve(convex, lpReceived);
            try IConvexBooster(convex).deposit(1, lpReceived, true) {
                finalAmountReceived = usdtReceived; // Use swap output for profit
            } catch {
                lpToken.approve(convex, 0);
                revert("Convex deposit failed");
            }
            lpToken.approve(convex, 0);
        } else {
            // Uniswap V3 swap
            address uniswapRouter = protocolAddress;
            ISwapRouter.ExactInputSingleParams memory params = ISwapRouter.ExactInputSingleParams({
                tokenIn: tokenIn,
                tokenOut: tokenOut,
                fee: uniswapV3Fee,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: amount,
                amountOutMinimum: (minOutExpected * (10000 - slippageTolerance)) / 10000,
                sqrtPriceLimitX96: 0
            });

            tokenInContract.approve(uniswapRouter, amount);
            try ISwapRouter(uniswapRouter).exactInputSingle(params) returns (uint256 out) {
                finalAmountReceived = out;
            } catch {
                tokenInContract.approve(uniswapRouter, 0);
                revert("Uniswap V3 swap failed");
            }
            tokenInContract.approve(uniswapRouter, 0);
        }

        profit = finalAmountReceived > amount + premium ? finalAmountReceived - amount - premium : 0;
        require(profit > 0, "Insufficient profit");

        emit ArbitrageExecuted(tokenIn, tokenOut, amount, finalAmountReceived, isCurve, profit, block.timestamp);

        return (true, abi.encode(finalAmountReceived, profit), profit);
    }

    // Helper functions for static call decoding
    function decodeQuoteResult(bytes memory data) external pure returns (uint256 amountOut) {
        (amountOut) = abi.decode(data, (uint256));
    }

    function decodeCurveResult(bytes memory data) external pure returns (uint256 amountOut) {
        (amountOut) = abi.decode(data, (uint256));
    }

    // Get Curve token index dynamically
    function _getCurveTokenIndex(address curvePool, address token) internal view returns (int128) {
        try ICurvePool(curvePool).coins(0) returns (address coin0) {
            if (coin0 == token) return 0;
        } catch {}
        try ICurvePool(curvePool).coins(1) returns (address coin1) {
            if (coin1 == token) return 1;
        } catch {}
        try ICurvePool(curvePool).coins(2) returns (address coin2) {
            if (coin2 == token) return 2;
        } catch {}
        return -1;
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyTriangularArbitrage.sol
---Filename: StrategyTriangularArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "../interfaces/ISwapRouter.sol";
import "../interfaces/IQuoter.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract StrategyTriangularArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    IRegistry public registry;
    address public usdc;
    address public dai;
    uint256 public slippageTolerance; // Basis points (50 = 0.5%)
    uint24 public poolFee; // e.g., 3000 = 0.3%
    uint160 public sqrtPriceLimitX96;

    event TriangularArbitrage(
        address indexed asset,
        uint256 amountIn,
        uint256 amountOut,
        uint256 profit,
        uint256 timestamp
    );

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(address _registry) external initializer {        
        require(_registry != address(0), "Invalid registry address");
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        registry = IRegistry(_registry);
        usdc = registry.getAddress("USDC");
        dai = registry.getAddress("DAI");
        require(usdc != address(0), "USDC address not set");
        require(dai != address(0), "DAI address not set");
        slippageTolerance = 50; // 0.5%
        poolFee = 3000; // 0.3%
        sqrtPriceLimitX96 = 0;
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "TriangularArbitrage";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        if (amount == 0 || (asset != usdc && asset != dai)) {
            return (0, "");
        }

        address uniswapRouter = registry.getAddress("UNISWAP_V3");
        address quoter = registry.getAddress("UNISWAP_V3_QUOTER");
        if (uniswapRouter == address(0) || quoter == address(0)) {
            return (0, "");
        }

        // Triangular path: asset -> USDC -> DAI -> asset
        address[] memory path = new address[](3);
        path[0] = asset;
        path[1] = asset == usdc ? dai : usdc;
        path[2] = asset;

        uint256 amountOut = amount;

        // Swap 1: asset -> intermediate token (USDC or DAI)
        (bool success1, bytes memory data1) = address(quoter).staticcall(
            abi.encodeWithSelector(
                IQuoter.quoteExactInputSingle.selector,
                path[0],
                path[1],
                poolFee,
                amountOut,
                sqrtPriceLimitX96
            )
        );
        if (success1) {
            try this.decodeQuote(data1) returns (uint256 quoteOut) {
                amountOut = quoteOut;
            } catch {
                return (0, "");
            }
        } else {
            return (0, "");
        }

        // Swap 2: intermediate token -> final token (DAI or USDC)
        (bool success2, bytes memory data2) = address(quoter).staticcall(
            abi.encodeWithSelector(
                IQuoter.quoteExactInputSingle.selector,
                path[1],
                path[2],
                poolFee,
                amountOut,
                sqrtPriceLimitX96
            )
        );
        if (success2) {
            try this.decodeQuote(data2) returns (uint256 quoteOut) {
                amountOut = quoteOut;
            } catch {
                return (0, "");
            }
        } else {
            return (0, "");
        }

        // Swap 3: final token -> asset
        (bool success3, bytes memory data3) = address(quoter).staticcall(
            abi.encodeWithSelector(
                IQuoter.quoteExactInputSingle.selector,
                path[2],
                path[0],
                poolFee,
                amountOut,
                sqrtPriceLimitX96
            )
        );
        if (success3) {
            try this.decodeQuote(data3) returns (uint256 quoteOut) {
                amountOut = quoteOut;
            } catch {
                return (0, "");
            }
        } else {
            return (0, "");
        }

        if (amountOut > amount) {
            profit = amountOut - amount;
            executionData = abi.encode(path, amountOut);
        } else {
            return (0, "");
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256 finalProfit)
    {
        (address[] memory path, uint256 expectedAmountOut) = abi.decode(executionData, (address[], uint256));
        require(path.length == 3, "Invalid path length");
        require(path[0] == path[2], "Path must start and end with same token");
        require(path[0] == usdc || path[0] == dai, "Invalid asset");
        require(path[1] == usdc || path[1] == dai, "Invalid intermediate token");

        address uniswapRouter = registry.getAddress("UNISWAP_V3");
        require(uniswapRouter != address(0), "Uniswap router not set");

        IERC20 tokenIn = IERC20(path[0]);
        require(tokenIn.balanceOf(address(this)) >= amount, "Insufficient balance");

        // Execute swap 1: path[0] -> path[1]
        uint256 amountOut1;
        uint256 minAmountOut1 = (amount * (10000 - slippageTolerance)) / 10000;
        tokenIn.approve(uniswapRouter, amount);
        try ISwapRouter(uniswapRouter).exactInputSingle(
            ISwapRouter.ExactInputSingleParams({
                tokenIn: path[0],
                tokenOut: path[1],
                fee: poolFee,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: amount,
                amountOutMinimum: minAmountOut1,
                sqrtPriceLimitX96: sqrtPriceLimitX96
            })
        ) returns (uint256 amountOut) {
            amountOut1 = amountOut;
        } catch {
            tokenIn.approve(uniswapRouter, 0);
            revert("Swap 1 failed");
        }
        tokenIn.approve(uniswapRouter, 0);

        // Execute swap 2: path[1] -> path[2]
        uint256 amountOut2;
        uint256 minAmountOut2 = (amountOut1 * (10000 - slippageTolerance)) / 10000;
        IERC20 tokenMid = IERC20(path[1]);
        require(tokenMid.balanceOf(address(this)) >= amountOut1, "Insufficient balance for swap 2");
        tokenMid.approve(uniswapRouter, amountOut1);
        try ISwapRouter(uniswapRouter).exactInputSingle(
            ISwapRouter.ExactInputSingleParams({
                tokenIn: path[1],
                tokenOut: path[2],
                fee: poolFee,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: amountOut1,
                amountOutMinimum: minAmountOut2,
                sqrtPriceLimitX96: sqrtPriceLimitX96
            })
        ) returns (uint256 amountOut) {
            amountOut2 = amountOut;
        } catch {
            tokenMid.approve(uniswapRouter, 0);
            revert("Swap 2 failed");
        }
        tokenMid.approve(uniswapRouter, 0);

        // Execute swap 3: path[2] -> path[0]
        uint256 finalAmount;
        uint256 minAmountOut3 = (expectedAmountOut * (10000 - slippageTolerance)) / 10000;
        IERC20 tokenFinal = IERC20(path[2]);
        require(tokenFinal.balanceOf(address(this)) >= amountOut2, "Insufficient balance for swap 3");
        tokenFinal.approve(uniswapRouter, amountOut2);
        try ISwapRouter(uniswapRouter).exactInputSingle(
            ISwapRouter.ExactInputSingleParams({
                tokenIn: path[2],
                tokenOut: path[0],
                fee: poolFee,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: amountOut2,
                amountOutMinimum: minAmountOut3,
                sqrtPriceLimitX96: sqrtPriceLimitX96
            })
        ) returns (uint256 amountOut) {
            finalAmount = amountOut;
        } catch {
            tokenFinal.approve(uniswapRouter, 0);
            revert("Swap 3 failed");
        }
        tokenFinal.approve(uniswapRouter, 0);

        finalProfit = finalAmount > amount + premium ? finalAmount - amount - premium : 0;
        require(finalProfit > 0, "Insufficient profit");

        emit TriangularArbitrage(path[0], amount, finalAmount, finalProfit, block.timestamp);

        return (true, abi.encode(finalAmount, finalProfit), finalProfit);
    }

    function withdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).safeTransfer(msg.sender, amount);
    }

    // Helper function for static call decoding
    function decodeQuote(bytes memory data) external pure returns (uint256 amountOut) {
        (amountOut) = abi.decode(data, (uint256));
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyStakingTokenArbitrage.sol
---Filename: StrategyStakingTokenArbitrage.sol
---File Content Start---
// SPDX-License-License: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";
import "@uniswap/v3-periphery/contracts/interfaces/IQuoter.sol";

contract StrategyStakingTokenArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    IRegistry public registry;
    address public steth;
    address public weth;
    uint256 public slippageTolerance; // Basis points (50 = 0.5%)
    uint24 public poolFee; // e.g., 3000 = 0.3%
    uint160 public constant SQRT_PRICE_LIMIT_X96 = 0;

    event StakingTokenArbitrage(
        address indexed staking,
        uint256 amountIn,
        uint256 amountOut,
        uint256 profit,
        uint256 timestamp
    );

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(address _registry) external initializer {       
        require(_registry != address(0), "Invalid registry address");
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        registry = IRegistry(_registry);
        steth = registry.getAddress("STETH");
        weth = registry.getAddress("WETH");
        require(steth != address(0), "STETH address not set");
        require(weth != address(0), "WETH address not set");
        slippageTolerance = 50; // 0.5%
        poolFee = 3000; // 0.3%
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "StakingTokenArbitrage";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        if (asset != steth || amount == 0) {
            return (0, "");
        }

        address uniswapRouter = registry.getAddress("UNISWAP_V3");
        address quoter = registry.getAddress("UNISWAP_V3_QUOTER");
        if (uniswapRouter == address(0) || weth == address(0) || quoter == address(0)) {
            return (0, "");
        }

        // Get stETH -> WETH quote
        uint256 stethToWeth;
        (bool quoteSuccess, bytes memory quoteData) = address(quoter).staticcall(
            abi.encodeWithSelector(
                IQuoter.quoteExactInputSingle.selector,
                steth,
                weth,
                poolFee,
                amount,
                SQRT_PRICE_LIMIT_X96
            )
        );
        if (quoteSuccess) {
            try this.decodeQuote(quoteData) returns (uint256 amountOut) {
                stethToWeth = amountOut;
            } catch {
                stethToWeth = 0;
            }
        }

        // Get WETH -> stETH quote
        uint256 wethToSteth;
        if (stethToWeth > 0) {
            (bool wethQuoteSuccess, bytes memory wethQuoteData) = address(quoter).staticcall(
                abi.encodeWithSelector(
                    IQuoter.quoteExactInputSingle.selector,
                    weth,
                    steth,
                    poolFee,
                    stethToWeth,
                    SQRT_PRICE_LIMIT_X96
                )
            );
            if (wethQuoteSuccess) {
                try this.decodeQuote(wethQuoteData) returns (uint256 amountOut) {
                    wethToSteth = amountOut;
                } catch {
                    wethToSteth = 0;
                }
            }
        }

        if (wethToSteth > amount) {
            profit = wethToSteth - amount;
            executionData = abi.encode(weth, stethToWeth, wethToSteth);
        } else {
            return (0, "");
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256 finalProfit)
    {
        (address wethToken, uint256 expectedWethOut, uint256 expectedStethOut) = abi.decode(
            executionData,
            (address, uint256, uint256)
        );
        require(wethToken == weth, "Invalid WETH address");
        address uniswapRouter = registry.getAddress("UNISWAP_V3");
        require(uniswapRouter != address(0), "Invalid Uniswap router");
        IERC20 stethContract = IERC20(steth);
        require(stethContract.balanceOf(address(this)) >= amount, "Insufficient stETH balance");

        // Swap stETH -> WETH
        uint256 minWethOut = (expectedWethOut * (10000 - slippageTolerance)) / 10000;
        stethContract.approve(uniswapRouter, amount);
        uint256 wethOut;
        try ISwapRouter(uniswapRouter).exactInputSingle(
            ISwapRouter.ExactInputSingleParams({
                tokenIn: steth,
                tokenOut: weth,
                fee: poolFee,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: amount,
                amountOutMinimum: minWethOut,
                sqrtPriceLimitX96: SQRT_PRICE_LIMIT_X96
            })
        ) returns (uint256 amountOut) {
            wethOut = amountOut;
        } catch {
            stethContract.approve(uniswapRouter, 0);
            revert("stETH to WETH swap failed");
        }
        stethContract.approve(uniswapRouter, 0);

        // Swap WETH -> stETH
        IERC20 wethContract = IERC20(weth);
        require(wethContract.balanceOf(address(this)) >= wethOut, "Insufficient WETH balance");
        uint256 minStethOut = (expectedStethOut * (10000 - slippageTolerance)) / 10000;
        wethContract.approve(uniswapRouter, wethOut);
        uint256 finalStethAmount;
        try ISwapRouter(uniswapRouter).exactInputSingle(
            ISwapRouter.ExactInputSingleParams({
                tokenIn: weth,
                tokenOut: steth,
                fee: poolFee,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: wethOut,
                amountOutMinimum: minStethOut,
                sqrtPriceLimitX96: SQRT_PRICE_LIMIT_X96
            })
        ) returns (uint256 amountOut) {
            finalStethAmount = amountOut;
        } catch {
            wethContract.approve(uniswapRouter, 0);
            revert("WETH to stETH swap failed");
        }
        wethContract.approve(uniswapRouter, 0);

        // Calculate profit
        finalProfit = finalStethAmount > amount + premium ? finalStethAmount - amount - premium : 0;
        require(finalProfit > 0, "Insufficient profit");

        emit StakingTokenArbitrage(steth, amount, finalStethAmount, finalProfit, block.timestamp);

        return (true, abi.encode(finalStethAmount, finalProfit), finalProfit);
    }

    function withdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).safeTransfer(msg.sender, amount);
    }

    // Helper function for static call decoding
    function decodeQuote(bytes memory data) external pure returns (uint256 amountOut) {
        (amountOut) = abi.decode(data, (uint256));
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyRebaseTokenArbitrage.sol
---Filename: StrategyRebaseTokenArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IStrategy.sol";
import "../interfaces/IRegistry.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";
import "@uniswap/v3-periphery/contracts/interfaces/IQuoter.sol";

interface IRebaseToken {
    function totalSupply() external view returns (uint256);
    function targetPrice() external view returns (uint256);
    function rebase() external returns (uint256);
    function balanceOf(address account) external view returns (uint256);
    function symbol() external view returns (string memory);
}

contract StrategyRebaseTokenArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    IRegistry public registry;
    ISwapRouter public uniswapRouter;
    IQuoter public quoter;
    
    uint256 public slippageTolerance; // 0.5% (50 basis points)
    uint256 public minProfitMargin; // 1% (100 basis points)
    uint24 public poolFee; // 0.3% (3000)
    uint256 public deadlineExtension; // 300 seconds
    uint256 public pricePrecision; // 1e18

    event RebaseArbitrageExecuted(
        address indexed token,
        uint256 profit,
        uint256 amountIn,
        uint256 amountOut,
        uint256 timestamp
    );

    struct ExecutionParams {
        address rebaseToken;
        address pairedAsset;
        bool isSelling;
    }

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(
        address _registry,
        address _uniswapRouter,
        address _quoter
    ) external initializer {
        require(_registry != address(0), "Invalid registry");
        require(_uniswapRouter != address(0), "Invalid router");
        require(_quoter != address(0), "Invalid quoter");        
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        registry = IRegistry(_registry);
        uniswapRouter = ISwapRouter(_uniswapRouter);
        quoter = IQuoter(_quoter);
        slippageTolerance = 50;
        minProfitMargin = 100;
        poolFee = 3000;
        deadlineExtension = 300;
        pricePrecision = 1e18;
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "RebaseTokenArbitrage";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        address rebaseTokenAddr = registry.getAddress("REBASE_TOKEN");
        address weth = registry.getAddress("WETH");
        
        if (rebaseTokenAddr == address(0) || weth == address(0) || asset != rebaseTokenAddr || amount == 0) {
            return (0, "");
        }

        IRebaseToken rebaseToken = IRebaseToken(rebaseTokenAddr);
        uint256 targetPrice;
        try rebaseToken.targetPrice() returns (uint256 price) {
            targetPrice = price;
        } catch {
            return (0, "");
        }
        
        (bool success, uint256 currentMarketPrice) = _getMarketPrice(rebaseTokenAddr, weth, amount);
        if (!success || currentMarketPrice == 0) return (0, "");

        // Normalize targetPrice (assuming 18 decimals for targetPrice)
        targetPrice = targetPrice / 1e18; // Convert to WETH units
        if (currentMarketPrice > targetPrice + (targetPrice / 1000)) {
            profit = (currentMarketPrice - targetPrice) * amount / pricePrecision;
            executionData = abi.encode(ExecutionParams(rebaseTokenAddr, weth, true));
        } else if (currentMarketPrice < targetPrice - (targetPrice / 1000)) {
            profit = (targetPrice - currentMarketPrice) * amount / pricePrecision;
            executionData = abi.encode(ExecutionParams(rebaseTokenAddr, weth, false));
        } else {
            return (0, "");
        }

        if (profit < (amount * minProfitMargin / 10000)) {
            return (0, "");
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256 finalProfit)
    {
        ExecutionParams memory params = abi.decode(executionData, (ExecutionParams));
        require(params.rebaseToken != address(0), "Invalid token");
        require(params.pairedAsset != address(0), "Invalid paired asset");
        require(amount > 0, "Invalid amount");

        IERC20 tokenIn = IERC20(params.isSelling ? params.rebaseToken : params.pairedAsset);
        require(tokenIn.balanceOf(address(this)) >= amount, "Insufficient balance");

        ISwapRouter.ExactInputSingleParams memory swapParams = ISwapRouter.ExactInputSingleParams({
            tokenIn: params.isSelling ? params.rebaseToken : params.pairedAsset,
            tokenOut: params.isSelling ? params.pairedAsset : params.rebaseToken,
            fee: poolFee,
            recipient: address(this),
            deadline: block.timestamp + deadlineExtension,
            amountIn: amount,
            amountOutMinimum: _calculateMinAmountOut(amount),
            sqrtPriceLimitX96: 0
        });

        tokenIn.approve(address(uniswapRouter), amount);
        uint256 amountOut;
        try uniswapRouter.exactInputSingle(swapParams) returns (uint256 out) {
            amountOut = out;
        } catch {
            tokenIn.approve(address(uniswapRouter), 0);
            revert("Swap failed");
        }
        tokenIn.approve(address(uniswapRouter), 0);

        finalProfit = _calculateProfit(amount, amountOut, premium);
        require(finalProfit >= (amount * minProfitMargin / 10000), "Insufficient profit");

        emit RebaseArbitrageExecuted(
            params.rebaseToken,
            finalProfit,
            amount,
            amountOut,
            block.timestamp
        );

        success = true;
        result = abi.encode(finalProfit);
        return (success, result, finalProfit);
    }

    function _getMarketPrice(address tokenIn, address tokenOut, uint256 amount)
        internal
        view
        returns (bool success, uint256 price)
    {
        // Use static call to ensure view compatibility
        (bool callSuccess, bytes memory data) = address(quoter).staticcall(
            abi.encodeWithSelector(
                IQuoter.quoteExactInputSingle.selector,
                tokenIn,
                tokenOut,
                poolFee,
                amount,
                0
            )
        );
        
        if (!callSuccess) {
            return (false, 0);
        }

        try this.decodeQuoteResult(data) returns (uint256 amountOut) {
            return (true, (amountOut * pricePrecision) / amount);
        } catch {
            return (false, 0);
        }
    }

    // External function to decode static call result
    function decodeQuoteResult(bytes memory data) external pure returns (uint256 amountOut) {
        (amountOut) = abi.decode(data, (uint256));
    }

    function _calculateMinAmountOut(uint256 amountIn) internal view returns (uint256) {
        return (amountIn * (10000 - slippageTolerance)) / 10000;
    }

    function _calculateProfit(uint256 amountIn, uint256 amountOut, uint256 premium) 
        internal
        pure
        returns (uint256) 
    {
        return amountOut > amountIn + premium ? amountOut - amountIn - premium : 0;
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyTemplate.sol
---Filename: StrategyTemplate.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";
import "@uniswap/v3-periphery/contracts/interfaces/IQuoter.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract StrategyTemplate is IStrategy, ReentrancyGuard {
    using SafeERC20 for IERC20;

    IRegistry public immutable registry;
    uint256 public constant SLIPPAGE_TOLERANCE = 50;
    uint24 public constant POOL_FEE = 3000;
    uint160 public constant SQRT_PRICE_LIMIT_X96 = 0;

    constructor(address _registry) {
        require(_registry != address(0), "Invalid registry");
        registry = IRegistry(_registry);
    }

    function name() external pure override returns (string memory) {
        return "Strategy Template";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        override
        returns (uint256 profit, bytes memory executionData)
    {
        address quoter = registry.getAddress("UNISWAP_V3_QUOTER");
        // Remove try-catch since not view anymore
        uint256 quotedAmount = IQuoter(quoter).quoteExactInputSingle(
            asset,
            registry.getAddress("WETH"), // Example token
            POOL_FEE,
            amount,
            SQRT_PRICE_LIMIT_X96
        );
        
        if (quotedAmount > amount) {
            profit = quotedAmount - amount;
            executionData = abi.encode(asset);
        }
    }

    function execute(bytes memory data, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool, bytes memory, uint256)
    {
        address router = registry.getAddress("UNISWAP_V3");
        address tokenIn = abi.decode(data, (address));
        
        // Use approve instead of safeApprove
        IERC20(tokenIn).approve(router, amount);
        
        uint256 amountOut = ISwapRouter(router).exactInputSingle(
            ISwapRouter.ExactInputSingleParams({
                tokenIn: tokenIn,
                tokenOut: registry.getAddress("WETH"),
                fee: POOL_FEE,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: amount,
                amountOutMinimum: amount * (10000 - SLIPPAGE_TOLERANCE) / 10000,
                sqrtPriceLimitX96: SQRT_PRICE_LIMIT_X96
            })
        );
        
        // Reset approval
        IERC20(tokenIn).approve(router, 0);
        
        uint256 profit = amountOut > amount + premium ? amountOut - amount - premium : 0;
        return (true, abi.encode(amountOut), profit);
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyOracleLagArbitrage.sol
---Filename: StrategyOracleLagArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "@chainlink/contracts/src/v0.8/shared/interfaces/AggregatorV3Interface.sol";
import "@uniswap/v2-periphery/contracts/interfaces/IUniswapV2Router02.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";

contract StrategyOracleLagArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    address public registry;
    uint256 public slippageTolerance; // 0.5% (50 basis points)

    event OracleLagArbitrageExecuted(address indexed asset, uint256 profit, uint256 timestamp);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers(); // Prevent initialization during deployment
    }

    function initialize(address _registry) external initializer {
        require(_registry != address(0), "Invalid registry");     
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        registry = _registry;
        slippageTolerance = 50; // 0.5%
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "OracleLagArbitrage";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        if (asset == address(0) || amount == 0) return (0, "");

        address priceFeed = IRegistry(registry).getAddress("ETH_USD_FEED");
        if (priceFeed == address(0)) return (0, "");

        int256 oraclePrice;
        try AggregatorV3Interface(priceFeed).latestRoundData() returns (uint80, int256 price, uint256, uint256, uint80) {
            oraclePrice = price;
        } catch {
            return (0, "");
        }
        if (oraclePrice <= 0) return (0, "");

        address uniswapRouter = IRegistry(registry).getAddress("UNISWAP_V2");
        address usdc = IRegistry(registry).getAddress("USDC");
        if (uniswapRouter == address(0) || usdc == address(0)) return (0, "");

        address[] memory path = new address[](2);
        path[0] = asset; // e.g., WETH
        path[1] = usdc; // USDC
        uint256[] memory amounts;
        try IUniswapV2Router02(uniswapRouter).getAmountsOut(amount, path) returns (uint256[] memory out) {
            amounts = out;
        } catch {
            return (0, "");
        }
        if (amounts.length < 2) return (0, "");
        uint256 dexPrice = amounts[1]; // USDC amount

        // Normalize prices (assuming 8 decimals for Chainlink ETH/USD feed)
        uint256 oraclePriceNormalized = (uint256(oraclePrice) * amount) / 1e8; // USDC amount
        if (oraclePriceNormalized > dexPrice) {
            profit = oraclePriceNormalized - dexPrice; // Profit in USDC
            executionData = abi.encode(asset, dexPrice);
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256 profit)
    {
        (address asset, uint256 dexPrice) = abi.decode(executionData, (address, uint256));
        require(asset != address(0), "Invalid asset");
        require(amount > 0, "Invalid amount");

        address uniswapRouter = IRegistry(registry).getAddress("UNISWAP_V2");
        address usdc = IRegistry(registry).getAddress("USDC");
        require(uniswapRouter != address(0) && usdc != address(0), "Invalid router or USDC");

        require(IERC20(asset).balanceOf(address(this)) >= amount, "Insufficient asset balance");

        // Approve Uniswap router
        IERC20(asset).approve(uniswapRouter, amount);

        // Swap asset for USDC
        address[] memory path = new address[](2);
        path[0] = asset;
        path[1] = usdc;
        uint256 minOut = (dexPrice * (10000 - slippageTolerance)) / 10000;
        uint256[] memory amounts;
        try IUniswapV2Router02(uniswapRouter).swapExactTokensForTokens(
            amount,
            minOut,
            path,
            address(this),
            block.timestamp + 300
        ) returns (uint256[] memory out) {
            amounts = out;
        } catch {
            IERC20(asset).approve(uniswapRouter, 0);
            revert("Swap failed");
        }

        // Reset allowance
        IERC20(asset).approve(uniswapRouter, 0);

        uint256 finalAmount = amounts[1];
        require(finalAmount > premium, "Insufficient profit");
        profit = finalAmount - premium;

        emit OracleLagArbitrageExecuted(asset, profit, block.timestamp);

        success = true;
        result = abi.encode(asset, profit);
        return (success, result, profit);
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyMEVCapture.sol
---Filename: StrategyMEVCapture.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

interface IUniswapV2Router {
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts);
    function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts);
}

interface IUniswapV2Pair {
    function getReserves() external view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast);
    function token0() external view returns (address);
}

contract StrategyMEVCapture is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    address public registry;
    uint256 public slippageTolerance; // 0.5% (50 basis points)
    uint256 public minProfit; // 1% (100 basis points)

    event MEVArbitrageExecuted(address indexed token, uint256 profit, uint256 timestamp);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers(); // Prevent initialization during deployment
    }

    function initialize(address _registry) external initializer {
        require(_registry != address(0), "Invalid registry");
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();            
        __UUPSUpgradeable_init();
        registry = _registry;
        slippageTolerance = 50; // 0.5%
        minProfit = 100; // 1%
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "MEVCapture";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        if (asset == address(0)) return (0, "");

        address weth = IRegistry(registry).getAddress("WETH");
        address router = IRegistry(registry).getAddress("UNISWAP_V2");
        address pair = IRegistry(registry).getAddress("UNISWAP_V2_PAIR");
        if (weth == address(0) || router == address(0) || pair == address(0)) return (0, "");

        (uint256 reserveAsset, uint256 reserveWeth) = _getReserves(pair, asset);
        if (reserveAsset == 0 || reserveWeth == 0) return (0, "");

        uint256 wethOut;
        try this._getAmountOut(amount, reserveAsset, reserveWeth) returns (uint256 out) {
            wethOut = out;
        } catch {
            return (0, "");
        }

        uint256 assetOut;
        try this._getAmountOut(wethOut, reserveWeth, reserveAsset) returns (uint256 out) {
            assetOut = out;
        } catch {
            return (0, "");
        }

        profit = assetOut > amount ? assetOut - amount : 0;
        if (profit >= _minProfit(amount)) {
            executionData = abi.encode(asset, weth, router);
        }
    }

    function execute(bytes calldata executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256 profit)
    {
        (address asset, address weth, address router) = abi.decode(executionData, (address, address, address));
        _validate(asset, weth, router, amount);

        uint256 wethReceived = _swap(router, asset, weth, amount);
        uint256 assetReceived = _swap(router, weth, asset, wethReceived);
        profit = _calculateProfit(assetReceived, amount, premium);

        emit MEVArbitrageExecuted(asset, profit, block.timestamp);
        success = true;
        result = abi.encode(profit);
        return (success, result, profit);
    }

    function _getReserves(address pair, address asset) internal view returns (uint256, uint256) {
        try IUniswapV2Pair(pair).getReserves() returns (uint112 reserve0, uint112 reserve1, uint32) {
            return IUniswapV2Pair(pair).token0() == asset ? (reserve0, reserve1) : (reserve1, reserve0);
        } catch {
            return (0, 0);
        }
    }

    function _getAmountOut(uint256 amountIn, uint256 reserveIn, uint256 reserveOut) external pure returns (uint256) {
        if (reserveIn == 0 || reserveOut == 0) return 0;
        uint256 amountInWithFee = amountIn * 997;
        uint256 numerator = amountInWithFee * reserveOut;
        uint256 denominator = reserveIn * 1000 + amountInWithFee;
        return numerator / denominator;
    }

    function _validate(address asset, address weth, address router, uint256 amount) internal view {
        require(asset != address(0) && weth != address(0) && router != address(0), "Invalid addresses");
        require(IERC20(asset).balanceOf(address(this)) >= amount, "Insufficient balance");
    }

    function _swap(address router, address tokenIn, address tokenOut, uint256 amount) internal returns (uint256) {
        address[] memory path = new address[](2);
        path[0] = tokenIn;
        path[1] = tokenOut;

        IERC20(tokenIn).approve(router, amount);
        uint256[] memory amounts;
        try IUniswapV2Router(router).swapExactTokensForTokens(
            amount,
            _minAmountOut(amount),
            path,
            address(this),
            block.timestamp + 300
        ) returns (uint256[] memory out) {
            amounts = out;
        } catch {
            revert("Swap failed");
        }
        IERC20(tokenIn).approve(router, 0);
        return amounts[1];
    }

    function _minProfit(uint256 amount) internal view returns (uint256) {
        return amount * minProfit / 10000;
    }

    function _minAmountOut(uint256 amount) internal view returns (uint256) {
        return amount * (10000 - slippageTolerance) / 10000;
    }

    function _calculateProfit(uint256 received, uint256 amount, uint256 premium) internal pure returns (uint256) {
        require(received > amount + premium, "Insufficient profit");
        return received - amount - premium;
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyStablecoinPegArbitrage.sol
---Filename: StrategyStablecoinPegArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "../interfaces/ICurvePool.sol";
import "../interfaces/IUniswapV2Router02.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";

contract StrategyStablecoinPegArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    IRegistry public registry;
    address public usdc;
    address public usdt;
    address public curve3Pool;
    address public uniswapV2;
    uint256 public slippageTolerance; // Basis points (100 = 1%)

    event PegArbitrage(
        address indexed stableIn,
        address indexed stableOut,
        uint256 amountIn,
        uint256 amountOut,
        uint256 profit,
        uint256 timestamp
    );

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(address _registry) external initializer {        
        require(_registry != address(0), "Invalid registry");
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        registry = IRegistry(_registry);
        usdc = IRegistry(_registry).getAddress("USDC");
        usdt = IRegistry(_registry).getAddress("USDT");
        curve3Pool = IRegistry(_registry).getAddress("CURVE_3POOL");
        uniswapV2 = IRegistry(_registry).getAddress("UNISWAP_V2");
        require(usdc != address(0), "USDC address not set");
        require(usdt != address(0), "USDT address not set");
        require(curve3Pool != address(0), "Curve 3Pool address not set");
        require(uniswapV2 != address(0), "Uniswap V2 address not set");
        slippageTolerance = 100; // 1%
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "StablecoinPegArbitrage";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        if (asset != usdc && asset != usdt || amount == 0) {
            return (0, "");
        }

        address stableIn = asset;
        address stableOut = asset == usdc ? usdt : usdc;

        // Get Curve price
        uint256 curveOut;
        int128 curveInIndex = _getCurveTokenIndex(curve3Pool, stableIn);
        int128 curveOutIndex = _getCurveTokenIndex(curve3Pool, stableOut);
        if (curveInIndex >= 0 && curveOutIndex >= 0) {
            (bool curveSuccess, bytes memory curveData) = address(curve3Pool).staticcall(
                abi.encodeWithSelector(
                    ICurvePool.get_dy.selector,
                    curveInIndex,
                    curveOutIndex,
                    amount
                )
            );
            if (curveSuccess) {
                try this.decodeCurveResult(curveData) returns (uint256 amountOut) {
                    curveOut = amountOut;
                } catch {
                    curveOut = 0;
                }
            }
        }

        // Get Uniswap V2 price
        uint256 uniOut;
        address[] memory path = new address[](2);
        path[0] = stableIn;
        path[1] = stableOut;
        (bool uniSuccess, bytes memory uniData) = address(uniswapV2).staticcall(
            abi.encodeWithSelector(
                IUniswapV2Router02.getAmountsOut.selector,
                amount,
                path
            )
        );
        if (uniSuccess) {
            try this.decodeUniResult(uniData) returns (uint256[] memory amounts) {
                uniOut = amounts[1];
            } catch {
                uniOut = 0;
            }
        }

        // Compare prices
        if (curveOut > (uniOut * (10000 + slippageTolerance)) / 10000 && curveOut > amount) {
            profit = curveOut - uniOut;
            executionData = abi.encode(stableIn, stableOut, curveOut, true); // Curve -> Uni
        } else if (uniOut > (curveOut * (10000 + slippageTolerance)) / 10000 && uniOut > amount) {
            profit = uniOut - curveOut;
            executionData = abi.encode(stableIn, stableOut, uniOut, false); // Uni -> Curve
        } else {
            return (0, "");
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256 profit)
    {
        (address stableIn, address stableOut, uint256 expectedOut, bool useCurveFirst) = abi.decode(
            executionData,
            (address, address, uint256, bool)
        );
        require(stableIn == usdc || stableIn == usdt, "Invalid stableIn");
        require(stableOut == usdc || stableOut == usdt, "Invalid stableOut");
        require(stableIn != stableOut, "Same tokens");
        require(expectedOut > 0, "Invalid expected output");
        require(amount > 0, "Invalid amount");

        IERC20 stableInContract = IERC20(stableIn);
        require(stableInContract.balanceOf(address(this)) >= amount, "Insufficient balance");

        uint256 finalAmountReceived;
        if (useCurveFirst) {
            // Curve: stableIn -> stableOut
            int128 i = _getCurveTokenIndex(curve3Pool, stableIn);
            int128 j = _getCurveTokenIndex(curve3Pool, stableOut);
            require(i >= 0 && j >= 0, "Invalid Curve indices");

            uint256 minDy = (expectedOut * (10000 - slippageTolerance)) / 10000;
            stableInContract.approve(curve3Pool, amount);
            uint256 received;
            try ICurvePool(curve3Pool).exchange(i, j, amount, minDy) returns (uint256 out) {
                received = out;
            } catch {
                stableInContract.approve(curve3Pool, 0);
                revert("Curve swap failed");
            }
            stableInContract.approve(curve3Pool, 0);

            // Uniswap V2: stableOut -> stableIn
            address[] memory path = new address[](2);
            path[0] = stableOut;
            path[1] = stableIn;
            IERC20 stableOutContract = IERC20(stableOut);
            stableOutContract.approve(uniswapV2, received);
            uint256[] memory amounts;
            try IUniswapV2Router02(uniswapV2).swapExactTokensForTokens(
                received,
                (amount * (10000 - slippageTolerance)) / 10000,
                path,
                address(this),
                block.timestamp + 300
            ) returns (uint256[] memory out) {
                amounts = out;
            } catch {
                stableOutContract.approve(uniswapV2, 0);
                revert("Uniswap V2 swap failed");
            }
            stableOutContract.approve(uniswapV2, 0);
            finalAmountReceived = amounts[1];
        } else {
            // Uniswap V2: stableIn -> stableOut
            address[] memory path = new address[](2);
            path[0] = stableIn;
            path[1] = stableOut;
            stableInContract.approve(uniswapV2, amount);
            uint256 received;
            try IUniswapV2Router02(uniswapV2).swapExactTokensForTokens(
                amount,
                (expectedOut * (10000 - slippageTolerance)) / 10000,
                path,
                address(this),
                block.timestamp + 300
            ) returns (uint256[] memory out) {
                received = out[1];
            } catch {
                stableInContract.approve(uniswapV2, 0);
                revert("Uniswap V2 swap failed");
            }
            stableInContract.approve(uniswapV2, 0);

            // Curve: stableOut -> stableIn
            int128 i = _getCurveTokenIndex(curve3Pool, stableOut);
            int128 j = _getCurveTokenIndex(curve3Pool, stableIn);
            require(i >= 0 && j >= 0, "Invalid Curve indices");

            IERC20 stableOutContract = IERC20(stableOut);
            stableOutContract.approve(curve3Pool, received);
            try ICurvePool(curve3Pool).exchange(i, j, received, (amount * (10000 - slippageTolerance)) / 10000) returns (uint256 out) {
                finalAmountReceived = out;
            } catch {
                stableOutContract.approve(curve3Pool, 0);
                revert("Curve swap failed");
            }
            stableOutContract.approve(curve3Pool, 0);
        }

        profit = finalAmountReceived > amount + premium ? finalAmountReceived - amount - premium : 0;
        require(profit > 0, "Insufficient profit");

        emit PegArbitrage(stableIn, stableOut, amount, finalAmountReceived, profit, block.timestamp);

        return (true, abi.encode(finalAmountReceived, profit), profit);
    }

    function getCurvePrice(address assetIn, address assetOut, uint256 amount) internal view returns (uint256) {
        int128 i = _getCurveTokenIndex(curve3Pool, assetIn);
        int128 j = _getCurveTokenIndex(curve3Pool, assetOut);
        if (i < 0 || j < 0) return 0;
        (bool success, bytes memory data) = address(curve3Pool).staticcall(
            abi.encodeWithSelector(ICurvePool.get_dy.selector, i, j, amount)
        );
        if (!success) return 0;
        try this.decodeCurveResult(data) returns (uint256 amountOut) {
            return amountOut;
        } catch {
            return 0;
        }
    }

    function _getUniPrice(address assetIn, address assetOut, uint256 amount) internal view returns (uint256) {
        address[] memory path = new address[](2);
        path[0] = assetIn;
        path[1] = assetOut;
        (bool success, bytes memory data) = address(uniswapV2).staticcall(
            abi.encodeWithSelector(IUniswapV2Router02.getAmountsOut.selector, amount, path)
        );
        if (!success) return 0;
        try this.decodeUniResult(data) returns (uint256[] memory amounts) {
            return amounts[1];
        } catch {
            return 0;
        }
    }

    // Helper functions for static call decoding
    function decodeCurveResult(bytes memory data) external pure returns (uint256 amountOut) {
        (amountOut) = abi.decode(data, (uint256));
    }

    function decodeUniResult(bytes memory data) external pure returns (uint256[] memory amounts) {
        (amounts) = abi.decode(data, (uint256[]));
    }

    // Get Curve token index dynamically
    function _getCurveTokenIndex(address curvePool, address token) internal view returns (int128) {
        try ICurvePool(curvePool).coins(0) returns (address coin0) {
            if (coin0 == token) return 0;
        } catch {}
        try ICurvePool(curvePool).coins(1) returns (address coin1) {
            if (coin1 == token) return 1;
        } catch {}
        try ICurvePool(curvePool).coins(2) returns (address coin2) {
            if (coin2 == token) return 2;
        } catch {}
        return -1;
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyNFTFloorArbitrage.sol
---Filename: StrategyNFTFloorArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IStrategy.sol";
import "../interfaces/IRegistry.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol";

interface ISkaleNftMarket {
    function getFloorPrice(address nftCollection) external view returns (uint256);
    function buy(address nftCollection, uint256 tokenId, uint256 price) external returns (bool);
    function sell(address nftCollection, uint256 tokenId, uint256 price) external returns (bool);
}

contract StrategyNFTFloorArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable, IERC721Receiver {
    using SafeERC20 for IERC20;

    address public registry;
    uint256 public minProfitMargin; // 5% (500 basis points)

    event ArbitrageExecuted(
        address indexed nftCollection,
        uint256 indexed tokenId,
        uint256 buyPrice,
        uint256 sellPrice,
        uint256 profit,
        address indexed marketplace
    );

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers(); // Prevent initialization during deployment
    }

    function initialize(address _registry) external initializer {
        require(_registry != address(0), "Invalid registry");        
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        registry = _registry;
        minProfitMargin = 500; // 5%
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "NFTFloorArbitrage";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        address marketplace = IRegistry(registry).getAddress("NFT_MARKETPLACE");
        address externalMarket = IRegistry(registry).getAddress("EXTERNAL_NFT_MARKET");
        if (marketplace == address(0) || externalMarket == address(0)) return (0, "");

        uint256 chainlinkPrice;
        try IRegistry(registry).getNftFloorPrice(asset) returns (uint256 price) {
            chainlinkPrice = price;
        } catch {
            return (0, "");
        }
        if (chainlinkPrice == 0) return (0, "");

        uint256 marketplacePrice;
        try ISkaleNftMarket(marketplace).getFloorPrice(asset) returns (uint256 price) {
            marketplacePrice = price;
        } catch {
            return (0, "");
        }

        uint256 externalPrice;
        try ISkaleNftMarket(externalMarket).getFloorPrice(asset) returns (uint256 price) {
            externalPrice = price;
        } catch {
            return (0, "");
        }
        if (marketplacePrice == 0 || externalPrice == 0) return (0, "");

        uint256 buyPrice;
        uint256 sellPrice;
        bool buyFromMarketplace;
        uint256 tokenId = 1; // Off-chain monitoring provides tokenId

        if (externalPrice < marketplacePrice) {
            buyPrice = externalPrice;
            sellPrice = marketplacePrice;
            buyFromMarketplace = false;
        } else if (marketplacePrice < externalPrice) {
            buyPrice = marketplacePrice;
            sellPrice = externalPrice;
            buyFromMarketplace = true;
        } else {
            return (0, "");
        }

        if (buyPrice > amount || tokenId == 0) return (0, "");

        uint256 profitMargin = ((sellPrice - buyPrice) * 10000) / buyPrice;
        if (profitMargin < minProfitMargin) return (0, "");

        profit = sellPrice - buyPrice;
        executionData = abi.encode(asset, marketplace, externalMarket, buyFromMarketplace, tokenId);
        return (profit, executionData);
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256 profit)
    {
        (address nftCollection, address marketplace, address externalMarket, bool buyFromMarketplace, uint256 tokenId) = abi.decode(
            executionData,
            (address, address, address, bool, uint256)
        );
        require(nftCollection != address(0) && tokenId != 0, "Invalid NFT collection or tokenId");

        uint256 chainlinkPrice;
        try IRegistry(registry).getNftFloorPrice(nftCollection) returns (uint256 price) {
            chainlinkPrice = price;
        } catch {
            revert("Invalid Chainlink price");
        }
        require(chainlinkPrice > 0, "Invalid Chainlink price");

        uint256 marketplacePrice;
        try ISkaleNftMarket(marketplace).getFloorPrice(nftCollection) returns (uint256 price) {
            marketplacePrice = price;
        } catch {
            revert("Invalid marketplace price");
        }

        uint256 externalPrice;
        try ISkaleNftMarket(externalMarket).getFloorPrice(nftCollection) returns (uint256 price) {
            externalPrice = price;
        } catch {
            revert("Invalid external market price");
        }
        require(marketplacePrice > 0 && externalPrice > 0, "Invalid market prices");

        uint256 buyPrice;
        uint256 sellPrice;
        if (externalPrice < marketplacePrice && !buyFromMarketplace) {
            buyPrice = externalPrice;
            sellPrice = marketplacePrice;
        } else if (marketplacePrice < externalPrice && buyFromMarketplace) {
            buyPrice = marketplacePrice;
            sellPrice = externalPrice;
        } else {
            revert("No arbitrage opportunity");
        }

        require(((sellPrice - buyPrice) * 10000) / buyPrice >= minProfitMargin, "Profit margin too low");
        require(buyPrice <= amount, "Buy price exceeds amount");

        address weth = IRegistry(registry).getAddress("WETH");
        require(weth != address(0), "WETH not set");
        IERC20 token = IERC20(weth);
        require(token.balanceOf(address(this)) >= buyPrice, "Insufficient WETH");

        bool buySuccess;
        if (buyFromMarketplace) {
            token.approve(marketplace, buyPrice);
            try ISkaleNftMarket(marketplace).buy(nftCollection, tokenId, buyPrice) returns (bool success) {
                buySuccess = success;
            } catch {
                revert("Buy failed");
            }
            token.approve(marketplace, 0);
        } else {
            token.approve(externalMarket, buyPrice);
            try ISkaleNftMarket(externalMarket).buy(nftCollection, tokenId, buyPrice) returns (bool success) {
                buySuccess = success;
            } catch {
                revert("Buy failed");
            }
            token.approve(externalMarket, 0);
        }
        require(buySuccess, "Buy failed");
        require(IERC721(nftCollection).ownerOf(tokenId) == address(this), "NFT not received");

        bool sellSuccess;
        IERC721(nftCollection).approve(buyFromMarketplace ? externalMarket : marketplace, tokenId);
        if (buyFromMarketplace) {
            try ISkaleNftMarket(externalMarket).sell(nftCollection, tokenId, sellPrice) returns (bool success) {
                sellSuccess = success;
            } catch {
                revert("Sell failed");
            }
        } else {
            try ISkaleNftMarket(marketplace).sell(nftCollection, tokenId, sellPrice) returns (bool success) {
                sellSuccess = success;
            } catch {
                revert("Sell failed");
            }
        }
        IERC721(nftCollection).approve(address(0), tokenId);
        require(sellSuccess, "Sell failed");

        profit = sellPrice - buyPrice;
        require(profit > premium, "Profit does not cover premium");

        emit ArbitrageExecuted(
            nftCollection,
            tokenId,
            buyPrice,
            sellPrice,
            profit,
            buyFromMarketplace ? marketplace : externalMarket
        );

        success = true;
        result = abi.encode(nftCollection, tokenId, profit);
        return (success, result, profit);
    }

    function onERC721Received(address, address, uint256, bytes calldata) external pure override returns (bytes4) {
        return this.onERC721Received.selector;
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyAaveLiquidation.sol
---Filename: StrategyAaveLiquidation.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IStrategy.sol";
import "../interfaces/IRegistry.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";

interface IAaveLendingPool {
    function liquidationCall(address collateral, address debt, address user, uint256 debtToCover, bool receiveAToken) external;
}

contract StrategyAaveLiquidation is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    address public registry;

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers(); // Prevent initialization during deployment
    }

    function initialize(address _registry) external initializer {
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        registry = _registry;
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "AaveLiquidation";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        address aave = IRegistry(registry).getAddress("AAVE_LENDING_POOL");
        address collateral = IRegistry(registry).getAddress("WETH");
        uint256 collateralValue = 1 ether; // Placeholder
        if (collateralValue > amount) {
            profit = collateralValue - amount;
            executionData = abi.encode(collateral, asset, amount);
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256)
    {
        (address collateral, address debtAsset, uint256 debt) = abi.decode(executionData, (address, address, uint256));
        address aave = IRegistry(registry).getAddress("AAVE_LENDING_POOL");
        IERC20 debtToken = IERC20(debtAsset);
        require(debtToken.balanceOf(address(this)) >= debt, "Insufficient balance");

        debtToken.forceApprove(aave, debt);
        try IAaveLendingPool(aave).liquidationCall(collateral, debtAsset, address(this), debt, false) {
            // Liquidation successful
        } catch {
            revert("Liquidation failed");
        }
        debtToken.forceApprove(aave, 0);

        uint256 collateralValue = 1 ether; // Placeholder
        uint256 profit = collateralValue - debt;
        require(profit > premium, "Profit too low");

        success = true;
        result = abi.encode(collateral, profit);
        return (success, result, profit - premium);
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyBase.sol
---Filename: StrategyBase.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IStrategy.sol";
import "../interfaces/IRegistry.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

abstract contract BaseStrategy is IStrategy, Ownable, ReentrancyGuard {
    using SafeERC20 for IERC20;

    IRegistry public immutable registry;
    address public immutable executor;

    modifier onlyExecutor() {
        require(msg.sender == executor, "Only executor");
        _;
    }

    constructor(address _registry, address _executor) {
        require(_registry != address(0) && _executor != address(0), "Invalid addresses");
        registry = IRegistry(_registry);
        executor = _executor;
    }

    function name() external pure virtual override returns (string memory) {
        return "BaseStrategy";
    }

    function checkOpportunity(
        address asset,
        uint256 amount
    ) external view virtual override returns (uint256 profit, bytes memory executionData) {
        profit = 0;
        executionData = "";
    }

    function execute(
        bytes memory executionData,
        uint256 amount,
        uint256 premium
    ) external virtual override onlyExecutor nonReentrant returns (bool, bytes memory, uint256) {
        executionData; amount; premium; // Silence unused parameter warnings
        return (false, "", 0);
    }

    function _approveToken(address token, address spender, uint256 amount) internal {
        IERC20(token).forceApprove(spender, amount); // Using forceApprove instead
    }

    function emergencyWithdraw(address token) external onlyOwner {
        IERC20(token).safeTransfer(owner(), IERC20(token).balanceOf(address(this)));
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyNFTCollateralLiquidation.sol
---Filename: StrategyNFTCollateralLiquidation.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";

interface ISkaleNftLending {
    function liquidate(address nftContract, uint256 tokenId, uint256 amount) external;
}

contract StrategyNFTCollateralLiquidation is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable, IERC721Receiver {
    using SafeERC20 for IERC20;

    address public registry;

    event NFTLiquidationExecuted(address indexed nftContract, uint256 indexed tokenId, uint256 profit, uint256 timestamp);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers(); // Prevent initialization during deployment
    }

    function initialize(address _registry) external initializer {
        require(_registry != address(0), "Invalid registry");       
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        registry = _registry;
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "NFTCollateralLiquidation";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        address usdc = IRegistry(registry).getAddress("USDC");
        if (asset != usdc || amount == 0) return (0, "");

        address skaleLending = IRegistry(registry).getAddress("SKALE_NFT_LENDING");
        address nftContract = IRegistry(registry).getAddress("NFT_CONTRACT");
        if (skaleLending == address(0) || nftContract == address(0)) return (0, "");

        uint256 tokenId = 1; // Off-chain monitoring for liquidatable tokenId
        uint256 debt = amount; // Debt in USDC
        uint256 nftValue;
        try IRegistry(registry).getNftFloorPrice(nftContract) returns (uint256 value) {
            nftValue = value;
        } catch {
            return (0, "");
        }

        if (nftValue > debt && tokenId != 0) {
            profit = nftValue - debt;
            executionData = abi.encode(nftContract, tokenId, debt);
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256 profit)
    {
        (address nftContract, uint256 tokenId, uint256 debt) = abi.decode(executionData, (address, uint256, uint256));
        require(nftContract != address(0) && debt == amount && tokenId != 0, "Invalid NFT contract, debt, or tokenId");
        address skaleLending = IRegistry(registry).getAddress("SKALE_NFT_LENDING");
        address usdc = IRegistry(registry).getAddress("USDC");
        require(skaleLending != address(0) && usdc != address(0), "Invalid lending or USDC address");

        require(IERC20(usdc).balanceOf(address(this)) >= debt, "Insufficient USDC balance");

        IERC20(usdc).approve(skaleLending, debt);
        try ISkaleNftLending(skaleLending).liquidate(nftContract, tokenId, debt) {
            // Liquidation successful
        } catch {
            revert("Liquidation failed");
        }
        IERC20(usdc).approve(skaleLending, 0);

        require(IERC721(nftContract).ownerOf(tokenId) == address(this), "NFT not received");

        uint256 nftValue;
        try IRegistry(registry).getNftFloorPrice(nftContract) returns (uint256 value) {
            nftValue = value;
        } catch {
            revert("Failed to fetch NFT floor price");
        }
        require(nftValue > debt + premium, "Insufficient profit");
        profit = nftValue - debt - premium;

        emit NFTLiquidationExecuted(nftContract, tokenId, profit, block.timestamp);

        success = true;
        result = abi.encode(nftContract, tokenId, debt, profit);
        return (success, result, profit);
    }

    function onERC721Received(address, address, uint256, bytes calldata) external pure override returns (bytes4) {
        return this.onERC721Received.selector;
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyFlashloanGasArbitrage.sol
---Filename: StrategyFlashloanGasArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";
import "@chainlink/contracts/src/v0.8/shared/interfaces/AggregatorV3Interface.sol";

// Mock interface for SKALE (replace Optimism gas oracle)
interface IGasPriceOracle {
    function getFee() external view returns (uint256);
}

// Mock WETH interface for SKALE (no ETH deposits)
interface IWETH {
    function mint(address to, uint256 amount) external; // Mock minting
    function balanceOf(address account) external view returns (uint256);
}

contract StrategyFlashloanGasArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    address public registry;
    uint256 public slippageTolerance;
    address public gasOracle; // Mock or SKALE-specific oracle

    event GasArbitrageExecuted(address indexed asset, uint256 profit, uint256 timestamp);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers(); // Prevent initialization during deployment
    }

    function initialize(address _registry) external initializer {
        require(_registry != address(0), "Invalid registry");       
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();         
        __UUPSUpgradeable_init();
        registry = _registry;
        slippageTolerance = 50; // 0.5%
        gasOracle = IRegistry(_registry).getAddress("GAS_ORACLE"); // Fetch from Registry
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "FlashloanGasArbitrage";
    }

    struct SwapParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        override
        returns (uint256 profit, bytes memory executionData)
    {
        address usdc = IRegistry(registry).getAddress("USDC");
        if (asset != usdc) return (0, "");

        (address weth, address uniswapRouter) = _getAddresses();
        uint256 fee = _estimateFee();
        uint256 refund = fee / 2; // Mock refund amount

        uint256 wethOut = _simulateSwap(usdc, weth, amount, uniswapRouter);
        uint256 refundInWeth = _convertRefund(refund);

        if (wethOut + refundInWeth > amount) {
            profit = wethOut + refundInWeth - amount;
            executionData = abi.encode(weth, refund, uniswapRouter);
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256 profit)
    {
        (address weth, uint256 refund, address uniswapRouter) = abi.decode(executionData, (address, uint256, address));
        address usdc = IRegistry(registry).getAddress("USDC");

        _validateAddresses(weth, uniswapRouter, usdc);
        require(IERC20(usdc).balanceOf(address(this)) >= amount, "Insufficient USDC");

        if (refund > 0) {
            // Mock WETH minting instead of ETH deposit
            IWETH(weth).mint(address(this), refund);
        }

        uint256 wethOut = _executeSwap(usdc, weth, amount, uniswapRouter);
        uint256 refundInWeth = _convertRefund(refund);
        profit = _calculateProfit(amount, premium, wethOut, refundInWeth);

        emit GasArbitrageExecuted(usdc, profit, block.timestamp);
        success = true;
        result = abi.encode(wethOut, refundInWeth, profit);
        return (success, result, profit);
    }

    function _getAddresses() internal view returns (address weth, address uniswapRouter) {
        return (
            IRegistry(registry).getAddress("WETH"),
            IRegistry(registry).getAddress("UNISWAP_V3")
        );
    }

    function _estimateFee() internal view returns (uint256) {
        // Mock fee estimation for SKALE
        try IGasPriceOracle(gasOracle).getFee() returns (uint256 fee) {
            return fee;
        } catch {
            return 0; // Default to zero if no oracle
        }
    }

    function _simulateSwap(address tokenIn, address tokenOut, uint256 amount, address router) internal returns (uint256) {
        try ISwapRouter(router).exactInputSingle(
            ISwapRouter.ExactInputSingleParams({
                tokenIn: tokenIn,
                tokenOut: tokenOut,
                fee: 500,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: amount,
                amountOutMinimum: 0,
                sqrtPriceLimitX96: 0
            })
        ) returns (uint256 amountOut) {
            return amountOut;
        } catch {
            return 0;
        }
    }

    function _convertRefund(uint256 refund) internal view returns (uint256) {
        address priceFeed = IRegistry(registry).getAddress("ETH_USD_FEED");
        (, int256 ethPrice, , , ) = AggregatorV3Interface(priceFeed).latestRoundData();
        return ethPrice > 0 ? (refund * uint256(ethPrice)) / 1e8 : 0;
    }

    function _validateAddresses(address weth, address router, address usdc) internal view {
        require(weth == IRegistry(registry).getAddress("WETH"), "Invalid WETH");
        require(router == IRegistry(registry).getAddress("UNISWAP_V3"), "Invalid router");
        require(usdc != address(0), "Invalid USDC");
    }

    function _executeSwap(address tokenIn, address tokenOut, uint256 amount, address router) internal returns (uint256) {
        IERC20(tokenIn).approve(router, amount);
        uint256 amountOut = ISwapRouter(router).exactInputSingle(
            ISwapRouter.ExactInputSingleParams({
                tokenIn: tokenIn,
                tokenOut: tokenOut,
                fee: 500,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: amount,
                amountOutMinimum: (amount * (10000 - slippageTolerance)) / 10000,
                sqrtPriceLimitX96: 0
            })
        );
        IERC20(tokenIn).approve(router, 0);
        return amountOut;
    }

    function _calculateProfit(uint256 amount, uint256 premium, uint256 wethOut, uint256 refund) internal pure returns (uint256) {
        uint256 totalValue = wethOut + refund;
        require(totalValue > amount + premium, "Insufficient profit");
        return totalValue - amount - premium;
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyGovernanceArbitrage.sol
---Filename: StrategyGovernanceArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@uniswap/v2-periphery/contracts/interfaces/IUniswapV2Router02.sol";

contract StrategyGovernanceArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    address public registry;
    uint256 public minProfitMargin; // 10% (1000 basis points)

    event ArbitrageExecuted(address indexed asset, address indexed governanceToken, uint256 profit, uint256 timestamp);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers(); // Prevent initialization during deployment
    }

    function initialize(address _registry) external initializer {
        require(_registry != address(0), "Invalid registry");    
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();        
        __UUPSUpgradeable_init();
        registry = _registry;
        minProfitMargin = 1000; // 10%
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "GovernanceArbitrage";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        address governanceToken = IRegistry(registry).getAddress("GOV_TOKEN");
        address uniswapRouter = IRegistry(registry).getAddress("UNISWAP_V2");
        address usdc = IRegistry(registry).getAddress("USDC");
        if (asset != usdc) return (0, "");

        address[] memory path = new address[](2);
        path[0] = asset;
        path[1] = governanceToken;
        uint256[] memory amounts;
        try IUniswapV2Router02(uniswapRouter).getAmountsOut(amount, path) returns (uint256[] memory out) {
            amounts = out;
        } catch {
            return (0, "");
        }
        uint256 tokenAmount = amounts[1];

        address[] memory reversePath = new address[](2);
        reversePath[0] = governanceToken;
        reversePath[1] = asset;
        try IUniswapV2Router02(uniswapRouter).getAmountsOut(tokenAmount, reversePath) returns (uint256[] memory out) {
            amounts = out;
        } catch {
            return (0, "");
        }
        uint256 sellAmount = amounts[1];

        if (sellAmount > (amount * (10000 + minProfitMargin)) / 10000) {
            profit = sellAmount - amount;
            executionData = abi.encode(governanceToken);
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256 profit)
    {
        address governanceToken = abi.decode(executionData, (address));
        address uniswapRouter = IRegistry(registry).getAddress("UNISWAP_V2");
        address usdc = IRegistry(registry).getAddress("USDC");

        require(IERC20(usdc).balanceOf(address(this)) >= amount, "Insufficient USDC balance");

        // Buy governance token
        address[] memory path = new address[](2);
        path[0] = usdc;
        path[1] = governanceToken;
        IERC20(usdc).approve(uniswapRouter, amount);
        uint256[] memory amounts = IUniswapV2Router02(uniswapRouter).swapExactTokensForTokens(
            amount,
            (amount * (10000 - minProfitMargin)) / 10000, // Slippage protection
            path,
            address(this),
            block.timestamp + 300
        );
        IERC20(usdc).approve(uniswapRouter, 0); // Reset allowance
        uint256 tokenAmount = amounts[1];

        // Sell governance tokens
        path[0] = governanceToken;
        path[1] = usdc;
        require(IERC20(governanceToken).balanceOf(address(this)) >= tokenAmount, "Insufficient gov token balance");
        IERC20(governanceToken).approve(uniswapRouter, tokenAmount);
        amounts = IUniswapV2Router02(uniswapRouter).swapExactTokensForTokens(
            tokenAmount,
            (amount * (10000 + minProfitMargin)) / 10000, // Ensure profit
            path,
            address(this),
            block.timestamp + 300
        );
        IERC20(governanceToken).approve(uniswapRouter, 0); // Reset allowance

        uint256 finalAmount = amounts[1];
        require(finalAmount > amount + premium, "Insufficient profit");
        profit = finalAmount - amount - premium;

        emit ArbitrageExecuted(usdc, governanceToken, profit, block.timestamp);
        success = true;
        result = abi.encode(governanceToken, profit);
        return (success, result, profit);
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyBridgingLatencyArbitrage.sol
---Filename: StrategyBridgingLatencyArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IStrategy.sol";
import "../interfaces/IRegistry.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@chainlink/contracts/src/v0.8/shared/interfaces/AggregatorV3Interface.sol";
import "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";
import "@uniswap/v3-periphery/contracts/interfaces/IQuoterV2.sol";

// SKALE IMA Bridge interface (simplified)
interface ISkaleIMABridge {
    function deposit(
        address token,
        uint256 amount,
        address receiver,
        bytes32 destinationChainId
    ) external;
    function withdraw(
        address token,
        uint256 amount,
        address receiver
    ) external;
}

contract StrategyBridgingLatencyArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    address public registry;
    ISwapRouter public uniswapRouter; // Uniswap V3 on SKALE (Ruby Exchange) or Arbitrum
    IQuoterV2 public uniswapQuoter;   // For price quoting
    ISkaleIMABridge public skaleBridge;
    uint256 public constant SLIPPAGE_TOLERANCE = 50; // 0.5% (50 basis points)
    uint256 public constant DEADLINE_EXTENSION = 300; // 5 minutes
    uint24 public constant POOL_FEE = 3000; // Uniswap V3 0.3% fee tier
    bytes32 public constant ARBITRUM_CHAIN_ID = bytes32(uint256(42161)); // Arbitrum chain ID
    uint256 public constant MIN_PROFIT_MARGIN = 100; // 1% minimum profit
    bool public paused;

    // Supported assets (mapped to their Chainlink price feeds)
    mapping(address => address) public assetPriceFeeds; // asset => Chainlink feed
    address[] public supportedAssets;

    event ArbitrageExecuted(
        address indexed asset,
        uint256 amountIn,
        uint256 profit,
        bool buyOnSkale,
        uint256 timestamp
    );
    event AssetAdded(address indexed asset, address priceFeed);
    event Paused(bool paused);

    modifier whenNotPaused() {
        require(!paused, "Strategy paused");
        _;
    }

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers(); // Prevent initialization during deployment
    }

    function initialize(
        address _registry,
        address _uniswapRouter,
        address _uniswapQuoter,
        address _skaleBridge,
        address _initialOwner
    ) external initializer {
        __Ownable_init(_initialOwner);
        __UUPSUpgradeable_init();
        __ReentrancyGuard_init();
        registry = _registry;
        uniswapRouter = ISwapRouter(_uniswapRouter);
        uniswapQuoter = IQuoterV2(_uniswapQuoter);
        skaleBridge = ISkaleIMABridge(_skaleBridge);
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "BridgingLatencyArbitrage";
    }

    function addSupportedAsset(address asset, address priceFeed) external onlyOwner {
        require(asset != address(0) && priceFeed != address(0), "Invalid addresses");
        require(assetPriceFeeds[asset] == address(0), "Asset already supported");
        assetPriceFeeds[asset] = priceFeed;
        supportedAssets.push(asset);
        emit AssetAdded(asset, priceFeed);
    }

    function setPaused(bool _paused) external onlyOwner {
        paused = _paused;
        emit Paused(_paused);
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        override
        whenNotPaused
        returns (uint256 profit, bytes memory executionData)
    {
        address priceFeed = assetPriceFeeds[asset];
        if (priceFeed == address(0)) return (0, "");

        (uint256 skalePrice,) = _getUniswapV3Price(asset, amount);
        uint256 arbitrumPrice = skalePrice * 102 / 100; // Placeholder: 2% higher

        bool buyOnSkale = skalePrice < arbitrumPrice;
        uint256 buyPrice = buyOnSkale ? skalePrice : arbitrumPrice;
        uint256 sellPrice = buyOnSkale ? arbitrumPrice : skalePrice;

        uint256 bridgeFee = _estimateBridgeFee(asset, amount);
        uint256 arbitrumGasCost = buyOnSkale ? _estimateArbitrumGasCost() : 0;

        if (sellPrice > buyPrice) {
            uint256 grossProfit = sellPrice - buyPrice;
            uint256 netProfit = grossProfit > (bridgeFee + arbitrumGasCost) ? grossProfit - (bridgeFee + arbitrumGasCost) : 0;
            uint256 profitMargin = (netProfit * 10000) / buyPrice;
            if (profitMargin >= MIN_PROFIT_MARGIN) {
                profit = netProfit;
                executionData = abi.encode(buyOnSkale);
            }
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        whenNotPaused
        returns (bool success, bytes memory result, uint256)
    {
        (bool buyOnSkale) = abi.decode(executionData, (bool));
        address asset = IRegistry(registry).getAddress("USDC");
        address weth = IRegistry(registry).getAddress("WETH");

        require(assetPriceFeeds[asset] != address(0), "Unsupported asset");
        require(amount > 0, "Invalid amount");
        IERC20 assetToken = IERC20(asset);
        require(assetToken.balanceOf(address(this)) >= amount, "Insufficient balance");

        uint256 wethOut;
        if (buyOnSkale) {
            wethOut = _swapOnSkale(asset, weth, amount);
            _bridgeToArbitrum(weth, wethOut);
        } else {
            _bridgeToArbitrum(asset, amount);
            wethOut = amount; // Simplified
        }

        uint256 usdcOut = _simulateSellSwap(weth, wethOut);
        require(usdcOut > amount + premium, "Insufficient profit");
        uint256 profit = usdcOut - amount - premium;

        emit ArbitrageExecuted(asset, amount, profit, buyOnSkale, block.timestamp);
        success = true;
        result = abi.encode(buyOnSkale, profit);
        return (success, result, profit);
    }

    function _swapOnSkale(address tokenIn, address tokenOut, uint256 amountIn)
        internal
        returns (uint256 amountOut)
    {
        IERC20(tokenIn).approve(address(uniswapRouter), amountIn);

        ISwapRouter.ExactInputSingleParams memory params = ISwapRouter.ExactInputSingleParams({
            tokenIn: tokenIn,
            tokenOut: tokenOut,
            fee: POOL_FEE,
            recipient: address(this),
            deadline: block.timestamp + DEADLINE_EXTENSION,
            amountIn: amountIn,
            amountOutMinimum: (amountIn * (10000 - SLIPPAGE_TOLERANCE)) / 10000,
            sqrtPriceLimitX96: 0
        });

        amountOut = uniswapRouter.exactInputSingle(params);
        IERC20(tokenIn).approve(address(uniswapRouter), 0);
        require(amountOut > 0, "Swap failed");
    }

    function _bridgeToArbitrum(address token, uint256 amount) internal {
        IERC20(token).approve(address(skaleBridge), amount);
        skaleBridge.deposit(token, amount, address(this), ARBITRUM_CHAIN_ID);
        IERC20(token).approve(address(skaleBridge), 0);
    }

    function _estimateBridgeFee(address, uint256) internal pure returns (uint256) {
        return 0; // SKALE IMA Bridge is typically free
    }

    function _estimateArbitrumGasCost() internal view returns (uint256) {
        address ethUsdFeed = IRegistry(registry).getAddress("ETH_USD_FEED");
        (, int256 ethPrice,,,) = AggregatorV3Interface(ethUsdFeed).latestRoundData();
        require(ethPrice > 0, "Invalid ETH price");
        uint256 gasPrice = 100 gwei;
        uint256 gasCostEth = (200_000 * gasPrice) / 1e18;
        return (gasCostEth * uint256(ethPrice)) / 1e8;
    }

    function _simulateSellSwap(address tokenIn, uint256 amountIn) internal pure returns (uint256) {
        return amountIn * 101 / 100; // Placeholder: 1% profit
    }

    function _getUniswapV3Price(address tokenIn, uint256 amountIn)
        internal
        returns (uint256 amountOut, bool valid)
    {
        try uniswapQuoter.quoteExactInputSingle(
            IQuoterV2.QuoteExactInputSingleParams({
                tokenIn: tokenIn,
                tokenOut: IRegistry(registry).getAddress("WETH"),
                amountIn: amountIn,
                fee: POOL_FEE,
                sqrtPriceLimitX96: 0
            })
        ) returns (uint256 quotedAmountOut, uint160, uint32, uint256) {
            amountOut = quotedAmountOut;
            valid = true;
        } catch {
            amountOut = 0;
            valid = false;
        }
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyLPBurnArbitrage.sol
---Filename: StrategyLPBurnArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "@uniswap/v2-core/contracts/interfaces/IUniswapV2Pair.sol";
import "@uniswap/v2-periphery/contracts/interfaces/IUniswapV2Router02.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

contract StrategyLPBurnArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    address public registry;
    uint256 public slippageTolerance; // 0.5% (50 basis points)

    event ArbitrageExecuted(address indexed pair, address indexed asset, uint256 profit, uint256 timestamp);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers(); // Prevent initialization during deployment
    }

    function initialize(address _registry) external initializer {
        require(_registry != address(0), "Invalid registry");        
        __Ownable_init(msg.sender);
        __UUPSUpgradeable_init();
        __ReentrancyGuard_init();
        registry = _registry;
        slippageTolerance = 50; // 0.5%
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "LPBurnArbitrage";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        address pair = IRegistry(registry).getAddress("UNISWAP_V2_PAIR"); // e.g., USDC/WETH
        address uniswapRouter = IRegistry(registry).getAddress("UNISWAP_V2");
        address usdc = IRegistry(registry).getAddress("USDC");
        if (asset != usdc || pair == address(0)) return (0, "");

        uint256 reserve0;
        uint256 reserve1;
        try IUniswapV2Pair(pair).getReserves() returns (uint112 r0, uint112 r1, uint32) {
            reserve0 = r0;
            reserve1 = r1;
        } catch {
            return (0, "");
        }
        address token0 = IUniswapV2Pair(pair).token0();
        address token1 = IUniswapV2Pair(pair).token1();
        uint256 totalSupply = IUniswapV2Pair(pair).totalSupply();
        if (totalSupply == 0) return (0, "");

        // Calculate amounts received from burning LP tokens
        uint256 amount0Out = (amount * reserve0) / totalSupply;
        uint256 amount1Out = (amount * reserve1) / totalSupply;
        if (amount0Out == 0 && amount1Out == 0) return (0, "");

        // Convert both tokens to `asset` (e.g., USDC)
        uint256 token0Value = 0;
        if (token0 != asset && amount0Out > 0) {
            address[] memory path = new address[](2);
            path[0] = token0;
            path[1] = asset;
            try IUniswapV2Router02(uniswapRouter).getAmountsOut(amount0Out, path) returns (uint256[] memory amounts) {
                token0Value = amounts[1];
            } catch {
                token0Value = 0;
            }
        } else if (token0 == asset) {
            token0Value = amount0Out;
        }

        uint256 token1Value = 0;
        if (token1 != asset && amount1Out > 0) {
            address[] memory path = new address[](2);
            path[0] = token1;
            path[1] = asset;
            try IUniswapV2Router02(uniswapRouter).getAmountsOut(amount1Out, path) returns (uint256[] memory amounts) {
                token1Value = amounts[1];
            } catch {
                token1Value = 0;
            }
        } else if (token1 == asset) {
            token1Value = amount1Out;
        }

        uint256 totalValue = token0Value + token1Value;
        if (totalValue > amount) {
            profit = totalValue - amount;
            executionData = abi.encode(pair, token0, token1);
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256 profit)
    {
        (address pair, address token0, address token1) = abi.decode(executionData, (address, address, address));
        require(pair != address(0) && token0 != address(0) && token1 != address(0), "Invalid pair or tokens");
        address uniswapRouter = IRegistry(registry).getAddress("UNISWAP_V2");
        address asset = IRegistry(registry).getAddress("USDC");

        // Ensure sufficient LP token balance
        require(IERC20(pair).balanceOf(address(this)) >= amount, "Insufficient LP balance");

        // Burn LP tokens to receive token0 and token1
        IERC20(pair).approve(pair, amount);
        try IUniswapV2Pair(pair).burn(address(this)) {
            // Burn successful
        } catch {
            revert("LP burn failed");
        }
        IERC20(pair).approve(pair, 0); // Reset allowance

        // Get balances of token0 and token1
        uint256 token0Balance = IERC20(token0).balanceOf(address(this));
        uint256 token1Balance = IERC20(token1).balanceOf(address(this));
        require(token0Balance > 0 || token1Balance > 0, "No tokens received from burn");

        // Swap token0 to asset (if not already asset)
        uint256 assetReceived = 0;
        if (token0 != asset && token0Balance > 0) {
            IERC20(token0).approve(uniswapRouter, token0Balance);
            address[] memory path = new address[](2);
            path[0] = token0;
            path[1] = asset;
            uint256 minOut = (token0Balance * (10000 - slippageTolerance)) / 10000;
            try IUniswapV2Router02(uniswapRouter).swapExactTokensForTokens(
                token0Balance,
                minOut,
                path,
                address(this),
                block.timestamp + 300
            ) returns (uint256[] memory amounts) {
                assetReceived += amounts[1];
            } catch {
                revert("Token0 swap failed");
            }
            IERC20(token0).approve(uniswapRouter, 0); // Reset allowance
        } else if (token0 == asset) {
            assetReceived += token0Balance;
        }

        // Swap token1 to asset (if not already asset)
        if (token1 != asset && token1Balance > 0) {
            IERC20(token1).approve(uniswapRouter, token1Balance);
            address[] memory path = new address[](2);
            path[0] = token1;
            path[1] = asset;
            uint256 minOut = (token1Balance * (10000 - slippageTolerance)) / 10000;
            try IUniswapV2Router02(uniswapRouter).swapExactTokensForTokens(
                token1Balance,
                minOut,
                path,
                address(this),
                block.timestamp + 300
            ) returns (uint256[] memory amounts) {
                assetReceived += amounts[1];
            } catch {
                revert("Token1 swap failed");
            }
            IERC20(token1).approve(uniswapRouter, 0); // Reset allowance
        } else if (token1 == asset) {
            assetReceived += token1Balance;
        }

        // Calculate profit
        require(assetReceived > amount + premium, "Insufficient profit");
        profit = assetReceived - amount - premium;

        emit ArbitrageExecuted(pair, asset, profit, block.timestamp);
        success = true;
        result = abi.encode(pair, profit);
        return (success, result, profit);
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyGasRefundArbitrage.sol
---Filename: StrategyGasRefundArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "../utils/StrategyLib.sol";

// Mock gas price oracle interface for SKALE
interface IGasPriceOracle {
    function getGasPrice() external view returns (uint256);
}

contract StrategyGasRefundArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    address public registry;
    uint256 public slippageTolerance;
    uint256 public estimatedGas;
    address public gasOracle; // Mock gas oracle for SKALE

    event GasArbitrageExecuted(address indexed asset, uint256 profit, uint256 timestamp);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers(); // Prevent initialization during deployment
    }

    function initialize(address _registry) external initializer {
        require(_registry != address(0), "Invalid registry");   
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();             
        __UUPSUpgradeable_init();
        registry = _registry;
        slippageTolerance = 50; // 0.5%
        estimatedGas = 100000;
        gasOracle = IRegistry(_registry).getAddress("GAS_ORACLE"); // Fetch from Registry
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "GasRefundArbitrage";
    }

    struct ExecutionParams {
        address weth;
        uint256 refund;
        address router;
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        address usdc = IRegistry(registry).getAddress("USDC");
        if (asset != usdc) return (0, "");

        (address weth, address router, uint256 wethOut) = _getBestSwapRoute(usdc, amount);
        (uint256 ethPrice, uint8 decimals) = StrategyLib.getOnchainPrice(IRegistry(registry).getAddress("ETH_USD_FEED"));
        uint256 refundInWeth = _calculateRefundValue(ethPrice, decimals);

        if (wethOut + refundInWeth > amount) {
            profit = wethOut + refundInWeth - amount;
            executionData = abi.encode(ExecutionParams(weth, refundInWeth, router));
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool, bytes memory, uint256)
    {
        ExecutionParams memory params = abi.decode(executionData, (ExecutionParams));
        address usdc = IRegistry(registry).getAddress("USDC");

        _validateExecution(params.weth, params.router, usdc, amount);
        uint256 wethOut = _executeSwap(usdc, params.weth, amount, params.router);
        uint256 profit = _calculateProfit(amount, premium, wethOut, params.refund);

        emit GasArbitrageExecuted(usdc, profit, block.timestamp);
        return (true, abi.encode(wethOut, params.refund, profit), profit);
    }

    function _getBestSwapRoute(address usdc, uint256 amount) internal view returns (address, address, uint256) {
        address weth = IRegistry(registry).getAddress("WETH");
        address uniswap = IRegistry(registry).getAddress("UNISWAP_V2");
        address sushiswap = IRegistry(registry).getAddress("SUSHISWAP");

        uint256 uniOut = StrategyLib._getPrice(uniswap, usdc, weth, amount);
        uint256 sushiOut = StrategyLib._getPrice(sushiswap, usdc, weth, amount);

        return uniOut >= sushiOut 
            ? (weth, uniswap, uniOut) 
            : (weth, sushiswap, sushiOut);
    }

    function _calculateRefundValue(uint256 ethPrice, uint8 decimals) internal view returns (uint256) {
        uint256 gasPrice;
        try IGasPriceOracle(gasOracle).getGasPrice() returns (uint256 price) {
            gasPrice = price;
        } catch {
            gasPrice = 0; // Default to zero for SKALE
        }
        uint256 gasCost = gasPrice * estimatedGas;
        uint256 refund = gasCost / 2;
        return (refund * ethPrice) / (10 ** decimals);
    }

    function _validateExecution(address weth, address router, address usdc, uint256 amount) internal view {
        require(weth == IRegistry(registry).getAddress("WETH"), "Invalid WETH");
        require(
            router == IRegistry(registry).getAddress("UNISWAP_V2") ||
            router == IRegistry(registry).getAddress("SUSHISWAP"),
            "Invalid router"
        );
        require(usdc != address(0), "Invalid USDC");
        require(IERC20(usdc).balanceOf(address(this)) >= amount, "Insufficient USDC");
    }

    function _executeSwap(address tokenIn, address tokenOut, uint256 amount, address router) internal returns (uint256) {
        address[] memory path = new address[](2);
        path[0] = tokenIn;
        path[1] = tokenOut;
        
        IERC20(tokenIn).approve(router, amount);
        uint256 amountOut = StrategyLib.swapTokens(
            router,
            path,
            amount,
            (amount * (10000 - slippageTolerance)) / 10000
        );
        IERC20(tokenIn).approve(router, 0);
        
        return amountOut;
    }

    function _calculateProfit(uint256 amount, uint256 premium, uint256 wethOut, uint256 refund) internal pure returns (uint256) {
        uint256 totalValue = wethOut + refund;
        require(totalValue > amount + premium, "Insufficient profit");
        return totalValue - amount - premium;
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyFlashMintArbitrage.sol
---Filename: StrategyFlashMintArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IStrategy.sol";
import "../interfaces/IRegistry.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";
import "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";
import "@chainlink/contracts/src/v0.8/shared/interfaces/AggregatorV3Interface.sol";

// Uniswap V3 Quoter interface for SKALE (Ruby Exchange)
interface IQuoterV2 {
    function quoteExactInputSingle(
        address tokenIn,
        address tokenOut,
        uint24 fee,
        uint256 amountIn,
        uint160 sqrtPriceLimitX96
    ) external view returns (uint256 amountOut, uint160 sqrtPriceNextX96, uint32 initializedTicksCrossed, uint256 gasEstimate);
}

// Generic flash mint protocol interface
interface IFlashMintProtocol {
    function flashMint(uint256 amount, bytes calldata data) external;
}

contract StrategyFlashMintArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    address public registry;
    ISwapRouter public uniswapRouter; // Ruby Exchange (Uniswap V3-compatible)
    address public quoter; // Uniswap V3 Quoter for price estimation
    uint256 public slippageTolerance; // 0.5% (50 basis points)
    uint256 public minProfitMargin; // 1% minimum profit
    uint24 public poolFee; // Uniswap V3 0.3% fee tier
    uint256 public deadlineExtension; // 5 minutes
    bool public paused;

    // Supported flash mint tokens and protocols
    mapping(address => address) public flashMintProtocols; // token => protocol
    mapping(address => address) public priceFeeds; // token => Chainlink feed
    address[] public supportedTokens;

    event ArbitrageExecuted(
        address indexed token,
        address indexed outputToken,
        uint256 amountIn,
        uint256 profit,
        uint256 timestamp
    );
    event TokenAdded(address indexed token, address protocol, address priceFeed);
    event Paused(bool paused);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers(); // Prevent initialization during deployment
    }

    function initialize(
        address _registry,
        address _uniswapRouter,
        address _quoter,
        address _initialOwner
    ) external initializer {
        require(_registry != address(0) && _uniswapRouter != address(0) && _quoter != address(0) && _initialOwner != address(0), "Invalid addresses");
        __Ownable_init(_initialOwner);
        __ReentrancyGuard_init();        
        __UUPSUpgradeable_init();
        registry = _registry;
        uniswapRouter = ISwapRouter(_uniswapRouter);
        quoter = _quoter;
        slippageTolerance = 50; // 0.5%
        minProfitMargin = 100; // 1%
        poolFee = 3000; // 0.3% fee tier
        deadlineExtension = 300; // 5 minutes
        paused = false;
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    modifier whenNotPaused() {
        require(!paused, "Strategy paused");
        _;
    }

    function name() external pure override returns (string memory) {
        return "FlashMintArbitrage";
    }

    // Add supported flash mint token with its protocol and Chainlink price feed
    function addSupportedToken(address token, address protocol, address priceFeed) external onlyOwner {
        require(token != address(0) && protocol != address(0) && priceFeed != address(0), "Invalid addresses");
        require(flashMintProtocols[token] == address(0), "Token already supported");
        flashMintProtocols[token] = protocol;
        priceFeeds[token] = priceFeed;
        supportedTokens.push(token);
        emit TokenAdded(token, protocol, priceFeed);
    }

    // Pause or unpause the strategy
    function setPaused(bool _paused) external onlyOwner {
        paused = _paused;
        emit Paused(_paused);
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        whenNotPaused
        returns (uint256 profit, bytes memory executionData)
    {
        // Validate flash mint token
        address protocol = flashMintProtocols[asset];
        if (protocol == address(0)) return (0, "");

        address skaleRouter = IRegistry(registry).getAddress("UNISWAP_V3");
        address weth = IRegistry(registry).getAddress("WETH");

        // Get price for token -> WETH
        (uint256 wethOut, bool valid) = _getUniswapV3Price(skaleRouter, asset, weth, amount);
        if (!valid) return (0, "");

        // Get price for WETH -> token
        (uint256 tokenOut, bool reverseValid) = _getUniswapV3Price(skaleRouter, weth, asset, wethOut);
        if (!reverseValid) return (0, "");

        // Calculate profit
        if (tokenOut > amount) {
            uint256 grossProfit = tokenOut - amount;
            uint256 gasCost = _estimateGasCost();
            profit = grossProfit > gasCost ? grossProfit - gasCost : 0;
            uint256 profitMargin = (profit * 10000) / amount;
            if (profitMargin >= minProfitMargin) {
                executionData = abi.encode(asset, protocol, weth);
            }
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        whenNotPaused
        returns (bool success, bytes memory result, uint256)
    {
        (address token, address protocol, address weth) = abi.decode(executionData, (address, address, address));
        require(flashMintProtocols[token] == protocol, "Invalid protocol");
        require(priceFeeds[token] != address(0), "Unsupported token");

        // Encode flash mint data
        bytes memory flashData = abi.encode(token, weth, amount, premium);
        IFlashMintProtocol(protocol).flashMint(amount, flashData);

        // Verify final balance
        uint256 finalBalance = IERC20(token).balanceOf(address(this));
        require(finalBalance >= amount + premium, "Insufficient profit");
        uint256 profit = finalBalance - amount - premium;

        emit ArbitrageExecuted(token, weth, amount, profit, block.timestamp);
        success = true;
        result = abi.encode(token, weth, profit);
        return (success, result, profit);
    }

    // Callback for flash mint
    function onFlashMintCallback(bytes calldata data) external nonReentrant {
        (address token, address weth, uint256 amount, uint256 premium) = abi.decode(data, (address, address, uint256, uint256));
        address protocol = flashMintProtocols[token];
        require(msg.sender == protocol, "Unauthorized caller");

        // Verify flash minted balance
        IERC20 tokenContract = IERC20(token);
        require(tokenContract.balanceOf(address(this)) >= amount, "Insufficient flash minted balance");

        // Swap token -> WETH
        uint256 wethOut = _swapOnSkale(token, weth, amount);

        // Swap WETH -> token
        uint256 tokenOut = _swapOnSkale(weth, token, wethOut);

        // Repay flash mint (assuming no fee for simplicity)
        require(tokenOut >= amount + premium, "Insufficient tokens to repay");
        tokenContract.safeTransfer(protocol, amount);

        // Profit is retained in the contract
    }

    // Swap on SKALE (Ruby Exchange, Uniswap V3-compatible)
    function _swapOnSkale(address tokenIn, address tokenOut, uint256 amountIn)
        internal
        returns (uint256 amountOut)
    {
        IERC20(tokenIn).approve(address(uniswapRouter), amountIn);

        ISwapRouter.ExactInputSingleParams memory params = ISwapRouter.ExactInputSingleParams({
            tokenIn: tokenIn,
            tokenOut: tokenOut,
            fee: poolFee,
            recipient: address(this),
            deadline: block.timestamp + deadlineExtension,
            amountIn: amountIn,
            amountOutMinimum: (amountIn * (10000 - slippageTolerance)) / 10000,
            sqrtPriceLimitX96: 0
        });

        amountOut = uniswapRouter.exactInputSingle(params);
        IERC20(tokenIn).approve(address(uniswapRouter), 0);
        require(amountOut > 0, "Swap failed");
    }

    // Get Uniswap V3 price using Quoter
    function _getUniswapV3Price(address router, address tokenIn, address tokenOut, uint256 amountIn)
        internal
        view
        returns (uint256 amountOut, bool valid)
    {
        try IQuoterV2(quoter).quoteExactInputSingle(tokenIn, tokenOut, poolFee, amountIn, 0) returns (
            uint256 out, uint160, uint32, uint256
        ) {
            amountOut = out;
            valid = true;
        } catch {
            amountOut = 0;
            valid = false;
        }
    }

    // Estimate gas cost (SKALE is gasless, but include for overhead)
    function _estimateGasCost() internal view returns (uint256) {
        address ethUsdFeed = IRegistry(registry).getAddress("ETH_USD_FEED");
        (, int256 ethPrice,,,) = AggregatorV3Interface(ethUsdFeed).latestRoundData();
        require(ethPrice > 0, "Invalid ETH price");
        uint256 gasPrice = 0; // SKALE is gasless
        uint256 gasCostEth = (150_000 * gasPrice) / 1e18; // 150k gas for overhead
        return (gasCostEth * uint256(ethPrice)) / 1e8; // Convert to USD
    }

    // Emergency withdraw function
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).safeTransfer(owner(), amount);
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyCrossDexLendingArbitrage.sol
---Filename: StrategyCrossDexLendingArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "../interfaces/IAavePool.sol";
import "../interfaces/ICompound.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
// import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";

contract StrategyCrossDexLendingArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    using SafeERC20 for IERC20;

    address public registry;

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers(); // Prevent initialization during deployment
    }

    function initialize(address _registry) external initializer {      
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        registry = _registry;
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "CrossDexLendingArbitrage";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        address aavePool = IRegistry(registry).getAddress("AAVE_LENDING_POOL");
        address compound = IRegistry(registry).getAddress("COMPTROLLER");

        (, , , , uint128 aaveBorrowRate, , , , , , , ) = IAavePool(aavePool).getReserveData(asset);
        uint256 compoundSupplyRate = ICompound(compound).getSupplyRate(asset);

        if (compoundSupplyRate > aaveBorrowRate) {
            profit = (amount * (compoundSupplyRate - aaveBorrowRate)) / 1e18;
            executionData = abi.encode(aavePool, compound);
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256)
    {
        (address aavePool, address compound) = abi.decode(executionData, (address, address));
        
        address asset = IRegistry(registry).getAddress("USDC");
        address cToken = IRegistry(registry).getAddress("CUSDC");

        // Ensure contract is approved to borrow
        require(IERC20(asset).balanceOf(address(this)) >= 0, "Invalid initial balance");

        // Borrow from Aave (interestRateMode = 2 for variable rate)
        IAavePool(aavePool).borrow(asset, amount, 2, 0, address(this));

        // Verify borrowed amount
        uint256 borrowedBalance = IERC20(asset).balanceOf(address(this));
        require(borrowedBalance >= amount, "Borrow failed");

        // Supply to Compound V2 (using cToken)
        SafeERC20.forceApprove(IERC20(asset), cToken, amount);
        require(ICompound(cToken).mint(amount) == 0, "Compound supply failed");

        // Reset approval for safety
        SafeERC20.forceApprove(IERC20(asset), cToken, 0);

        // Simplified: Return estimated profit
        uint256 estimatedProfit = amount - premium;
        require(estimatedProfit > 0, "No profit after premium");

        uint256 profit = estimatedProfit; // Lending APY - borrow APR
        success = true;
        result = abi.encode(asset, profit);
        return (success, result, profit - premium);
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyLiquityLiquidation.sol
---Filename: StrategyLiquityLiquidation.sol
---File Content Start---
// contracts/pmms/strategies/StrategyLiquityLiquidation.sol
pragma solidity ^0.8.20;

import {IStrategy} from "../interfaces/IStrategy.sol";
import {IRegistry} from "../interfaces/IRegistry.sol";
import {ILiquityTroveManager} from "../interfaces/ILiquityTroveManager.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {Initializable} from "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import {UUPSUpgradeable} from "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import {OwnableUpgradeable} from "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import {ReentrancyGuardUpgradeable} from "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";

contract StrategyLiquityLiquidation is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable, ReentrancyGuardUpgradeable {
    IRegistry public registry;

    event ArbitrageExecuted(address indexed asset, uint256 amount, uint256 profit, uint256 timestamp);

    function initialize(address _registry) external initializer {
        __Ownable_init(msg.sender);
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        registry = IRegistry(_registry);
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "LiquityLiquidation";
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        address troveManager = registry.getAddress("LIQUITY_TROVE_MANAGER");
        // Placeholder: Assume off-chain bot identifies undercollateralized trove
        address borrower = address(0); // Replace with real borrower address in production
        uint256 debt = ILiquityTroveManager(troveManager).getTroveDebt(borrower);
        uint256 coll = ILiquityTroveManager(troveManager).getTroveColl(borrower);
        // Liquity V2: Liquidation if collateral ratio < 110%
        if (coll * 110 / 100 < debt && debt > 0) {
            profit = coll - debt; // Simplified profit estimate (ETH - LUSD debt)
            executionData = abi.encode(borrower);
        }
        return (profit, executionData);
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        nonReentrant
        returns (bool success, bytes memory result, uint256)
    {
        address borrower = abi.decode(executionData, (address));
        address troveManager = registry.getAddress("LIQUITY_TROVE_MANAGER");
        address lusd = registry.getAddress("LUSD");
        IERC20(lusd).approve(troveManager, amount);
        ILiquityTroveManager(troveManager).liquidate(borrower);
        address weth = registry.getAddress("WETH");
        uint256 profit = IERC20(weth).balanceOf(address(this)) - amount - premium;
        if (profit > 0) {
            emit ArbitrageExecuted(lusd, amount, profit, block.timestamp); // Fix: Use lusd instead of asset
            return (true, abi.encode(profit), profit);
        }
        return (false, abi.encode("No profit"), 0);
    }
}---File Content End---

---Path and files name: pmms/contracts/strategies/StrategyDexArbitrage.sol
---Filename: StrategyDexArbitrage.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "../interfaces/IRegistry.sol";
import "../interfaces/IStrategy.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@uniswap/v2-periphery/contracts/interfaces/IUniswapV2Router02.sol";

contract StrategyDexArbitrage is IStrategy, Initializable, UUPSUpgradeable, OwnableUpgradeable {
    using SafeERC20 for IERC20;

    address public registry;
    uint256 public slippageDenominator;
    uint256 public slippageTolerance; // 0.5%

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers(); // Prevent initialization during deployment
    }

    function initialize(address _registry) external initializer {
        require(_registry != address(0), "Invalid registry");
        __Ownable_init(msg.sender);
        __UUPSUpgradeable_init();
        registry = _registry;
        slippageDenominator = 10000;
        slippageTolerance = 50; // 0.5%
    }

    function _authorizeUpgrade(address newImplementation) internal override onlyOwner {}

    function name() external pure override returns (string memory) {
        return "DexArbitrage";
    }

    struct DexAddresses {
        address uniswap;
        address sushiswap;
        address weth;
    }

    function checkOpportunity(address asset, uint256 amount)
        external
        view
        override
        returns (uint256 profit, bytes memory executionData)
    {
        DexAddresses memory dex = _getDexAddresses();
        
        uint256 uniOut = _getPrice(dex.uniswap, asset, dex.weth, amount);
        uint256 sushiOut = _getPrice(dex.sushiswap, asset, dex.weth, amount);

        if (uniOut > _applySlippage(sushiOut, true)) {
            profit = uniOut - sushiOut;
            executionData = abi.encode(asset, dex.uniswap, dex.sushiswap);
        } else if (sushiOut > _applySlippage(uniOut, true)) {
            profit = sushiOut - uniOut;
            executionData = abi.encode(asset, dex.sushiswap, dex.uniswap);
        }
    }

    function execute(bytes memory executionData, uint256 amount, uint256 premium)
        external
        override
        returns (bool, bytes memory, uint256)
    {
        (address token, address buyDex, address sellDex) = abi.decode(executionData, (address, address, address));
        DexAddresses memory dex = _getDexAddresses();

        uint256 receivedAmount = _swapTokens(
            buyDex,
            token,
            dex.weth,
            amount,
            _applySlippage(amount, false)
        );

        uint256 finalAmount = _swapTokens(
            sellDex,
            dex.weth,
            token,
            receivedAmount,
            0
        );

        uint256 profit = finalAmount > amount + premium ? finalAmount - amount - premium : 0;
        require(profit > 0, "Insufficient profit");
        
        return (true, abi.encode(profit), profit);
    }

    function _getDexAddresses() internal view returns (DexAddresses memory) {
        return DexAddresses({
            uniswap: IRegistry(registry).getAddress("UNISWAP_V2"),
            sushiswap: IRegistry(registry).getAddress("SUSHISWAP"),
            weth: IRegistry(registry).getAddress("WETH")
        });
    }

    function _applySlippage(uint256 amount, bool isPositive) internal view returns (uint256) {
        return isPositive 
            ? (amount * (slippageDenominator + slippageTolerance)) / slippageDenominator
            : (amount * (slippageDenominator - slippageTolerance)) / slippageDenominator;
    }

    function _swapTokens(
        address router,
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        uint256 amountOutMin
    ) internal returns (uint256) {
        address[] memory path = new address[](2);
        path[0] = tokenIn;
        path[1] = tokenOut;

        IERC20(tokenIn).approve(router, amountIn);
        uint256[] memory amounts = IUniswapV2Router02(router).swapExactTokensForTokens(
            amountIn,
            amountOutMin,
            path,
            address(this),
            block.timestamp + 300
        );
        IERC20(tokenIn).approve(router, 0);

        return amounts[1];
    }

    function _getPrice(address dex, address from, address to, uint256 amountIn)
        internal
        view
        returns (uint256)
    {
        address[] memory path = new address[](2);
        path[0] = from;
        path[1] = to;
        uint256[] memory amounts = IUniswapV2Router02(dex).getAmountsOut(amountIn, path);
        return amounts[1];
    }

    function withdraw(address token, uint256 amount) external {
        require(msg.sender == registry, "Only registry can withdraw");
        IERC20(token).safeTransfer(msg.sender, amount);
    }
}---File Content End---

---Path and files name: pmms/contracts/utils/ArbitrumSellHandler.sol
---Filename: ArbitrumSellHandler.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";
import "@chainlink/contracts/src/v0.8/shared/interfaces/AggregatorV3Interface.sol";

contract ArbitrumSellHandler is ReentrancyGuard, Ownable {
    using SafeERC20 for IERC20;

    ISwapRouter public immutable uniswapRouter; // Uniswap V3 on Arbitrum
    address public immutable skaleStrategy; // SKALE strategy contract
    uint24 public constant POOL_FEE = 3000; // 0.3% fee tier
    uint256 public constant SLIPPAGE_TOLERANCE = 50; // 0.5%
    uint256 public constant DEADLINE_EXTENSION = 300; // 5 minutes

    event SellExecuted(address indexed tokenIn, uint256 amountIn, uint256 amountOut, uint256 timestamp);

    constructor(address _uniswapRouter, address _skaleStrategy, address _initialOwner) Ownable(_initialOwner) {
        uniswapRouter = ISwapRouter(_uniswapRouter);
        skaleStrategy = _skaleStrategy;
    }

    // Called by SKALE IMA Bridge or off-chain relayer after bridging
    function executeSell(
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        uint256 minAmountOut
    ) external nonReentrant {
        // In production, secure this with a signature or bridge authentication
        require(msg.sender == skaleStrategy, "Unauthorized");

        IERC20(tokenIn).approve(address(uniswapRouter), amountIn);

        ISwapRouter.ExactInputSingleParams memory params = ISwapRouter.ExactInputSingleParams({
            tokenIn: tokenIn,
            tokenOut: tokenOut,
            fee: POOL_FEE,
            recipient: skaleStrategy, // Send USDC back to SKALE strategy
            deadline: block.timestamp + DEADLINE_EXTENSION,
            amountIn: amountIn,
            amountOutMinimum: minAmountOut,
            sqrtPriceLimitX96: 0
        });

        uint256 amountOut = uniswapRouter.exactInputSingle(params);
        IERC20(tokenIn).approve(address(uniswapRouter), 0);

        emit SellExecuted(tokenIn, amountIn, amountOut, block.timestamp);

        // Bridge USDC back to SKALE
        // In production, use SKALE IMA Bridge to send USDC back
    }
}---File Content End---

---Path and files name: pmms/contracts/utils/SafeERC20.sol
---Filename: SafeERC20.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;---File Content End---

---Path and files name: pmms/contracts/utils/StrategyLib.sol
---Filename: StrategyLib.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@uniswap/v2-periphery/contracts/interfaces/IUniswapV2Router02.sol";
import "@chainlink/contracts/src/v0.8/shared/interfaces/AggregatorV3Interface.sol";

library StrategyLib {
    function swapTokens(
        address router, 
        address[] memory path, 
        uint256 amount, 
        uint256 minOut
    ) internal returns (uint256) {
        uint256 deadline = block.timestamp + 300;
        IERC20(path[0]).approve(router, amount);
        uint[] memory amountsOut = IUniswapV2Router02(router).swapExactTokensForTokens(
            amount,
            minOut,
            path,
            address(this),
            deadline
        );
        return amountsOut[amountsOut.length - 1];
    }

    function _getPrice(
        address router, 
        address tokenIn, 
        address tokenOut, 
        uint256 amountIn
    ) internal view returns (uint256) {
        address[] memory path = new address[](2);
        path[0] = tokenIn;
        path[1] = tokenOut;
        try IUniswapV2Router02(router).getAmountsOut(amountIn, path) returns (uint[] memory amounts) {
            return amounts[1];
        } catch {
            return 0;
        }
    }

    function getOnchainPrice(
        address tokenUsdFeed
    ) internal view returns (uint256 price, uint8 decimals) {
        (, int256 answer,,,) = AggregatorV3Interface(tokenUsdFeed).latestRoundData();
        decimals = AggregatorV3Interface(tokenUsdFeed).decimals();
        price = uint256(answer);
    }
}---File Content End---

---Path and files name: pmms/contracts/interfaces/IAavePool.sol
---Filename: IAavePool.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

// interface IAavePool {
//     function deposit(
//         address asset,
//         uint256 amount,
//         address onBehalfOf,
//         uint16 referralCode
//     ) external;
//     function withdraw(
//         address asset,
//         uint256 amount,
//         address to
//     ) external returns (uint256);

//     function getReserveData(
//         address asset
//     )
//         external
//         view
//         returns (
//             uint256 configuration,
//             uint128 liquidityIndex,
//             uint128 variableBorrowIndex,
//             uint128 currentLiquidityRate,
//             uint128 currentVariableBorrowRate,
//             uint128 currentStableBorrowRate,
//             uint40 lastUpdateTimestamp,
//             address aTokenAddress,
//             address stableDebtTokenAddress,
//             address variableDebtTokenAddress,
//             address interestRateStrategyAddress,
//             uint8 id
//         );

//     function borrow(
//     address asset,
//     uint256 amount,
//     uint256 interestRateMode,
//     uint16 referralCode,
//     address onBehalfOf
//     ) external;
// }

// // pmms/interfaces/IAavePool.sol
// pragma solidity ^0.8.20;

interface IAavePool {
    function flashLoanSimple(
        address receiverAddress,
        address asset,
        uint256 amount,
        bytes calldata params,
        uint16 referralCode
    ) external;

    function deposit(
        address asset,
        uint256 amount,
        address onBehalfOf,
        uint16 referralCode
    ) external;

    function borrow(
        address asset,
        uint256 amount,
        uint256 interestRateMode,
        uint16 referralCode,
        address onBehalfOf
    ) external;

    function repay(
        address asset,
        uint256 amount,
        uint256 interestRateMode,
        address onBehalfOf
    ) external returns (uint256);

    function getReserveData(address asset)
        external
        view
        returns (
            uint256 configuration,
            uint128 liquidityIndex,
            uint128 variableBorrowIndex,
            uint128 currentLiquidityRate,
            uint128 currentVariableBorrowRate,
            uint128 currentStableBorrowRate,
            uint40 lastUpdateTimestamp,
            address aTokenAddress,
            address stableDebtTokenAddress,
            address variableDebtTokenAddress,
            address interestRateStrategyAddress,
            uint8 id
        );
}---File Content End---

---Path and files name: pmms/contracts/interfaces/ICompound.sol
---Filename: ICompound.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

interface ICompound {
    function supply(address asset, uint256 amount) external returns (uint256);
    function getSupplyRate(address asset) external view returns (uint256);
    function mint(uint256 mintAmount) external returns (uint256);
    function supplyRatePerBlock() external view returns (uint256);
}---File Content End---

---Path and files name: pmms/contracts/interfaces/IUniswapV2Router02.sol
---Filename: IUniswapV2Router02.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

interface IUniswapV2Router02 {
function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts);
function swapExactTokensForTokens(
    uint amountIn,
    uint amountOutMin,
    address[] calldata path,
    address to,
    uint deadline
) external returns (uint[] memory amounts);
}---File Content End---

---Path and files name: pmms/contracts/interfaces/ILiquityTroveManager.sol
---Filename: ILiquityTroveManager.sol
---File Content Start---
// pmms/interfaces/ILiquityTroveManager.sol
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

interface ILiquityTroveManager {
    function liquidate(address borrower) external;
    function getTroveDebt(address borrower) external view returns (uint256);
    function getTroveColl(address borrower) external view returns (uint256);
}
---File Content End---

---Path and files name: pmms/contracts/interfaces/ICurvePool.sol
---Filename: ICurvePool.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

interface ICurvePool {
    function coins(uint256 i) external view returns (address);
    function get_dy(int128 i, int128 j, uint256 dx) external view returns (uint256);
    function exchange(int128 i, int128 j, uint256 dx, uint256 min_dy) external returns (uint256);
    function add_liquidity(uint256[3] memory amounts, uint256 min_mint_amount) external returns (uint256);
}---File Content End---

---Path and files name: pmms/contracts/interfaces/INftFloorOracle.sol
---Filename: INftFloorOracle.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

interface INftFloorOracle {
    function getFloorPrice(address nftContract) external view returns (uint256);
}
---File Content End---

---Path and files name: pmms/contracts/interfaces/IStrategy.sol
---Filename: IStrategy.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

interface IStrategy {
    function name() external view returns (string memory);
    function checkOpportunity(address asset, uint256 amount) external returns (uint256 profit, bytes memory executionData);
    function execute(bytes memory executionData, uint256 amount, uint256 premium) external returns (bool success, bytes memory result, uint256 finalAmount);
}---File Content End---

---Path and files name: pmms/contracts/interfaces/IRegistry.sol
---Filename: IRegistry.sol
---File Content Start---
// contracts/interfaces/IRegistry.sol
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;
interface IRegistry {
    function getAddress(string memory key) external view returns (address);
    function getNftFloorPrice(address nftContract) external view returns (uint256 floorPrice);
    function getStrategies() external view returns (address[] memory);
    function registerStrategy(string calldata name, address strategy) external;
    function deregisterStrategy(string calldata name) external;
    function getStrategy(string calldata name) external view returns (address);
    function getStrategyNames() external view returns (string[] memory);
    function setAddress(string memory name, address addr) external;
    function addStrategy(string memory name, address strategy) external;
}---File Content End---

---Path and files name: pmms/contracts/interfaces/IQuoter.sol
---Filename: IQuoter.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

interface IQuoter {
    function quoteExactInputSingle(
        address tokenIn,
        address tokenOut,
        uint24 fee,
        uint256 amountIn,
        uint160 sqrtPriceLimitX96
    ) external returns (uint256 amountOut);
}---File Content End---

---Path and files name: pmms/contracts/interfaces/ISwapRouter.sol
---Filename: ISwapRouter.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

interface ISwapRouter {
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint256 amountOutMinimum;
        uint160 sqrtPriceLimitX96;
    }

    function exactInputSingle(ExactInputSingleParams calldata params) external payable returns (uint256 amountOut);
}---File Content End---

---Path and files name: pmms/contracts/interfaces/IConvexBooster.sol
---Filename: IConvexBooster.sol
---File Content Start---
// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.20;

interface IConvexBooster {
    function deposit(uint256 _pid, uint256 _amount, bool _stake) external returns (bool);
}---File Content End---

