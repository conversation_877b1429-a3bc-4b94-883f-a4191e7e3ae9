const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying Flash Loan Arbitrage Contracts...");
  
  const [deployer] = await ethers.getSigners();
  console.log("Deploying with account:", deployer.address);
  console.log("Account balance:", (await deployer.getBalance()).toString());

  // Deploy Insurance Protocol Arbitrage
  console.log("\n📋 Deploying InsuranceProtocolArbitrage...");
  const InsuranceArbitrage = await ethers.getContractFactory("InsuranceProtocolArbitrage");
  const insuranceArbitrage = await InsuranceArbitrage.deploy();
  await insuranceArbitrage.deployed();
  console.log("✅ InsuranceProtocolArbitrage deployed to:", insuranceArbitrage.address);

  // Deploy Liquid Staking Arbitrage
  console.log("\n🥩 Deploying LiquidStakingArbitrage...");
  const LiquidStakingArbitrage = await ethers.getContractFactory("LiquidStakingArbitrage");
  const liquidStakingArbitrage = await LiquidStakingArbitrage.deploy();
  await liquidStakingArbitrage.deployed();
  console.log("✅ LiquidStakingArbitrage deployed to:", liquidStakingArbitrage.address);

  // Save deployment addresses
  const deploymentInfo = {
    network: await ethers.provider.getNetwork(),
    deployer: deployer.address,
    contracts: {
      InsuranceProtocolArbitrage: insuranceArbitrage.address,
      LiquidStakingArbitrage: liquidStakingArbitrage.address
    },
    deploymentTime: new Date().toISOString()
  };

  const fs = require('fs');
  const path = require('path');
  
  const deploymentDir = path.join(__dirname, '..', 'deployments');
  if (!fs.existsSync(deploymentDir)) {
    fs.mkdirSync(deploymentDir, { recursive: true });
  }
  
  const deploymentFile = path.join(deploymentDir, `deployment-${deploymentInfo.network.name}.json`);
  fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
  
  console.log("\n📄 Deployment info saved to:", deploymentFile);
  
  console.log("\n🎯 Next Steps:");
  console.log("1. Verify contracts on Etherscan");
  console.log("2. Test with small amounts first");
  console.log("3. Monitor for profitable opportunities");
  console.log("4. Scale gradually after confirming profitability");
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(error);
    process.exit(1);
  });
