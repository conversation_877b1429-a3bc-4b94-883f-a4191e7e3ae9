import { ethers } from 'hardhat';

// REAL TOKEN GENERATOR CONTRACT
const REAL_TOKEN_CONTRACT = '******************************************';

async function testFlashLoanOnly() {
  console.log('\n🔍 TESTING FLASH LOAN ONLY - MINIMAL TEST');
  console.log('🎯 ISOLATING THE EXACT ISSUE');
  console.log('🔧 TESTING JUST THE FLASH LOAN MECHANISM');
  console.log('=' .repeat(80));

  const [executor] = await ethers.getSigners();
  const network = await ethers.provider.getNetwork();
  
  console.log(`Network: ${network.name} (${network.chainId})`);
  console.log(`Executor: ${executor.address}`);
  console.log(`Contract: ${REAL_TOKEN_CONTRACT}`);
  console.log(`Balance: ${ethers.utils.formatEther(await executor.getBalance())} POL`);

  // Connect to contract
  const contract = await ethers.getContractAt('DualStrategyFlashProfit', REAL_TOKEN_CONTRACT);
  
  console.log('\n🔍 STEP 1: CHECK CONTRACT DEPLOYMENT');
  try {
    const code = await ethers.provider.getCode(REAL_TOKEN_CONTRACT);
    console.log(`✅ Contract deployed: ${code.length > 2 ? 'YES' : 'NO'}`);
    console.log(`   Code length: ${code.length} bytes`);
  } catch (error) {
    console.log(`❌ Contract check failed: ${error.message}`);
    return;
  }

  console.log('\n🔍 STEP 2: CHECK AAVE POOL CONNECTION');
  try {
    // Check if we can read from Aave pool
    const aavePool = new ethers.Contract(
      '******************************************', // Aave V3 Pool
      ['function getReserveData(address asset) view returns (tuple(uint256,uint128,uint128,uint128,uint128,uint128,uint40,uint16,address,address,address,address,uint128))'],
      ethers.provider
    );
    
    const reserveData = await aavePool.getReserveData('******************************************'); // USDC
    console.log(`✅ Aave pool accessible: YES`);
    console.log(`   USDC reserve available: ${reserveData[0].toString() !== '0' ? 'YES' : 'NO'}`);
  } catch (error) {
    console.log(`❌ Aave pool check failed: ${error.message.substring(0, 50)}...`);
  }

  console.log('\n🔍 STEP 3: CHECK USDC CONTRACT');
  try {
    const usdcContract = new ethers.Contract(
      '******************************************',
      ['function totalSupply() view returns (uint256)', 'function decimals() view returns (uint8)'],
      ethers.provider
    );
    
    const totalSupply = await usdcContract.totalSupply();
    const decimals = await usdcContract.decimals();
    console.log(`✅ USDC contract accessible: YES`);
    console.log(`   Total supply: ${ethers.utils.formatUnits(totalSupply, decimals)} USDC`);
    console.log(`   Decimals: ${decimals}`);
  } catch (error) {
    console.log(`❌ USDC contract check failed: ${error.message.substring(0, 50)}...`);
  }

  console.log('\n🔍 STEP 4: CHECK QUICKSWAP ROUTER');
  try {
    const quickswapRouter = new ethers.Contract(
      '******************************************',
      ['function factory() view returns (address)', 'function WETH() view returns (address)'],
      ethers.provider
    );
    
    const factory = await quickswapRouter.factory();
    const weth = await quickswapRouter.WETH();
    console.log(`✅ QuickSwap router accessible: YES`);
    console.log(`   Factory: ${factory}`);
    console.log(`   WETH: ${weth}`);
  } catch (error) {
    console.log(`❌ QuickSwap router check failed: ${error.message.substring(0, 50)}...`);
  }

  console.log('\n🔍 STEP 5: TEST MINIMAL FLASH LOAN');
  try {
    console.log('🚀 Attempting minimal flash loan (1000 USDC)...');
    
    const liquidityParams = {
      pool: '******************************************',
      token0: '******************************************', // USDC
      token1: '******************************************', // WETH
      fee: 3000,
      tickLower: -276320,
      tickUpper: -276260,
      amount0: ethers.utils.parseUnits('1000', 6), // 1k USDC (small amount)
      amount1: ethers.utils.parseEther('0.3'), // 0.3 WETH
      minProfit: ethers.utils.parseUnits('0.01', 6) // $0.01 minimum
    };

    // Try to estimate gas first
    try {
      const gasEstimate = await contract.estimateGas.executeLiquidityProvision(liquidityParams, {
        maxPriorityFeePerGas: ethers.utils.parseUnits('30', 'gwei'),
        maxFeePerGas: ethers.utils.parseUnits('50', 'gwei'),
        gasLimit: 1000000
      });
      console.log(`✅ Gas estimation successful: ${gasEstimate.toLocaleString()}`);
    } catch (gasError) {
      console.log(`❌ Gas estimation failed: ${gasError.message.substring(0, 100)}...`);
      
      // Try to get more specific error
      try {
        await contract.callStatic.executeLiquidityProvision(liquidityParams);
        console.log(`✅ Static call successful`);
      } catch (staticError) {
        console.log(`❌ Static call failed: ${staticError.message.substring(0, 100)}...`);
        
        // Check if it's a revert reason
        if (staticError.message.includes('revert')) {
          const revertReason = staticError.message.split('revert ')[1]?.split('"')[0];
          console.log(`🚨 REVERT REASON: ${revertReason}`);
        }
      }
    }

  } catch (error) {
    console.log(`❌ Flash loan test failed: ${error.message.substring(0, 100)}...`);
  }

  console.log('\n📊 DIAGNOSTIC SUMMARY:');
  console.log('🔍 This test helps identify the exact failure point');
  console.log('💡 Check the results above to see where the issue occurs');
}

// Execute diagnostic test
if (require.main === module) {
  testFlashLoanOnly()
    .then(() => {
      console.log('\n🎉 DIAGNOSTIC TEST COMPLETED!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 DIAGNOSTIC TEST FAILED:', error);
      process.exit(1);
    });
}

export { testFlashLoanOnly };
