// 🔥 BASIC TEST - Verify Solana Connection
console.log('🔥 TESTING DIRECT BLOCKCHAIN ACCESS');
console.log('Starting basic connection test...');

try {
  // Test basic import
  const { Connection, PublicKey } = await import('@solana/web3.js');
  console.log('✅ Solana web3.js imported successfully');
  
  // Test connection
  const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');
  console.log('✅ Connection created');
  
  // Test basic RPC call
  const version = await connection.getVersion();
  console.log('✅ RPC call successful:', version);
  
  // Test Meteora program
  const METEORA_DLMM_PROGRAM = new PublicKey('LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo');
  console.log('✅ Meteora DLMM Program ID:', METEORA_DLMM_PROGRAM.toString());
  
  // Test getting program accounts (limited)
  console.log('🔍 Testing program account access...');
  const accounts = await connection.getProgramAccounts(METEORA_DLMM_PROGRAM, {
    dataSlice: { offset: 0, length: 0 },
    limit: 10 // Limit to 10 for testing
  });
  
  console.log(`📊 SUCCESS: Found ${accounts.length} Meteora DLMM accounts`);
  
  if (accounts.length > 0) {
    console.log('🎯 Sample accounts:');
    accounts.slice(0, 5).forEach((account, index) => {
      console.log(`   ${index + 1}. ${account.pubkey.toString()}`);
    });
  }
  
  console.log('\n🎉 DIRECT BLOCKCHAIN ACCESS WORKING!');
  console.log('✅ We can read Meteora DLMM data directly from blockchain');
  console.log('💰 Cost: $0 deployment + minimal RPC costs');
  console.log('🧠 Next: Build full intelligence system');
  
} catch (error) {
  console.error('❌ Error:', error.message);
  console.error('Stack:', error.stack);
}
