{"name": "pmms-auto-ui", "version": "1.0.0", "description": "Profit Maximizer Modular System (PMMS) - Auto Strategy Execution UI", "private": true, "homepage": "https://kmafutah.github.io/SmartContracts/", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "predeploy": "npm run build", "deploy0": "gh-pages -d dist --branch=gh-pages --message='Deploy '\"$(date)\"", "deploy": "gh-pages -d dist", "build:abis": "bash scripts/build-abis.sh"}, "dependencies": {"axios": "^1.10.0", "ethers": "^6.7.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.14", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "gh-pages": "^6.3.0", "globals": "^16.0.0", "postcss": "^8.4.21", "tailwindcss": "^3.3.2", "vite": "^6.3.5"}}