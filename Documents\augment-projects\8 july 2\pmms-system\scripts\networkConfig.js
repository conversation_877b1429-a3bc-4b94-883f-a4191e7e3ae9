const { ethers } = require("ethers");

const ZeroAddress = ethers.ZeroAddress;

const networkConfig = {
  polygon: {
    USDC: "******************************************",
    DAI: "******************************************",
    USDT: "******************************************",
    WETH: "******************************************",
    STETH: ZeroAddress,
    CURVE_3POOL: "******************************************",
    CURVE_3POOL_TOKEN: "******************************************",
    AAVE_LENDING_POOL: "******************************************",
    UNISWAP_V3: ZeroAddress, // not on Polygon PoS (use Sushi/Quick)
    UNISWAP_V3_QUOTER: ZeroAddress,
    CONVEX: ZeroAddress,
    SKALE_IMA_BRIDGE: ZeroAddress,
    GAS_ORACLE: ZeroAddress,
  },

  polygon_zkevm: {
    USDC: "******************************************",
    DAI: ZeroAddress,
    USDT: ZeroAddress,
    WETH: "******************************************",
    STETH: ZeroAddress,
    CURVE_3POOL: ZeroAddress,
    CURVE_3POOL_TOKEN: ZeroAddress,
    AAVE_LENDING_POOL: "******************************************",
    UNISWAP_V3: "******************************************",
    UNISWAP_V3_QUOTER: "******************************************",
    CONVEX: ZeroAddress,
    SKALE_IMA_BRIDGE: ZeroAddress,
    GAS_ORACLE: ZeroAddress,
  },

  skale: {},
  skale_testnet: {},
};

function getNetworkConfig(networkName) {
  const lowerName = networkName.toLowerCase();

  // default to mock for SKALE and unknowns
  if (lowerName.includes("skale") || lowerName.includes("mock")) {
    return {};
  }

  return networkConfig[lowerName] || {};
}

module.exports = {
  getNetworkConfig,
};
