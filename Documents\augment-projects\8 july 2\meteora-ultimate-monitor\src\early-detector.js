// 🚀 EARLY DETECTOR - Catch honey pools BEFORE they explode
import { CONFIG } from './config.js';

class EarlyDetector {
  constructor() {
    this.historicalData = new Map(); // Store historical snapshots
    this.trendAnalysis = new Map();  // Track trends over time
    this.velocityTracking = new Map(); // Track rate of change
    this.smartMoneySignals = new Map(); // Track large movements
    this.emergingPools = new Map();   // Pools showing early signs
  }

  // 🎯 ANALYZE POOL FOR EARLY SIGNALS
  analyzeEarlySignals(currentPool, previousData = null) {
    const poolId = currentPool.address || currentPool.id;
    
    // Store current snapshot
    this.storeSnapshot(poolId, currentPool);
    
    // Get historical data for comparison
    const history = this.historicalData.get(poolId) || [];
    
    if (history.length < 2) {
      // Not enough data for trend analysis yet
      return {
        isEmerging: false,
        signals: [],
        confidence: 0,
        timeToExplosion: null
      };
    }
    
    const signals = this.detectEarlySignals(poolId, currentPool, history);
    const confidence = this.calculateConfidence(signals);
    const timeToExplosion = this.predictExplosionTime(signals, history);
    
    return {
      poolId,
      poolName: currentPool.name || currentPool.pool_name,
      isEmerging: confidence > 60, // 60%+ confidence threshold
      signals,
      confidence,
      timeToExplosion,
      currentMetrics: this.extractCurrentMetrics(currentPool),
      trend: this.analyzeTrend(poolId, history)
    };
  }

  // 📊 DETECT EARLY SIGNALS
  detectEarlySignals(poolId, current, history) {
    const signals = [];
    const latest = history[history.length - 1];
    const previous = history[history.length - 2];
    
    // 1. FEE VELOCITY ACCELERATION
    const feeVelocity = this.calculateFeeVelocity(current, latest, previous);
    if (feeVelocity.acceleration > 50) { // 50%+ acceleration
      signals.push({
        type: 'FEE_ACCELERATION',
        strength: Math.min(feeVelocity.acceleration / 100, 1),
        message: `Fee generation accelerating by ${feeVelocity.acceleration.toFixed(1)}%`,
        priority: 'HIGH'
      });
    }
    
    // 2. VOLUME MOMENTUM BUILDING
    const volumeMomentum = this.calculateVolumeMomentum(current, history);
    if (volumeMomentum.isBuilding) {
      signals.push({
        type: 'VOLUME_MOMENTUM',
        strength: volumeMomentum.strength,
        message: `Volume momentum building: ${volumeMomentum.change.toFixed(1)}% increase`,
        priority: 'MEDIUM'
      });
    }
    
    // 3. LIQUIDITY EFFICIENCY IMPROVING
    const efficiencyTrend = this.calculateEfficiencyTrend(current, history);
    if (efficiencyTrend.improving) {
      signals.push({
        type: 'EFFICIENCY_IMPROVEMENT',
        strength: efficiencyTrend.strength,
        message: `Liquidity efficiency improving by ${efficiencyTrend.improvement.toFixed(1)}%`,
        priority: 'MEDIUM'
      });
    }
    
    // 4. SMART MONEY DETECTION
    const smartMoney = this.detectSmartMoney(current, history);
    if (smartMoney.detected) {
      signals.push({
        type: 'SMART_MONEY',
        strength: smartMoney.strength,
        message: `Large transactions detected: ${smartMoney.count} whale moves`,
        priority: 'HIGH'
      });
    }
    
    // 5. CONCENTRATION BUILDING
    const concentration = this.detectConcentrationBuilding(current, history);
    if (concentration.building) {
      signals.push({
        type: 'CONCENTRATION_BUILDING',
        strength: concentration.strength,
        message: `Fee concentration building: ${concentration.rate.toFixed(2)}% fee/TVL ratio`,
        priority: 'HIGH'
      });
    }
    
    // 6. VOLATILITY SPIKE INCOMING
    const volatilitySignal = this.detectVolatilitySpike(current, history);
    if (volatilitySignal.incoming) {
      signals.push({
        type: 'VOLATILITY_SPIKE',
        strength: volatilitySignal.strength,
        message: `Volatility spike incoming: ${volatilitySignal.indicator}`,
        priority: 'MEDIUM'
      });
    }
    
    return signals;
  }

  // ⚡ CALCULATE FEE VELOCITY
  calculateFeeVelocity(current, latest, previous) {
    const currentFees = parseFloat(current.fee_24h || current.fees_24h || 0);
    const latestFees = parseFloat(latest.fees || 0);
    const previousFees = parseFloat(previous.fees || 0);
    
    const currentVelocity = ((currentFees - latestFees) / Math.max(latestFees, 1)) * 100;
    const previousVelocity = ((latestFees - previousFees) / Math.max(previousFees, 1)) * 100;
    
    const acceleration = currentVelocity - previousVelocity;
    
    return {
      currentVelocity,
      previousVelocity,
      acceleration,
      isAccelerating: acceleration > 0
    };
  }

  // 📈 CALCULATE VOLUME MOMENTUM
  calculateVolumeMomentum(current, history) {
    const currentVolume = parseFloat(current.volume_24h || 0);
    const recentHistory = history.slice(-5); // Last 5 data points
    
    if (recentHistory.length < 3) return { isBuilding: false, strength: 0 };
    
    const volumes = recentHistory.map(h => parseFloat(h.volume || 0));
    volumes.push(currentVolume);
    
    // Calculate momentum using linear regression slope
    const momentum = this.calculateSlope(volumes);
    const avgVolume = volumes.reduce((a, b) => a + b, 0) / volumes.length;
    const change = ((currentVolume - avgVolume) / Math.max(avgVolume, 1)) * 100;
    
    return {
      isBuilding: momentum > 0 && change > 20, // 20%+ above average
      strength: Math.min(Math.abs(change) / 100, 1),
      change,
      momentum
    };
  }

  // 🎯 CALCULATE EFFICIENCY TREND
  calculateEfficiencyTrend(current, history) {
    const currentTvl = parseFloat(current.tvl || 0);
    const currentVolume = parseFloat(current.volume_24h || 0);
    const currentFees = parseFloat(current.fee_24h || current.fees_24h || 0);
    
    const currentEfficiency = currentTvl > 0 ? (currentFees / currentTvl) * 100 : 0;
    
    const recentHistory = history.slice(-3);
    if (recentHistory.length < 2) return { improving: false, strength: 0 };
    
    const pastEfficiencies = recentHistory.map(h => {
      const tvl = parseFloat(h.tvl || 0);
      const fees = parseFloat(h.fees || 0);
      return tvl > 0 ? (fees / tvl) * 100 : 0;
    });
    
    const avgPastEfficiency = pastEfficiencies.reduce((a, b) => a + b, 0) / pastEfficiencies.length;
    const improvement = ((currentEfficiency - avgPastEfficiency) / Math.max(avgPastEfficiency, 0.01)) * 100;
    
    return {
      improving: improvement > 15, // 15%+ improvement
      strength: Math.min(Math.abs(improvement) / 50, 1),
      improvement,
      currentEfficiency,
      avgPastEfficiency
    };
  }

  // 🐋 DETECT SMART MONEY
  detectSmartMoney(current, history) {
    const currentVolume = parseFloat(current.volume_24h || 0);
    const currentTvl = parseFloat(current.tvl || 0);
    
    // Look for sudden large volume spikes relative to TVL
    const volumeToTvlRatio = currentTvl > 0 ? currentVolume / currentTvl : 0;
    
    // Check for unusual volume patterns
    const recentVolumes = history.slice(-3).map(h => parseFloat(h.volume || 0));
    const avgRecentVolume = recentVolumes.reduce((a, b) => a + b, 0) / recentVolumes.length;
    
    const volumeSpike = avgRecentVolume > 0 ? (currentVolume / avgRecentVolume) : 1;
    
    // Smart money indicators
    const largeVolumeSpike = volumeSpike > 3; // 3x volume spike
    const highVolumeToTvl = volumeToTvlRatio > 5; // High turnover
    const sustainedActivity = recentVolumes.every(v => v > avgRecentVolume * 0.8);
    
    let whaleCount = 0;
    if (largeVolumeSpike) whaleCount++;
    if (highVolumeToTvl) whaleCount++;
    if (sustainedActivity) whaleCount++;
    
    return {
      detected: whaleCount >= 2,
      strength: whaleCount / 3,
      count: whaleCount,
      volumeSpike,
      volumeToTvlRatio
    };
  }

  // 🍯 DETECT CONCENTRATION BUILDING
  detectConcentrationBuilding(current, history) {
    const currentTvl = parseFloat(current.tvl || 0);
    const currentFees = parseFloat(current.fee_24h || current.fees_24h || 0);
    const currentRatio = currentTvl > 0 ? (currentFees / currentTvl) * 100 : 0;
    
    const recentRatios = history.slice(-5).map(h => {
      const tvl = parseFloat(h.tvl || 0);
      const fees = parseFloat(h.fees || 0);
      return tvl > 0 ? (fees / tvl) * 100 : 0;
    });
    
    if (recentRatios.length < 3) return { building: false, strength: 0 };
    
    // Check if concentration is consistently increasing
    const trend = this.calculateSlope(recentRatios.concat([currentRatio]));
    const avgRatio = recentRatios.reduce((a, b) => a + b, 0) / recentRatios.length;
    const improvement = ((currentRatio - avgRatio) / Math.max(avgRatio, 0.01)) * 100;
    
    return {
      building: trend > 0 && improvement > 25, // 25%+ improvement with positive trend
      strength: Math.min(Math.abs(improvement) / 100, 1),
      rate: currentRatio,
      trend,
      improvement
    };
  }

  // 🌪️ DETECT VOLATILITY SPIKE
  detectVolatilitySpike(current, history) {
    const currentVolume = parseFloat(current.volume_24h || 0);
    const currentTvl = parseFloat(current.tvl || 0);
    
    const recentVolumes = history.slice(-5).map(h => parseFloat(h.volume || 0));
    
    if (recentVolumes.length < 3) return { incoming: false, strength: 0 };
    
    // Calculate volume volatility
    const avgVolume = recentVolumes.reduce((a, b) => a + b, 0) / recentVolumes.length;
    const variance = recentVolumes.reduce((sum, vol) => sum + Math.pow(vol - avgVolume, 2), 0) / recentVolumes.length;
    const volatility = Math.sqrt(variance);
    
    const currentDeviation = Math.abs(currentVolume - avgVolume);
    const volatilityRatio = volatility > 0 ? currentDeviation / volatility : 0;
    
    return {
      incoming: volatilityRatio > 2, // 2+ standard deviations
      strength: Math.min(volatilityRatio / 3, 1),
      indicator: `${volatilityRatio.toFixed(1)}σ deviation`,
      volatility,
      currentDeviation
    };
  }

  // 📊 CALCULATE SLOPE (for trend analysis)
  calculateSlope(values) {
    const n = values.length;
    if (n < 2) return 0;
    
    const xSum = (n * (n - 1)) / 2;
    const ySum = values.reduce((a, b) => a + b, 0);
    const xySum = values.reduce((sum, y, x) => sum + x * y, 0);
    const xSquareSum = (n * (n - 1) * (2 * n - 1)) / 6;
    
    const slope = (n * xySum - xSum * ySum) / (n * xSquareSum - xSum * xSum);
    return slope;
  }

  // 🎯 CALCULATE CONFIDENCE
  calculateConfidence(signals) {
    if (signals.length === 0) return 0;
    
    const weights = {
      'FEE_ACCELERATION': 30,
      'SMART_MONEY': 25,
      'CONCENTRATION_BUILDING': 20,
      'VOLUME_MOMENTUM': 15,
      'EFFICIENCY_IMPROVEMENT': 10,
      'VOLATILITY_SPIKE': 10
    };
    
    let totalWeight = 0;
    let weightedScore = 0;
    
    signals.forEach(signal => {
      const weight = weights[signal.type] || 5;
      totalWeight += weight;
      weightedScore += weight * signal.strength;
    });
    
    return totalWeight > 0 ? (weightedScore / totalWeight) * 100 : 0;
  }

  // ⏰ PREDICT EXPLOSION TIME
  predictExplosionTime(signals, history) {
    if (signals.length === 0) return null;
    
    // Simple prediction based on signal strength and momentum
    const avgStrength = signals.reduce((sum, s) => sum + s.strength, 0) / signals.length;
    const highPrioritySignals = signals.filter(s => s.priority === 'HIGH').length;
    
    if (avgStrength > 0.8 && highPrioritySignals >= 2) {
      return '5-15 minutes'; // Very soon
    } else if (avgStrength > 0.6 && highPrioritySignals >= 1) {
      return '15-30 minutes'; // Soon
    } else if (avgStrength > 0.4) {
      return '30-60 minutes'; // Moderate timeframe
    }
    
    return '1-2 hours'; // Longer timeframe
  }

  // 💾 STORE SNAPSHOT
  storeSnapshot(poolId, poolData) {
    if (!this.historicalData.has(poolId)) {
      this.historicalData.set(poolId, []);
    }
    
    const history = this.historicalData.get(poolId);
    const snapshot = {
      timestamp: Date.now(),
      tvl: parseFloat(poolData.tvl || 0),
      volume: parseFloat(poolData.volume_24h || 0),
      fees: parseFloat(poolData.fee_24h || poolData.fees_24h || 0),
      name: poolData.name || poolData.pool_name
    };
    
    history.push(snapshot);
    
    // Keep only last 50 snapshots per pool
    if (history.length > 50) {
      history.shift();
    }
    
    this.historicalData.set(poolId, history);
  }

  // 📊 EXTRACT CURRENT METRICS
  extractCurrentMetrics(pool) {
    return {
      tvl: parseFloat(pool.tvl || 0),
      volume24h: parseFloat(pool.volume_24h || 0),
      fees24h: parseFloat(pool.fee_24h || pool.fees_24h || 0),
      feeToTvlRatio: this.calculateFeeToTvlRatio(pool),
      volumeVelocity: this.calculateVolumeVelocity(pool)
    };
  }

  // 📈 ANALYZE TREND
  analyzeTrend(poolId, history) {
    if (history.length < 3) return 'INSUFFICIENT_DATA';
    
    const recentFees = history.slice(-5).map(h => h.fees);
    const slope = this.calculateSlope(recentFees);
    
    if (slope > 0.1) return 'STRONG_UPTREND';
    if (slope > 0.05) return 'UPTREND';
    if (slope > -0.05) return 'SIDEWAYS';
    if (slope > -0.1) return 'DOWNTREND';
    return 'STRONG_DOWNTREND';
  }

  // Helper methods
  calculateFeeToTvlRatio(pool) {
    const tvl = parseFloat(pool.tvl || 0);
    const fees = parseFloat(pool.fee_24h || pool.fees_24h || 0);
    return tvl > 0 ? (fees / tvl) * 100 : 0;
  }

  calculateVolumeVelocity(pool) {
    const tvl = parseFloat(pool.tvl || 0);
    const volume = parseFloat(pool.volume_24h || 0);
    return tvl > 0 ? volume / tvl : 0;
  }
}

export default EarlyDetector;
