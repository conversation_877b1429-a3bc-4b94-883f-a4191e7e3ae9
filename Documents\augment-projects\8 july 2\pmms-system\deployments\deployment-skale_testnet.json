{"network": "skale_testnet", "timestamp": "2025-06-04T15:14:54.864Z", "Registry": "******************************************", "StrategyExecutor": "******************************************", "FlashloanExecutor": "******************************************", "PMMS": "******************************************", "Mocks": {"USDC": "******************************************", "USDT": "******************************************", "DAI": "******************************************", "WETH": "******************************************", "STETH": "******************************************", "CURVE_3POOL": "******************************************", "UNISWAP_V2": "******************************************", "UNISWAP_V3": "******************************************", "UNISWAP_V3_QUOTER": "******************************************", "CONVEX": "******************************************", "CURVE_3POOL_TOKEN": "******************************************", "AAVE_LENDING_POOL": "******************************************", "SKALE_IMA_BRIDGE": "******************************************", "GAS_ORACLE": "******************************************"}, "Strategies": {"StrategyAaveLiquidation": "******************************************", "StrategyBridgingLatencyArbitrage": "******************************************", "StrategyCrossDexLendingArbitrage": "******************************************", "StrategyDexArbitrage": "******************************************", "StrategyFlashloanGasArbitrage": "0xD59A374a28CB3cC85B155bd78f13C6b199794d73", "StrategyFlashMintArbitrage": "0xF586d46619B0373a716c6200a9F4B9500C34723C", "StrategyGasRefundArbitrage": "0xf9bC4ed394c09d110ef9cdbcc7f707024De5bD03", "StrategyGovernanceArbitrage": "0x3162AED6B9eFE53034D96C1855e8Df079b679642", "StrategyLPBurnArbitrage": "0x9952674E3513Fa5505Ec2336b647C6C83Cf22518", "StrategyMEVCapture": "0x9298710367E7193624Fe2eF7Ebd6942Bd0279864", "StrategyNFTCollateralLiquidation": "0x565F6c49d6F72F156A1D94041E2EF65C02E28Dd8", "StrategyNFTFloorArbitrage": "0xC164a7293f1cEDE3Fd3AFe98850a3D19e3F88a03", "StrategyOracleLagArbitrage": "0xFe04DC013afD9DB074c746Cd0941628484Ce3539", "StrategyRebaseTokenArbitrage": "0xd812Ee502928a3546e07c488aD07ba60A07a266B", "StrategyStablecoinMetaProtocolArbitrage": "0xCFB509432fB1A3995514897d3D9DeB51d0DBcf7d", "StrategyStablecoinPegArbitrage": "0x8dF8A8c1f4F01f2Fd78E7D598e65c83Bd4e97D64", "StrategyStakingTokenArbitrage": "0x4D8C904792f2bA0b3C82197D377ae297cBA5E776", "StrategyTriangularArbitrage": "0x1FAf21c082b165094ccE40a30e0b697a9EeE86Fa", "StrategyYieldLoop": "0x7AA1Db3F049786514c19ECcD48c2747E55602fe8"}}