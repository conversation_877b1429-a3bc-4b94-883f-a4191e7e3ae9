// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * 🚀 FIXED BALANCE ARBITRAGE
 * Fix the exact balance issue found in debugging
 */

interface IERC20 {
    function transfer(address to, uint256 amount) external returns (bool);
    function balanceOf(address account) external view returns (uint256);
    function approve(address spender, uint256 amount) external returns (bool);
}

interface IBalancerVault {
    function flashLoan(address recipient, address[] memory tokens, uint256[] memory amounts, bytes memory userData) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(address[] memory tokens, uint256[] memory amounts, uint256[] memory feeAmounts, bytes memory userData) external;
}

interface IUniswapV2Router {
    function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts);
    function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts);
}

contract FixedBalanceArbitrage is IFlashLoanRecipient {
    
    IERC20 public constant USDC = IERC20(******************************************);
    IERC20 public constant WETH = IERC20(******************************************);
    IBalancerVault public constant BALANCER = IBalancerVault(******************************************);
    
    // Use QuickSwap (most reliable)
    IUniswapV2Router public constant QUICKSWAP = IUniswapV2Router(******************************************);
    
    address public constant PROFIT_WALLET = ******************************************;
    uint256 public constant FLASH_AMOUNT = 1000e6; // $1K USDC (CONFIRMED WORKING)
    uint256 public constant MAX_GAS_PRICE = 100e9; // 100 gwei max
    
    uint256 public lastProfit;
    bool public lastSuccess;
    uint256 public totalExecutions;
    
    event FlashLoanResult(uint256 profit, bool success, uint256 execution);
    event DebugStep(string step, uint256 value);
    event BalanceCheck(string step, uint256 balance);
    event ArbitrageExecuted(address tokenIn, address tokenOut, uint256 amountIn, uint256 profit, string strategy);
    
    /**
     * 🚀 EXECUTE FIXED BALANCE ARBITRAGE
     */
    function executeFixedBalanceArbitrage() external {
        require(tx.gasprice <= MAX_GAS_PRICE, "Gas price too high");
        
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        
        tokens[0] = address(USDC);
        amounts[0] = FLASH_AMOUNT;
        
        emit DebugStep("Starting fixed balance arbitrage", FLASH_AMOUNT);
        BALANCER.flashLoan(address(this), tokens, amounts, "");
    }
    
    /**
     * 🎯 FLASH LOAN CALLBACK - FIXED BALANCE VERSION
     */
    function receiveFlashLoan(
        address[] memory,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory
    ) external override {
        require(msg.sender == address(BALANCER), "Only Balancer");
        
        uint256 flashAmount = amounts[0];
        uint256 initialBalance = USDC.balanceOf(address(this));
        
        emit DebugStep("Flash loan received", flashAmount);
        emit BalanceCheck("Initial balance", initialBalance);
        
        require(initialBalance >= flashAmount, "Flash loan not received");
        require(feeAmounts[0] == 0, "Expected 0% fee");
        
        // FIXED: Use only available balance for arbitrage
        executeFixedArbitrageStrategy(initialBalance);
        
        // Check balance before repay
        uint256 balanceBeforeRepay = USDC.balanceOf(address(this));
        emit BalanceCheck("Before repay", balanceBeforeRepay);
        
        // Repay flash loan
        USDC.transfer(address(BALANCER), flashAmount);
        emit DebugStep("Flash loan repaid", flashAmount);
        
        // Extract profit
        uint256 finalBalance = USDC.balanceOf(address(this));
        totalExecutions++;
        
        if (finalBalance > 0) {
            lastProfit = finalBalance;
            USDC.transfer(PROFIT_WALLET, finalBalance);
            lastSuccess = true;
        } else {
            lastProfit = 0;
            lastSuccess = true;
        }
        
        emit FlashLoanResult(lastProfit, lastSuccess, totalExecutions);
        emit DebugStep("Fixed arbitrage completed", finalBalance);
    }
    
    /**
     * 💰 FIXED ARBITRAGE STRATEGY
     */
    function executeFixedArbitrageStrategy(uint256 availableBalance) internal {
        emit DebugStep("Starting fixed strategy", availableBalance);
        emit BalanceCheck("Available for arbitrage", availableBalance);
        
        // FIXED: Use only 80% of available balance (20% buffer for fees/slippage)
        uint256 safeAmount = (availableBalance * 80) / 100;
        emit DebugStep("Safe amount for arbitrage", safeAmount);
        
        if (safeAmount < 100e6) { // Minimum $100
            emit DebugStep("Amount too small for arbitrage", safeAmount);
            return;
        }
        
        // Test simple USDC → WETH → USDC arbitrage
        try this.executeFixedSwapTest(safeAmount) {
            emit DebugStep("Fixed swap test completed", 1);
        } catch {
            emit DebugStep("Fixed swap test failed", 0);
        }
    }
    
    /**
     * 🔄 EXECUTE FIXED SWAP TEST (EXTERNAL FOR TRY-CATCH)
     */
    function executeFixedSwapTest(uint256 amount) external {
        require(msg.sender == address(this), "Internal only");
        
        emit DebugStep("Fixed swap test starting", amount);
        
        uint256 balanceBefore = USDC.balanceOf(address(this));
        emit BalanceCheck("Before swap test", balanceBefore);
        
        // Verify we have enough balance
        require(balanceBefore >= amount, "Insufficient balance for swap");
        
        // Step 1: USDC → WETH
        address[] memory path = new address[](2);
        path[0] = address(USDC);
        path[1] = address(WETH);
        
        // Check price first
        uint256[] memory amountsOut = QUICKSWAP.getAmountsOut(amount, path);
        uint256 expectedWeth = amountsOut[1];
        emit DebugStep("Expected WETH output", expectedWeth);
        
        // Approve and swap
        USDC.approve(address(QUICKSWAP), amount);
        emit DebugStep("USDC approved for QuickSwap", amount);
        
        uint256[] memory amounts1 = QUICKSWAP.swapExactTokensForTokens(
            amount,
            (expectedWeth * 95) / 100, // 5% slippage
            path,
            address(this),
            block.timestamp + 300
        );
        
        uint256 wethReceived = amounts1[1];
        emit DebugStep("WETH received", wethReceived);
        
        uint256 usdcAfterFirst = USDC.balanceOf(address(this));
        uint256 wethBalance = WETH.balanceOf(address(this));
        emit BalanceCheck("USDC after first swap", usdcAfterFirst);
        emit BalanceCheck("WETH balance", wethBalance);
        
        // Step 2: WETH → USDC
        address[] memory reversePath = new address[](2);
        reversePath[0] = address(WETH);
        reversePath[1] = address(USDC);
        
        // Check reverse price
        uint256[] memory reverseAmountsOut = QUICKSWAP.getAmountsOut(wethReceived, reversePath);
        uint256 expectedUsdc = reverseAmountsOut[1];
        emit DebugStep("Expected USDC output", expectedUsdc);
        
        // Approve and swap back
        WETH.approve(address(QUICKSWAP), wethReceived);
        emit DebugStep("WETH approved for QuickSwap", wethReceived);
        
        uint256[] memory amounts2 = QUICKSWAP.swapExactTokensForTokens(
            wethReceived,
            (expectedUsdc * 95) / 100, // 5% slippage
            reversePath,
            address(this),
            block.timestamp + 300
        );
        
        uint256 usdcFinal = USDC.balanceOf(address(this));
        emit BalanceCheck("USDC final", usdcFinal);
        
        // Calculate result
        if (usdcFinal > balanceBefore) {
            uint256 profit = usdcFinal - balanceBefore;
            emit DebugStep("Arbitrage profit", profit);
            emit ArbitrageExecuted(address(USDC), address(WETH), amount, profit, "Fixed-USDC-WETH");
        } else if (balanceBefore > usdcFinal) {
            uint256 loss = balanceBefore - usdcFinal;
            emit DebugStep("Arbitrage loss", loss);
        } else {
            emit DebugStep("Arbitrage break-even", 0);
        }
        
        emit DebugStep("Fixed swap test completed", usdcFinal);
    }
    
    /**
     * 📊 VIEW FUNCTIONS
     */
    function getResults() external view returns (uint256 profit, bool success, uint256 executions) {
        return (lastProfit, lastSuccess, totalExecutions);
    }
    
    function getCurrentBalance() external view returns (uint256) {
        return USDC.balanceOf(address(this));
    }
    
    /**
     * 🚨 EMERGENCY FUNCTIONS
     */
    function emergencyWithdraw() external {
        require(msg.sender == PROFIT_WALLET, "Only profit wallet");
        
        uint256 usdcBalance = USDC.balanceOf(address(this));
        if (usdcBalance > 0) USDC.transfer(PROFIT_WALLET, usdcBalance);
        
        uint256 wethBalance = WETH.balanceOf(address(this));
        if (wethBalance > 0) WETH.transfer(PROFIT_WALLET, wethBalance);
    }
}
