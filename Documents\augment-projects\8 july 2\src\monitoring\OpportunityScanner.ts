import { JsonRpcProvider } from 'ethers';
import { ChainId } from '../types';

export class OpportunityScanner {
  private provider: JsonRpcProvider;
  private providers: Map<ChainId, JsonRpcProvider> = new Map();

  constructor(rpcUrl: string) {
    this.provider = new JsonRpcProvider(rpcUrl);
  }

  public addProvider(chainId: ChainId, rpcUrl: string): void {
    this.providers.set(chainId, new JsonRpcProvider(rpcUrl));
  }

  private async scanBlock(blockNumber: number): Promise<void> {
    const block = await this.provider.getBlock(blockNumber, true);
    if (!block) return;

    const promises = block.transactions.map(async (tx: any) => {
      const receipt = await this.provider.getTransactionReceipt(tx.hash);
      return this.analyzeTransaction(tx, receipt);
    });
  }

  private analyzeTransaction(tx: any, receipt: any): any {
    return {
      hash: tx.hash,
      profit: 0
    };
  }
}



