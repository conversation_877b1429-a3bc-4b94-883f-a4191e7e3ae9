// 🔥 TEST DIRECT BL<PERSON><PERSON><PERSON>AIN INTELLIGENCE - NO DEPLOYMENT NEEDED!
// This shows you EXACTLY who's making money on Meteora DLMM

import DirectBlockchainIntelligence from './src/blockchain-direct-reader.js';
import chalk from 'chalk';

async function testDirectBlockchainIntelligence() {
  console.log(chalk.yellow.bold('🔥 TESTING DIRECT BLOCKCHAIN INTELLIGENCE'));
  console.log(chalk.cyan('📡 Reading Meteora DLMM directly from Solana blockchain...'));
  console.log(chalk.green('💰 NO DEPLOYMENT COSTS - Pure blockchain data extraction!'));
  console.log('═'.repeat(80));
  
  const intelligence = new DirectBlockchainIntelligence();
  
  try {
    // 🎯 TEST 1: SCAN ALL DLMM POOLS
    console.log(chalk.blue('\n🔍 TEST 1: Scanning ALL Meteora DLMM pools from blockchain...'));
    const pools = await intelligence.scanAllDLMMPools();
    
    console.log(chalk.green(`✅ Found ${pools.length} DLMM pools directly from blockchain`));
    
    if (pools.length > 0) {
      console.log(chalk.cyan('\n📊 Sample Pool Data:'));
      const samplePool = pools[0];
      console.log(`   Address: ${samplePool.address}`);
      console.log(`   Token X: ${samplePool.tokenX}`);
      console.log(`   Token Y: ${samplePool.tokenY}`);
      console.log(`   TVL: $${samplePool.tvl.toFixed(2)}`);
      console.log(`   24h Volume: $${samplePool.volume24h.toFixed(2)}`);
      console.log(`   24h Fees: $${samplePool.fees24h.toFixed(2)}`);
    }
    
    // 🎯 TEST 2: FIND PROFITABLE WALLETS
    console.log(chalk.blue('\n👥 TEST 2: Finding who\'s making the most money...'));
    const profitableWallets = await intelligence.findProfitableWallets();
    
    console.log(chalk.green(`✅ Found ${profitableWallets.length} profitable wallets`));
    
    if (profitableWallets.length > 0) {
      console.log(chalk.cyan('\n💰 Top Profitable Wallets:'));
      profitableWallets.slice(0, 5).forEach((wallet, index) => {
        const [address, data] = wallet;
        console.log(`   ${index + 1}. ${address.substring(0, 8)}...`);
        console.log(`      Total Profit: $${data.totalProfit.toFixed(2)}`);
        console.log(`      Success Rate: ${(data.successRate * 100).toFixed(1)}%`);
        console.log(`      Trade Count: ${data.tradeCount}`);
        console.log(`      Avg Profit: $${data.avgProfit.toFixed(2)}`);
      });
    }
    
    // 🎯 TEST 3: GET CONCENTRATION INTELLIGENCE
    console.log(chalk.blue('\n🧠 TEST 3: Extracting concentration intelligence...'));
    const concentrationIntelligence = await intelligence.getConcentrationIntelligence();
    
    console.log(chalk.green(`✅ Generated concentration intelligence for ${concentrationIntelligence.length} pools`));
    
    if (concentrationIntelligence.length > 0) {
      console.log(chalk.yellow.bold('\n🏆 TOP CONCENTRATION OPPORTUNITIES:'));
      console.log('═'.repeat(80));
      
      concentrationIntelligence.slice(0, 10).forEach((pool, index) => {
        console.log(chalk.cyan(`\n${index + 1}. Pool: ${pool.tokenX.substring(0, 8)}.../${pool.tokenY.substring(0, 8)}...`));
        console.log(chalk.green(`   🏆 Ultimate Score: ${pool.ultimateScore.toFixed(2)}`));
        console.log(chalk.yellow(`   🎯 Concentration: ${pool.concentration.toFixed(2)}`));
        console.log(chalk.magenta(`   💰 Profitability: ${pool.profitability.toFixed(2)}%`));
        console.log(chalk.blue(`   👥 Active Wallets: ${pool.walletActivity.activeWallets}`));
        console.log(chalk.white(`   💸 Total Wallet Profit: $${pool.walletActivity.totalProfit.toFixed(2)}`));
        console.log(chalk.gray(`   📊 Avg Success Rate: ${(pool.walletActivity.avgSuccessRate * 100).toFixed(1)}%`));
        console.log(chalk.gray(`   💵 TVL: $${pool.tvl.toFixed(2)} | Volume: $${pool.volume24h.toFixed(2)}`));
      });
    }
    
    // 🎯 TEST 4: REAL-TIME MONITORING DEMO
    console.log(chalk.blue('\n📡 TEST 4: Real-time monitoring demo (30 seconds)...'));
    console.log(chalk.cyan('This would run continuously to catch opportunities as they happen!'));
    
    let monitorCount = 0;
    const monitorInterval = setInterval(async () => {
      monitorCount++;
      console.log(chalk.gray(`\n📊 Monitor scan #${monitorCount}...`));
      
      try {
        const liveIntelligence = await intelligence.getConcentrationIntelligence();
        
        if (liveIntelligence.length > 0) {
          const topOpportunity = liveIntelligence[0];
          console.log(chalk.green(`🔥 TOP LIVE OPPORTUNITY: ${topOpportunity.tokenX.substring(0, 8)}.../${topOpportunity.tokenY.substring(0, 8)}...`));
          console.log(chalk.yellow(`   Ultimate Score: ${topOpportunity.ultimateScore.toFixed(2)}`));
          
          // Alert for exceptional opportunities
          if (topOpportunity.ultimateScore > 300) {
            console.log(chalk.red.bold('🚨 EXCEPTIONAL OPPORTUNITY DETECTED! 🚨'));
          }
        }
        
      } catch (error) {
        console.log(chalk.red(`❌ Monitor error: ${error.message}`));
      }
      
      if (monitorCount >= 3) {
        clearInterval(monitorInterval);
        console.log(chalk.green('\n✅ Real-time monitoring demo completed!'));
        showFinalSummary();
      }
    }, 10000); // Every 10 seconds for demo
    
  } catch (error) {
    console.error(chalk.red('❌ Test failed:'), error.message);
    console.error(error.stack);
  }
}

function showFinalSummary() {
  console.log('\n' + '═'.repeat(80));
  console.log(chalk.yellow.bold('🎯 DIRECT BLOCKCHAIN INTELLIGENCE SUMMARY'));
  console.log('═'.repeat(80));
  
  console.log(chalk.green('✅ WHAT WE ACCOMPLISHED:'));
  console.log(chalk.white('   • Read Meteora DLMM pools directly from blockchain'));
  console.log(chalk.white('   • Identified profitable wallets and their strategies'));
  console.log(chalk.white('   • Calculated concentration and profitability scores'));
  console.log(chalk.white('   • Generated ultimate opportunity rankings'));
  console.log(chalk.white('   • Demonstrated real-time monitoring capabilities'));
  
  console.log(chalk.cyan('\n💰 COST ANALYSIS:'));
  console.log(chalk.white('   • Deployment Cost: $0 (NO SMART CONTRACT NEEDED!)'));
  console.log(chalk.white('   • RPC Calls: ~$0.01 per hour (negligible)'));
  console.log(chalk.white('   • Total Cost: Essentially FREE!'));
  
  console.log(chalk.magenta('\n🧠 INTELLIGENCE CAPABILITIES:'));
  console.log(chalk.white('   • Direct access to ALL Meteora DLMM data'));
  console.log(chalk.white('   • Real-time profitable wallet tracking'));
  console.log(chalk.white('   • Fee concentration analysis'));
  console.log(chalk.white('   • Smart money movement detection'));
  console.log(chalk.white('   • Ultimate opportunity scoring'));
  
  console.log(chalk.yellow('\n🚀 NEXT STEPS:'));
  console.log(chalk.white('   1. Integrate with your honey hunter system'));
  console.log(chalk.white('   2. Start real-time monitoring'));
  console.log(chalk.white('   3. Begin trading with $20 starting capital'));
  console.log(chalk.white('   4. Scale based on proven profitability'));
  
  console.log(chalk.green.bold('\n🧙‍♂️ YOU ARE NOW THE ULTIMATE METEORA WIZARD!'));
  console.log(chalk.cyan('You have direct access to the blockchain source of truth!'));
  console.log(chalk.yellow('No one can deceive you - you see exactly where the money flows!'));
  
  console.log('\n' + '═'.repeat(80));
}

// 🚀 RUN THE TEST
console.log(chalk.magenta.bold('🔥 STARTING DIRECT BLOCKCHAIN INTELLIGENCE TEST...'));
console.log(chalk.cyan('This will show you EXACTLY how to read Meteora DLMM from the source!'));
console.log('');

testDirectBlockchainIntelligence().catch(error => {
  console.error(chalk.red('❌ Test failed:'), error.message);
});

// 🎯 SIMPLE USAGE EXAMPLE
export async function getTopOpportunities() {
  const intelligence = new DirectBlockchainIntelligence();
  const opportunities = await intelligence.getConcentrationIntelligence();
  
  return opportunities.slice(0, 5).map(pool => ({
    pool: `${pool.tokenX.substring(0, 8)}.../${pool.tokenY.substring(0, 8)}...`,
    ultimateScore: pool.ultimateScore,
    concentration: pool.concentration,
    profitability: pool.profitability,
    activeWallets: pool.walletActivity.activeWallets,
    recommendation: pool.ultimateScore > 200 ? 'STRONG_BUY' : 
                   pool.ultimateScore > 100 ? 'BUY' : 'MONITOR'
  }));
}

// 🔍 FIND SPECIFIC PROFITABLE WALLET
export async function analyzeWallet(walletAddress) {
  const intelligence = new DirectBlockchainIntelligence();
  const profitableWallets = await intelligence.findProfitableWallets();
  
  const wallet = profitableWallets.find(([address]) => address === walletAddress);
  
  if (wallet) {
    const [address, data] = wallet;
    return {
      address,
      totalProfit: data.totalProfit,
      successRate: data.successRate,
      tradeCount: data.tradeCount,
      avgProfit: data.avgProfit,
      status: data.totalProfit > 1000 ? 'WHALE' : 
              data.successRate > 0.8 ? 'EXPERT' : 'ACTIVE'
    };
  }
  
  return null;
}
