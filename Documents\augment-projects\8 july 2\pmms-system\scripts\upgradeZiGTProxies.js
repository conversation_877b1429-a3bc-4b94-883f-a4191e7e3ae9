// scripts/upgradeZiGTProxies.js
const { ethers, upgrades } = require("hardhat");
require("dotenv").config();

async function main() {
    const [deployer] = await ethers.getSigners();
    console.log(`\n🚀 Running upgrade script with account: ${deployer.address}`);
    console.log("💰 Balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "sFUEL");

    // if (parseFloat(ethers.formatEther(await deployer.provider.getBalance(deployer.address))) === 0) {
    //     console.error("\n❌ WARNING: Deployer account has 0 sFUEL. Transactions will fail due to insufficient funds.");
    //     console.error("Please fund your account (******************************************) with sFUEL and try again.");
    //     process.exit(1); // Exit if no funds
    // }

    // List of all deployed ZiGT token proxy addresses
    // IMPORTANT: Keep these as your *existing* proxy addresses.
    const ZIGT_TOKEN_PROXIES = [
        { name: "<PERSON>i<PERSON>-<PERSON>", address: "******************************************" },
        { name: "ZiG-N", address: "******************************************" },
        { name: "ZiG-RG", address: "******************************************" },
        { name: "ZiG-KB", address: "******************************************" },
        { name: "ZiG-UB", address: "******************************************" },
        { name: "ZiG-SF", address: "0x632612061BA979dFef4E2702d858667bBf04698e" },
        { name: "ZiG-PC", address: "0xa5E5822167F4A6272717Bf0338A2f6805B5f20c5" },
        { name: "ZiG-MG", address: "******************************************" },
        { name: "ZiG-SH", address: "******************************************" },
        { name: "ZiG-CD", address: "******************************************" },
        { name: "ZiG-KU", address: "******************************************" },
        { name: "ZiG-KD", address: "******************************************" },
    ];

    // Get the ContractFactory for your ZiGT contract (this reflects the LATEST compiled code)
    const ZiGTFactory = await ethers.getContractFactory("contracts/ZiGT_github/ZiGT.sol:ZiGT");

    for (const tokenInfo of ZIGT_TOKEN_PROXIES) {
        console.log(`\n--- Upgrading ${tokenInfo.name} (${tokenInfo.address}) ---`);
        try {
            // Optional: Validate the upgrade first. This checks for storage layout compatibility.
            // This is crucial for safe upgrades.
            await upgrades.validateUpgrade(tokenInfo.address, ZiGTFactory);
            console.log(`  ✅ Upgrade validation passed for ${tokenInfo.name}.`);

            // Perform the upgrade. This will deploy a new implementation if needed,
            // and then update the proxy to point to it.
            const upgradedToken = await upgrades.upgradeProxy(tokenInfo.address, ZiGTFactory);
            await upgradedToken.waitForDeployment(); // Wait for the transaction to be mined

            const newImplementationAddress = await upgrades.erc1967.getImplementationAddress(tokenInfo.address);

            console.log(`  ✅ ${tokenInfo.name} upgraded successfully!`);
            console.log(`     Proxy Address: ${upgradedToken.target}`); // Target is the proxy address
            console.log(`     New Implementation Address: ${newImplementationAddress}`);

        } catch (error) {
            console.error(`  ❌ Error upgrading ${tokenInfo.name}:`, error.message);
        }
    }

    console.log("\n--- Proxy upgrade script finished ---");
}

main()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error(error);
        process.exit(1);
    });