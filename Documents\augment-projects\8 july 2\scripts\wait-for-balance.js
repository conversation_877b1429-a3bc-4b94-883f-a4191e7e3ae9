/**
 * ⏳ WAIT FOR BALANCE TO APPEAR
 * Monitor balance until ETH arrives from bridge
 */

const { ethers } = require('hardhat');

async function waitForBalance() {
    console.log('⏳ WAITING FOR ETH TO ARRIVE ON ARBITRUM...');
    console.log('🌉 Bridge transactions can take 1-10 minutes');
    console.log('🎯 Monitoring: ******************************************');
    console.log('=' .repeat(80));
    
    const [deployer] = await ethers.getSigners();
    const targetAddress = deployer.address;
    
    console.log(`👤 Monitoring Address: ${targetAddress}`);
    
    let attempts = 0;
    const maxAttempts = 60; // 10 minutes max
    
    while (attempts < maxAttempts) {
        try {
            const balance = await deployer.getBalance();
            const balanceEth = ethers.utils.formatEther(balance);
            
            console.log(`💰 Attempt ${attempts + 1}: Balance = ${balanceEth} ETH (${balance.toString()} wei)`);
            
            if (balance.gt(0)) {
                console.log('🎉 ETH DETECTED ON ARBITRUM!');
                console.log(`💰 Balance: ${balanceEth} ETH`);
                
                // Check if enough for deployment
                const gasPrice = await ethers.provider.getGasPrice();
                const estimatedGas = 2500000;
                const deploymentCost = gasPrice.mul(estimatedGas);
                
                console.log(`💸 Deployment Cost: ${ethers.utils.formatEther(deploymentCost)} ETH`);
                
                if (balance.gte(deploymentCost)) {
                    console.log('✅ SUFFICIENT BALANCE FOR DEPLOYMENT!');
                    return { balance, canDeploy: true };
                } else {
                    console.log('⚠️ Balance detected but not enough for deployment');
                    const needed = deploymentCost.sub(balance);
                    console.log(`💰 Need ${ethers.utils.formatEther(needed)} more ETH`);
                    return { balance, canDeploy: false };
                }
            }
            
            // Wait 10 seconds before next check
            await new Promise(resolve => setTimeout(resolve, 10000));
            attempts++;
            
        } catch (error) {
            console.log(`❌ Error checking balance: ${error.message}`);
            await new Promise(resolve => setTimeout(resolve, 10000));
            attempts++;
        }
    }
    
    console.log('⏰ TIMEOUT: No ETH detected after 10 minutes');
    console.log('💡 Bridge might take longer or transaction failed');
    return { balance: ethers.BigNumber.from(0), canDeploy: false };
}

// Execute monitoring
if (require.main === module) {
    waitForBalance()
        .then((result) => {
            if (result.canDeploy) {
                console.log('\n🚀 READY TO DEPLOY!');
                console.log('💡 Run: npx hardhat run scripts/deploy-arbitrum-aave-perfect.js --network arbitrum');
            } else {
                console.log('\n⏳ Still waiting for sufficient balance...');
            }
        })
        .catch((error) => {
            console.error('Balance monitoring failed:', error.message);
        });
}

module.exports = { waitForBalance };
