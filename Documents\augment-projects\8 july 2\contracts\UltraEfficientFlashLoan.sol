// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol";
import "@aave/core-v3/contracts/interfaces/IPool.sol";
import "@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title UltraEfficientFlashLoan
 * @dev Production-ready flash loan arbitrage contract optimized for <400k gas usage
 * Based on analysis of profitable patterns: $4,320+ profit with 360k gas
 */
contract UltraEfficientFlashLoan is FlashLoanSimpleReceiverBase, Ownable, ReentrancyGuard, Pausable {
    
    // ============ CONSTANTS ============
    
    uint256 public constant MAX_GAS_LIMIT = 400000; // Ultra-efficient threshold
    uint256 public constant MIN_PROFIT_THRESHOLD = 500e6; // $500 USDC (6 decimals)
    uint256 public constant MAX_SLIPPAGE = 300; // 3% max slippage (basis points)
    uint256 public constant EMERGENCY_TIMEOUT = 300; // 5 minutes
    
    // ============ STATE VARIABLES ============

    // Profit wallet for immediate profit extraction
    address public constant PROFIT_WALLET = ******************************************;

    struct ArbitrageParams {
        address tokenIn;
        address tokenOut;
        address dexA; // Source DEX (e.g., SushiSwap)
        address dexB; // Target DEX (e.g., Uniswap V3)
        uint256 amountIn;
        uint256 minAmountOut;
        bytes routeA; // DEX A route data
        bytes routeB; // DEX B route data
        uint256 deadline;
    }
    
    struct SafetyChecks {
        uint256 maxGasUsed;
        uint256 minProfitRequired;
        uint256 maxSlippageTolerance;
        bool emergencyStop;
        uint256 lastExecutionTime;
    }
    
    SafetyChecks public safetyConfig;
    
    mapping(address => bool) public authorizedCallers;
    mapping(address => uint256) public tokenBalances;
    
    uint256 public totalProfitGenerated;
    uint256 public totalExecutions;
    uint256 public failedExecutions;
    
    // ============ EVENTS ============
    
    event FlashLoanExecuted(
        address indexed asset,
        uint256 amount,
        uint256 profit,
        uint256 gasUsed,
        address indexed caller
    );
    
    event ArbitrageCompleted(
        address indexed tokenIn,
        address indexed tokenOut,
        uint256 amountIn,
        uint256 profit,
        uint256 gasUsed
    );
    
    event EmergencyStop(address indexed caller, string reason);
    event ProfitWithdrawn(address indexed token, uint256 amount, address indexed to);
    
    // ============ MODIFIERS ============
    
    modifier onlyAuthorized() {
        require(authorizedCallers[msg.sender] || msg.sender == owner(), "Unauthorized");
        _;
    }
    
    modifier gasOptimized() {
        uint256 gasStart = gasleft();
        _;
        uint256 gasUsed = gasStart - gasleft();
        require(gasUsed <= MAX_GAS_LIMIT, "Gas limit exceeded");
    }
    
    modifier profitableOnly(uint256 expectedProfit) {
        require(expectedProfit >= MIN_PROFIT_THRESHOLD, "Profit below threshold");
        _;
    }
    
    modifier emergencyCheck() {
        require(!safetyConfig.emergencyStop, "Emergency stop active");
        require(
            block.timestamp >= safetyConfig.lastExecutionTime + 1, 
            "Execution too frequent"
        );
        _;
    }
    
    // ============ CONSTRUCTOR ============
    
    constructor(
        address _addressProvider
    ) FlashLoanSimpleReceiverBase(IPoolAddressesProvider(_addressProvider)) Ownable(msg.sender) {
        safetyConfig = SafetyChecks({
            maxGasUsed: MAX_GAS_LIMIT,
            minProfitRequired: MIN_PROFIT_THRESHOLD,
            maxSlippageTolerance: MAX_SLIPPAGE,
            emergencyStop: false,
            lastExecutionTime: 0
        });
        
        authorizedCallers[msg.sender] = true;
    }
    
    // ============ MAIN EXECUTION FUNCTIONS ============
    
    /**
     * @dev Execute ultra-efficient flash loan arbitrage
     * @param asset The asset to flash loan
     * @param amount The amount to flash loan
     * @param params Arbitrage parameters
     */
    function executeUltraEfficientArbitrage(
        address asset,
        uint256 amount,
        ArbitrageParams calldata params
    ) 
        external 
        onlyAuthorized 
        nonReentrant 
        whenNotPaused 
        emergencyCheck
        gasOptimized
        profitableOnly(params.minAmountOut)
    {
        require(params.deadline >= block.timestamp, "Deadline expired");
        require(amount > 0, "Invalid amount");
        require(asset == params.tokenIn, "Asset mismatch");
        
        // Update execution tracking
        safetyConfig.lastExecutionTime = block.timestamp;
        totalExecutions++;
        
        // Encode arbitrage parameters for flash loan callback
        bytes memory encodedParams = abi.encode(params);
        
        // Execute flash loan
        POOL.flashLoanSimple(
            address(this),
            asset,
            amount,
            encodedParams,
            0 // referralCode
        );
    }
    
    /**
     * @dev Flash loan callback - executes arbitrage logic
     */
    function executeOperation(
        address asset,
        uint256 amount,
        uint256 premium,
        address initiator,
        bytes calldata params
    ) external override returns (bool) {
        require(msg.sender == address(POOL), "Invalid caller");
        require(initiator == address(this), "Invalid initiator");
        
        uint256 gasStart = gasleft();
        
        // Decode arbitrage parameters
        ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
        
        // Execute arbitrage strategy
        uint256 profit = _executeArbitrageStrategy(asset, amount, arbParams);
        
        // Calculate total amount to repay
        uint256 totalDebt = amount + premium;
        
        // Ensure we have enough to repay and profit
        require(profit > premium, "Arbitrage not profitable");
        
        // Approve pool to pull the debt
        IERC20(asset).approve(address(POOL), totalDebt);
        
        // Track metrics
        uint256 gasUsed = gasStart - gasleft();
        uint256 netProfit = profit - premium;
        totalProfitGenerated += netProfit;

        // IMMEDIATE PROFIT EXTRACTION FOR SECURITY
        if (netProfit > 0) {
            IERC20(asset).transfer(PROFIT_WALLET, netProfit);
        }

        emit FlashLoanExecuted(asset, amount, netProfit, gasUsed, tx.origin);

        return true;
    }
    
    // ============ ARBITRAGE STRATEGY ============
    
    /**
     * @dev Execute the core arbitrage strategy (optimized for gas efficiency)
     */
    function _executeArbitrageStrategy(
        address asset,
        uint256 amount,
        ArbitrageParams memory params
    ) internal returns (uint256 profit) {
        uint256 initialBalance = IERC20(asset).balanceOf(address(this));
        
        // Step 1: Swap on DEX A (e.g., SushiSwap)
        uint256 intermediateAmount = _swapOnDexA(
            params.tokenIn,
            params.tokenOut,
            amount,
            params.dexA,
            params.routeA
        );
        
        // Step 2: Swap back on DEX B (e.g., Uniswap V3)
        _swapOnDexB(
            params.tokenOut,
            params.tokenIn,
            intermediateAmount,
            params.dexB,
            params.routeB
        );
        
        uint256 currentBalance = IERC20(asset).balanceOf(address(this));
        profit = currentBalance - initialBalance;
        
        // Safety check: ensure minimum profit
        require(profit >= params.minAmountOut, "Insufficient profit");
        
        emit ArbitrageCompleted(
            params.tokenIn,
            params.tokenOut,
            amount,
            profit,
            gasleft()
        );
        
        return profit;
    }
    
    /**
     * @dev Swap on DEX A (optimized for gas)
     */
    function _swapOnDexA(
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        address dex,
        bytes memory routeData
    ) internal returns (uint256 amountOut) {
        // Approve DEX to spend tokens
        IERC20(tokenIn).approve(dex, amountIn);
        
        // Execute swap using low-level call for gas optimization
        (bool success, bytes memory result) = dex.call(routeData);
        require(success, "DEX A swap failed");
        
        // Decode amount out (assuming standard return format)
        if (result.length > 0) {
            amountOut = abi.decode(result, (uint256));
        } else {
            amountOut = IERC20(tokenOut).balanceOf(address(this));
        }
        
        require(amountOut > 0, "DEX A: No output");
        return amountOut;
    }
    
    /**
     * @dev Swap on DEX B (optimized for gas)
     */
    function _swapOnDexB(
        address tokenIn,
        address tokenOut,
        uint256 amountIn,
        address dex,
        bytes memory routeData
    ) internal returns (uint256 amountOut) {
        // Approve DEX to spend tokens
        IERC20(tokenIn).approve(dex, amountIn);
        
        // Execute swap using low-level call for gas optimization
        (bool success, bytes memory result) = dex.call(routeData);
        require(success, "DEX B swap failed");
        
        // Decode amount out
        if (result.length > 0) {
            amountOut = abi.decode(result, (uint256));
        } else {
            amountOut = IERC20(tokenOut).balanceOf(address(this));
        }
        
        require(amountOut > 0, "DEX B: No output");
        return amountOut;
    }
    
    // ============ SAFETY & ADMIN FUNCTIONS ============
    
    /**
     * @dev Emergency stop function
     */
    function emergencyStop(string calldata reason) external onlyOwner {
        safetyConfig.emergencyStop = true;
        _pause();
        emit EmergencyStop(msg.sender, reason);
    }
    
    /**
     * @dev Resume operations
     */
    function resumeOperations() external onlyOwner {
        safetyConfig.emergencyStop = false;
        _unpause();
    }
    
    /**
     * @dev Withdraw profits
     */
    function withdrawProfits(address token, uint256 amount, address to) external onlyOwner {
        require(to != address(0), "Invalid recipient");
        require(amount > 0, "Invalid amount");
        
        IERC20(token).transfer(to, amount);
        emit ProfitWithdrawn(token, amount, to);
    }
    
    /**
     * @dev Add authorized caller
     */
    function addAuthorizedCaller(address caller) external onlyOwner {
        authorizedCallers[caller] = true;
    }
    
    /**
     * @dev Remove authorized caller
     */
    function removeAuthorizedCaller(address caller) external onlyOwner {
        authorizedCallers[caller] = false;
    }
    
    /**
     * @dev Update safety configuration
     */
    function updateSafetyConfig(
        uint256 maxGas,
        uint256 minProfit,
        uint256 maxSlippage
    ) external onlyOwner {
        require(maxGas <= 500000, "Gas limit too high");
        require(minProfit >= 100e6, "Min profit too low");
        require(maxSlippage <= 1000, "Slippage too high");
        
        safetyConfig.maxGasUsed = maxGas;
        safetyConfig.minProfitRequired = minProfit;
        safetyConfig.maxSlippageTolerance = maxSlippage;
    }
    
    // ============ VIEW FUNCTIONS ============
    
    function getContractStats() external view returns (
        uint256 totalProfit,
        uint256 totalExecs,
        uint256 failedExecs,
        uint256 successRate
    ) {
        totalProfit = totalProfitGenerated;
        totalExecs = totalExecutions;
        failedExecs = failedExecutions;
        successRate = totalExecs > 0 ? ((totalExecs - failedExecs) * 100) / totalExecs : 0;
    }
    
    function getSafetyConfig() external view returns (SafetyChecks memory) {
        return safetyConfig;
    }
    
    // ============ FALLBACK ============
    
    receive() external payable {
        revert("Direct payments not accepted");
    }
}

