// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * 🎯 OPTION 1: COMPOUND BORROWING STRATEGY
 * Flash loan → Supply to Aave → Borrow from Compound V3 → Profit
 * Tests if Compound allows borrowing within flash loan context
 */

interface IERC20 {
    function balanceOf(address account) external view returns (uint256);
    function transfer(address to, uint256 amount) external returns (bool);
    function approve(address spender, uint256 amount) external returns (bool);
}

interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external;
}

interface IAavePool {
    function supply(address asset, uint256 amount, address onBehalfOf, uint16 referralCode) external;
    function withdraw(address asset, uint256 amount, address to) external returns (uint256);
    function setUserEMode(uint8 categoryId) external;
    function getUserAccountData(address user) external view returns (
        uint256 totalCollateralBase,
        uint256 totalDebtBase,
        uint256 availableBorrowsBase,
        uint256 currentLiquidationThreshold,
        uint256 ltv,
        uint256 healthFactor
    );
}

interface ICompoundV3 {
    function supply(address asset, uint amount) external;
    function withdraw(address asset, uint amount) external;
    function borrow(uint amount) external;
    function repay(uint amount) external;
    function baseToken() external view returns (address);
    function balanceOf(address account) external view returns (uint256);
    function borrowBalanceOf(address account) external view returns (uint256);
    function getSupplyRate(uint utilization) external view returns (uint64);
    function getBorrowRate(uint utilization) external view returns (uint64);
}

contract CompoundBorrowingStrategy is IFlashLoanRecipient {
    
    // 🎯 ARBITRUM ADDRESSES (VERIFIED WORKING)
    IERC20 public constant USDC = IERC20(******************************************);
    IBalancerVault public constant BALANCER = IBalancerVault(******************************************);
    IAavePool public constant AAVE_POOL = IAavePool(******************************************);
    ICompoundV3 public constant COMPOUND_USDC = ICompoundV3(******************************************);
    
    address public constant PROFIT_WALLET = ******************************************;
    
    // 💰 STRATEGY PARAMETERS
    uint256 public constant FLASH_AMOUNT = 10000e6;   // $10,000 USDC
    uint256 public constant AAVE_SUPPLY = 8000e6;     // $8,000 USDC to Aave
    uint256 public constant COMPOUND_SUPPLY = 1500e6; // $1,500 USDC to Compound
    uint256 public constant COMPOUND_BORROW = 750e6;  // $750 USDC from Compound (50% LTV)
    uint8 public constant EMODE_CATEGORY = 1;         // Stablecoins eMode
    
    // 📊 TRACKING
    uint256 public totalExecutions;
    uint256 public lastProfit;
    bool public lastSuccess;
    uint256 public totalProfit;
    
    // 🎯 EVENTS
    event CompoundBorrowingTest(string step, uint256 value);
    event CompoundBorrowingSuccess(uint256 amount);
    event CompoundBorrowingFailed(string reason);
    event ProfitExtracted(uint256 profit, address wallet, uint256 execution);
    
    /**
     * 🚀 EXECUTE COMPOUND BORROWING STRATEGY
     */
    function executeCompoundBorrowingStrategy() external {
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        
        tokens[0] = address(USDC);
        amounts[0] = FLASH_AMOUNT;
        
        emit CompoundBorrowingTest("Starting Compound borrowing test", block.timestamp);
        
        // Execute flash loan
        BALANCER.flashLoan(address(this), tokens, amounts, "");
    }
    
    /**
     * 🎯 FLASH LOAN CALLBACK WITH COMPOUND BORROWING
     */
    function receiveFlashLoan(
        address[] memory,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory
    ) external override {
        require(msg.sender == address(BALANCER), "Only Balancer");
        
        uint256 flashAmount = amounts[0];
        uint256 initialBalance = USDC.balanceOf(address(this));
        
        emit CompoundBorrowingTest("Flash loan received", flashAmount);
        
        // Verify flash loan received
        require(initialBalance >= flashAmount, "Flash loan not received");
        
        // Execute Aave + Compound strategy
        _executeAaveCompoundStrategy();
        
        // Repay flash loan (including any fees)
        uint256 totalRepayment = flashAmount + feeAmounts[0];
        USDC.transfer(address(BALANCER), totalRepayment);
        
        emit CompoundBorrowingTest("Flash loan repaid", totalRepayment);
        
        // Extract profit
        uint256 finalBalance = USDC.balanceOf(address(this));
        totalExecutions++;
        
        if (finalBalance > 0) {
            lastProfit = finalBalance;
            totalProfit += finalBalance;
            USDC.transfer(PROFIT_WALLET, finalBalance);
            lastSuccess = true;
            emit ProfitExtracted(finalBalance, PROFIT_WALLET, totalExecutions);
        } else {
            lastProfit = 0;
            lastSuccess = false;
        }
        
        emit CompoundBorrowingTest("Strategy completed", finalBalance);
    }
    
    /**
     * 💰 AAVE + COMPOUND STRATEGY
     * Supply to Aave for collateral, borrow from Compound for leverage
     */
    function _executeAaveCompoundStrategy() internal {
        emit CompoundBorrowingTest("Starting Aave + Compound strategy", 1);
        
        // Step 1: Enable Aave eMode
        AAVE_POOL.setUserEMode(EMODE_CATEGORY);
        emit CompoundBorrowingTest("Aave eMode set", EMODE_CATEGORY);
        
        // Step 2: Supply USDC to Aave as collateral
        USDC.approve(address(AAVE_POOL), type(uint256).max);
        AAVE_POOL.supply(address(USDC), AAVE_SUPPLY, address(this), 0);
        emit CompoundBorrowingTest("USDC supplied to Aave", AAVE_SUPPLY);
        
        // Step 3: Supply USDC to Compound for borrowing power
        USDC.approve(address(COMPOUND_USDC), type(uint256).max);
        COMPOUND_USDC.supply(address(USDC), COMPOUND_SUPPLY);
        emit CompoundBorrowingTest("USDC supplied to Compound", COMPOUND_SUPPLY);
        
        // Check Compound supply balance
        uint256 compoundBalance = COMPOUND_USDC.balanceOf(address(this));
        emit CompoundBorrowingTest("Compound supply balance", compoundBalance);
        
        // Step 4: Attempt borrowing from Compound (KEY TEST!)
        emit CompoundBorrowingTest("Attempting Compound borrow", COMPOUND_BORROW);
        
        try COMPOUND_USDC.borrow(COMPOUND_BORROW) {
            emit CompoundBorrowingTest("Compound borrow successful", COMPOUND_BORROW);
            emit CompoundBorrowingSuccess(COMPOUND_BORROW);
            
            // Check borrowed balance
            uint256 borrowBalance = COMPOUND_USDC.borrowBalanceOf(address(this));
            emit CompoundBorrowingTest("Compound borrow balance", borrowBalance);
            
            // Immediately repay Compound borrow
            USDC.approve(address(COMPOUND_USDC), type(uint256).max);
            COMPOUND_USDC.repay(COMPOUND_BORROW);
            emit CompoundBorrowingTest("Compound repay successful", COMPOUND_BORROW);
            
        } catch Error(string memory reason) {
            emit CompoundBorrowingTest("Compound borrow failed", 0);
            emit CompoundBorrowingFailed(reason);
            // Continue without borrowing
        } catch {
            emit CompoundBorrowingTest("Compound borrow failed unknown", 0);
            emit CompoundBorrowingFailed("Unknown error");
            // Continue without borrowing
        }
        
        // Step 5: Withdraw from Compound
        try COMPOUND_USDC.withdraw(address(USDC), COMPOUND_SUPPLY) {
            emit CompoundBorrowingTest("Compound withdrawal successful", COMPOUND_SUPPLY);
        } catch {
            emit CompoundBorrowingTest("Compound withdrawal failed", 0);
            // Try withdrawing available balance
            uint256 availableBalance = COMPOUND_USDC.balanceOf(address(this));
            if (availableBalance > 0) {
                COMPOUND_USDC.withdraw(address(USDC), availableBalance);
                emit CompoundBorrowingTest("Compound partial withdrawal", availableBalance);
            }
        }
        
        // Step 6: Withdraw from Aave
        AAVE_POOL.withdraw(address(USDC), AAVE_SUPPLY, address(this));
        emit CompoundBorrowingTest("Aave withdrawal completed", AAVE_SUPPLY);
    }
    
    /**
     * 📊 GET EXECUTION STATS
     */
    function getExecutionStats() external view returns (
        uint256 lastProfitAmount,
        bool lastExecutionSuccess,
        uint256 totalExecutionCount,
        uint256 totalProfits
    ) {
        return (lastProfit, lastSuccess, totalExecutions, totalProfit);
    }
    
    /**
     * 🔧 GET CONTRACT INFO
     */
    function getContractInfo() external view returns (
        address usdc,
        address balancer,
        address aavePool,
        address compoundUsdc,
        address profitWallet
    ) {
        return (
            address(USDC),
            address(BALANCER),
            address(AAVE_POOL),
            address(COMPOUND_USDC),
            PROFIT_WALLET
        );
    }
    
    /**
     * 🔍 CHECK COMPOUND STATUS
     */
    function checkCompoundStatus() external view returns (
        address baseToken,
        uint256 supplyRate,
        uint256 borrowRate,
        bool compoundActive
    ) {
        try COMPOUND_USDC.baseToken() returns (address token) {
            baseToken = token;
        } catch {
            baseToken = address(0);
        }
        
        try COMPOUND_USDC.getSupplyRate(800000000000000000) returns (uint64 rate1) {
            supplyRate = rate1;
        } catch {
            supplyRate = 0;
        }

        try COMPOUND_USDC.getBorrowRate(800000000000000000) returns (uint64 rate2) {
            borrowRate = rate2;
        } catch {
            borrowRate = 0;
        }
        
        compoundActive = (baseToken != address(0) && supplyRate > 0);
    }
}
