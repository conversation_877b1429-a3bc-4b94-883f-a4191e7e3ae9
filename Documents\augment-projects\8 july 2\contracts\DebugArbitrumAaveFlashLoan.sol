// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

/**
 * 🔍 DEBUG ARBITRUM AAVE FLASH LOAN
 * Identical to working contract but with EXTENSIVE logging to find exact failure point
 * DO NOT CHANGE CORE LOGIC - ONLY ADD LOGGING
 */

interface IERC20 {
    function balanceOf(address account) external view returns (uint256);
    function transfer(address to, uint256 amount) external returns (bool);
    function approve(address spender, uint256 amount) external returns (bool);
}

interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

interface IFlashLoanRecipient {
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external;
}

interface IAavePool {
    function supply(address asset, uint256 amount, address onBehalfOf, uint16 referralCode) external;
    function borrow(address asset, uint256 amount, uint256 interestRateMode, uint16 referralCode, address onBehalfOf) external;
    function withdraw(address asset, uint256 amount, address to) external returns (uint256);
    function repay(address asset, uint256 amount, uint256 interestRateMode, address onBehalfOf) external returns (uint256);
    function setUserEMode(uint8 categoryId) external;
    function getUserAccountData(address user) external view returns (
        uint256 totalCollateralBase,
        uint256 totalDebtBase,
        uint256 availableBorrowsBase,
        uint256 currentLiquidationThreshold,
        uint256 ltv,
        uint256 healthFactor
    );
}

contract DebugArbitrumAaveFlashLoan is IFlashLoanRecipient {
    
    // 🎯 ARBITRUM ADDRESSES (VERIFIED WORKING)
    IERC20 public constant USDC = IERC20(******************************************);
    IERC20 public constant WETH = IERC20(******************************************);
    IBalancerVault public constant BALANCER = IBalancerVault(******************************************);
    IAavePool public constant AAVE_POOL = IAavePool(******************************************);
    
    address public constant PROFIT_WALLET = ******************************************;
    
    // 💰 STRATEGY PARAMETERS (SAME AS WORKING CONTRACT)
    uint256 public constant FLASH_AMOUNT = 10000e6;   // $10,000 USDC
    uint256 public constant SUPPLY_AMOUNT = 8000e6;   // $8,000 USDC supply
    uint8 public constant EMODE_CATEGORY = 1;         // Stablecoins eMode
    uint256 public constant SAFETY_MARGIN = 85;       // 85% safety margin
    
    // 📊 TRACKING
    uint256 public totalExecutions;
    uint256 public lastProfit;
    bool public lastSuccess;
    uint256 public totalProfit;
    uint256 public lastFailureStep; // NEW: Track where failure occurs
    
    // 🎯 DETAILED DEBUG EVENTS
    event DebugStep(string step, uint256 value);
    event DebugString(string step, string value);
    event DebugAddress(string step, address value);
    event DebugBool(string step, bool value);
    event FlashLoanReceived(uint256 amount, uint256 fee);
    event EmodeSet(uint8 category);
    event SupplyCompleted(address asset, uint256 amount);
    event BorrowCompleted(address asset, uint256 amount);
    event WithdrawCompleted(address asset, uint256 amount);
    event RepayCompleted(address asset, uint256 amount);
    event ProfitExtracted(uint256 profit, address wallet, uint256 execution);
    event FailurePoint(uint256 step, string description); // NEW: Failure tracking
    
    /**
     * 🚀 EXECUTE DEBUG FLASH LOAN (SAME FUNCTION NAME)
     */
    function executeArbitrumAaveFlashLoan() external {
        emit DebugStep("Starting flash loan execution", block.timestamp);
        emit DebugAddress("Caller", msg.sender);
        emit DebugStep("Flash amount", FLASH_AMOUNT);
        
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        
        tokens[0] = address(USDC);
        amounts[0] = FLASH_AMOUNT;
        
        emit DebugStep("About to call Balancer flashLoan", 1);
        
        try BALANCER.flashLoan(address(this), tokens, amounts, "") {
            emit DebugStep("Balancer flashLoan call succeeded", 2);
        } catch Error(string memory reason) {
            emit DebugString("Balancer flashLoan failed", reason);
            emit FailurePoint(1, "Balancer flashLoan call failed");
            revert(reason);
        } catch {
            emit DebugString("Balancer flashLoan failed", "Unknown error");
            emit FailurePoint(1, "Balancer flashLoan unknown error");
            revert("Balancer flashLoan failed");
        }
    }
    
    /**
     * 🎯 FLASH LOAN CALLBACK WITH EXTENSIVE DEBUGGING
     */
    function receiveFlashLoan(
        address[] memory,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory
    ) external override {
        emit DebugStep("receiveFlashLoan called", 10);
        emit DebugAddress("msg.sender", msg.sender);
        emit DebugAddress("BALANCER address", address(BALANCER));
        
        require(msg.sender == address(BALANCER), "Only Balancer");
        emit DebugStep("Balancer sender check passed", 11);
        
        uint256 flashAmount = amounts[0];
        uint256 initialBalance = USDC.balanceOf(address(this));
        
        emit DebugStep("Flash amount received", flashAmount);
        emit DebugStep("Initial USDC balance", initialBalance);
        emit DebugStep("Fee amount", feeAmounts[0]);
        emit FlashLoanReceived(flashAmount, feeAmounts[0]);
        
        // Verify flash loan (WORKING MECHANISM)
        require(initialBalance >= flashAmount, "Flash loan not received");
        emit DebugStep("Flash loan verification passed", 12);
        
        // Execute DEBUG AAVE STRATEGY
        emit DebugStep("About to execute Aave strategy", 20);
        
        try this.executeDebugAaveStrategy() {
            emit DebugStep("Aave strategy completed successfully", 21);
        } catch Error(string memory reason) {
            emit DebugString("Aave strategy failed", reason);
            emit FailurePoint(20, reason);
            revert(reason);
        } catch {
            emit DebugString("Aave strategy failed", "Unknown error");
            emit FailurePoint(20, "Aave strategy unknown error");
            revert("Aave strategy failed");
        }
        
        // Repay flash loan (including any fees)
        emit DebugStep("About to repay flash loan", 30);
        uint256 totalRepayment = flashAmount + feeAmounts[0];
        emit DebugStep("Total repayment amount", totalRepayment);
        
        uint256 balanceBeforeRepay = USDC.balanceOf(address(this));
        emit DebugStep("Balance before repay", balanceBeforeRepay);
        
        require(balanceBeforeRepay >= totalRepayment, "Insufficient balance for repayment");
        
        USDC.transfer(address(BALANCER), totalRepayment);
        emit DebugStep("Flash loan repaid", totalRepayment);
        
        // Extract profit
        uint256 finalBalance = USDC.balanceOf(address(this));
        emit DebugStep("Final balance after repay", finalBalance);
        
        totalExecutions++;
        
        if (finalBalance > 0) {
            lastProfit = finalBalance;
            totalProfit += finalBalance;
            USDC.transfer(PROFIT_WALLET, finalBalance);
            lastSuccess = true;
            emit ProfitExtracted(finalBalance, PROFIT_WALLET, totalExecutions);
            emit DebugStep("Profit extracted", finalBalance);
        } else {
            lastProfit = 0;
            lastSuccess = false;
            emit DebugStep("No profit generated", 0);
        }
        
        emit DebugStep("Flash loan execution completed", 99);
    }
    
    /**
     * 💰 DEBUG AAVE STRATEGY WITH STEP-BY-STEP LOGGING
     */
    function executeDebugAaveStrategy() external {
        require(msg.sender == address(this), "Only self");
        emit DebugStep("executeDebugAaveStrategy started", 100);
        
        // Step 1: Enable eMode for maximum leverage (97% LTV)
        emit DebugStep("About to set eMode", 101);
        emit DebugStep("eMode category", EMODE_CATEGORY);
        
        try AAVE_POOL.setUserEMode(EMODE_CATEGORY) {
            emit DebugStep("eMode set successfully", 102);
            emit EmodeSet(EMODE_CATEGORY);
        } catch Error(string memory reason) {
            emit DebugString("setUserEMode failed", reason);
            emit FailurePoint(101, reason);
            revert(reason);
        } catch {
            emit DebugString("setUserEMode failed", "Unknown error");
            emit FailurePoint(101, "setUserEMode unknown error");
            revert("setUserEMode failed");
        }
        
        // Step 2: Supply USDC as collateral
        emit DebugStep("About to approve USDC for supply", 110);
        emit DebugStep("Supply amount", SUPPLY_AMOUNT);
        
        try USDC.approve(address(AAVE_POOL), type(uint256).max) {
            emit DebugStep("USDC approval successful", 111);
        } catch Error(string memory reason) {
            emit DebugString("USDC approve failed", reason);
            emit FailurePoint(110, reason);
            revert(reason);
        } catch {
            emit DebugString("USDC approve failed", "Unknown error");
            emit FailurePoint(110, "USDC approve unknown error");
            revert("USDC approve failed");
        }
        
        emit DebugStep("About to supply USDC to Aave", 120);
        
        try AAVE_POOL.supply(address(USDC), SUPPLY_AMOUNT, address(this), 0) {
            emit DebugStep("USDC supply successful", 121);
            emit SupplyCompleted(address(USDC), SUPPLY_AMOUNT);
        } catch Error(string memory reason) {
            emit DebugString("USDC supply failed", reason);
            emit FailurePoint(120, reason);
            revert(reason);
        } catch {
            emit DebugString("USDC supply failed", "Unknown error");
            emit FailurePoint(120, "USDC supply unknown error");
            revert("USDC supply failed");
        }
        
        // Step 3: Get current account data
        emit DebugStep("About to get account data", 130);
        
        uint256 totalCollateral;
        uint256 totalDebt;
        uint256 availableBorrows;
        
        try AAVE_POOL.getUserAccountData(address(this)) returns (
            uint256 _totalCollateral,
            uint256 _totalDebt,
            uint256 _availableBorrows,
            uint256,
            uint256,
            uint256
        ) {
            totalCollateral = _totalCollateral;
            totalDebt = _totalDebt;
            availableBorrows = _availableBorrows;
            
            emit DebugStep("Account data retrieved successfully", 131);
            emit DebugStep("Total collateral", totalCollateral);
            emit DebugStep("Available borrows", availableBorrows);
        } catch Error(string memory reason) {
            emit DebugString("getUserAccountData failed", reason);
            emit FailurePoint(130, reason);
            revert(reason);
        } catch {
            emit DebugString("getUserAccountData failed", "Unknown error");
            emit FailurePoint(130, "getUserAccountData unknown error");
            revert("getUserAccountData failed");
        }
        
        // Step 4: Calculate CORRECTED borrow amount based on ACTUAL eMode LTV
        emit DebugStep("About to calculate CORRECTED borrow amount", 140);

        // CRITICAL FIX: eMode category 1 on Arbitrum has 32% LTV, not 93%!
        // Available borrows = $8,000 * 32% = $2,560 (in 6 decimals)
        uint256 actualMaxBorrow = (SUPPLY_AMOUNT * 32) / 100; // 32% of supply amount
        uint256 safeBorrowAmount = (actualMaxBorrow * SAFETY_MARGIN) / 100; // Apply safety margin
        uint256 usdcBorrowAmount = safeBorrowAmount; // Already in 6 decimals

        emit DebugStep("Actual max borrow (32% LTV)", actualMaxBorrow);
        emit DebugStep("Safe borrow amount (85% of max)", safeBorrowAmount);
        emit DebugStep("CORRECTED USDC borrow amount", usdcBorrowAmount);

        // Validate against available borrows from Aave
        uint256 availableBorrowsUsdc = availableBorrows / 100; // Convert 8 decimals to 6 decimals
        emit DebugStep("Available borrows from Aave", availableBorrowsUsdc);

        if (usdcBorrowAmount > availableBorrowsUsdc) {
            usdcBorrowAmount = (availableBorrowsUsdc * 90) / 100; // Use 90% of available
            emit DebugStep("Adjusted to available borrows", usdcBorrowAmount);
        }
        
        // Step 5: CORRECTED BORROWING (FIXED VERSION)
        emit DebugStep("Starting CORRECTED borrowing", 150);
        emit DebugStep("Corrected borrow amount", usdcBorrowAmount);

        // Use the corrected calculated amount (should be ~$2,176 with 32% LTV)
        uint256 finalBorrowAmount = usdcBorrowAmount;

        // Only borrow if amount is reasonable and we have sufficient available borrows
        if (finalBorrowAmount > 1e6 && finalBorrowAmount < 5000e6 && availableBorrows > (finalBorrowAmount * 100)) { // Convert to 8 decimals for comparison
            emit DebugStep("Sufficient borrows available", availableBorrows);
            emit DebugStep("Final borrow amount", finalBorrowAmount);

            try AAVE_POOL.borrow(address(USDC), finalBorrowAmount, 2, 0, address(this)) {
                emit DebugStep("CORRECTED USDC borrow successful", 151);
                emit BorrowCompleted(address(USDC), finalBorrowAmount);

                // Immediately repay the corrected borrow
                emit DebugStep("About to repay corrected borrow", 152);

                try USDC.approve(address(AAVE_POOL), type(uint256).max) {
                    emit DebugStep("USDC approval for repay successful", 153);
                } catch Error(string memory reason) {
                    emit DebugString("USDC approve for repay failed", reason);
                    emit FailurePoint(152, reason);
                    revert(reason);
                }

                try AAVE_POOL.repay(address(USDC), finalBorrowAmount, 2, address(this)) {
                    emit DebugStep("CORRECTED USDC repay successful", 154);
                    emit RepayCompleted(address(USDC), finalBorrowAmount);
                } catch Error(string memory reason) {
                    emit DebugString("Conservative USDC repay failed", reason);
                    emit FailurePoint(153, reason);
                    revert(reason);
                } catch {
                    emit DebugString("CORRECTED USDC repay failed", "Unknown error");
                    emit FailurePoint(153, "CORRECTED USDC repay unknown error");
                    revert("CORRECTED USDC repay failed");
                }

            } catch Error(string memory reason) {
                emit DebugString("CORRECTED USDC borrow failed", reason);
                emit FailurePoint(150, reason);
                // DON'T REVERT - continue without borrowing to preserve working strategy
                emit DebugString("Continuing without borrowing", "Preserving working strategy");
            } catch {
                emit DebugString("CORRECTED USDC borrow failed", "Unknown error");
                emit FailurePoint(150, "CORRECTED USDC borrow unknown error");
                // DON'T REVERT - continue without borrowing to preserve working strategy
                emit DebugString("Continuing without borrowing", "Preserving working strategy");
            }
        } else {
            emit DebugStep("Borrow amount invalid or insufficient borrows", finalBorrowAmount);
            emit DebugString("Borrowing skipped", "Invalid amount or insufficient available borrows");
        }
        
        // Step 7: Withdraw all collateral
        emit DebugStep("About to withdraw collateral", 170);
        
        try AAVE_POOL.withdraw(address(USDC), SUPPLY_AMOUNT, address(this)) {
            emit DebugStep("USDC withdraw successful", 171);
            emit WithdrawCompleted(address(USDC), SUPPLY_AMOUNT);
        } catch Error(string memory reason) {
            emit DebugString("USDC withdraw failed", reason);
            emit FailurePoint(170, reason);
            revert(reason);
        } catch {
            emit DebugString("USDC withdraw failed", "Unknown error");
            emit FailurePoint(170, "USDC withdraw unknown error");
            revert("USDC withdraw failed");
        }
        
        uint256 finalBalance = USDC.balanceOf(address(this));
        emit DebugStep("Debug Aave strategy completed", finalBalance);
    }
    
    /**
     * 📊 GET EXECUTION STATS (SAME AS ORIGINAL)
     */
    function getExecutionStats() external view returns (
        uint256 lastProfitAmount,
        bool lastExecutionSuccess,
        uint256 totalExecutionCount,
        uint256 totalProfits,
        uint256 lastFailureStepNumber
    ) {
        return (lastProfit, lastSuccess, totalExecutions, totalProfit, lastFailureStep);
    }
    
    function getArbitrumInfo() external view returns (
        address usdc,
        address weth,
        address balancer,
        address aavePool,
        address oracle
    ) {
        return (
            address(USDC),
            address(WETH),
            address(BALANCER),
            address(AAVE_POOL),
            address(0) // No oracle used
        );
    }
}
