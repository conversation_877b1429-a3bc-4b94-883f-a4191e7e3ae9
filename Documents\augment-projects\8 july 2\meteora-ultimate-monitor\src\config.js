// 🚀 METEORA ULTIMATE DLMM MONITOR CONFIGURATION
// The most comprehensive DLMM monitoring system ever built

export const CONFIG = {
  // 🎯 API ENDPOINTS - All Meteora data sources
  APIS: {
    DLMM_MAIN: 'https://dlmm-api.meteora.ag',
    DLMM_STAGING: 'https://mainnet-staging.dlmm-api.meteora.ag',
    UNIVERSAL_SEARCH: 'https://universal-search-api.meteora.ag',
    DEXSCREENER_ADAPTER: 'http://dexscreener-adapter.meteora.ag',
    DEFILLAMA: 'https://api.llama.fi',
    SOLANA_COMPASS: 'https://api.solanacompass.com'
  },

  // 🔥 MONITORING SETTINGS - Ultra-aggressive monitoring
  MONITORING: {
    UPDATE_INTERVAL: 5000,        // 5 seconds - ultra fast updates
    CONCENTRATION_THRESHOLD: 0.001, // 0.001% fee/TVL ratio minimum (very permissive)
    MIN_VOLUME_24H: 100,          // $100 minimum daily volume (very low threshold)
    MIN_TVL: 100,                 // $100 minimum TVL (very low threshold)
    MAX_POOLS_TO_TRACK: 500,      // Track top 500 pools
    HONEY_DETECTION_WINDOW: 300,  // 5 minutes for honey detection
    PROFIT_ALERT_THRESHOLD: 1     // Alert when pool generates $1+ fees/hour (very permissive)
  },

  // 📊 RANKING CRITERIA - What makes a pool "honey"
  RANKING_WEIGHTS: {
    FEE_TVL_RATIO: 0.35,          // 35% - Most important metric
    VOLUME_VELOCITY: 0.25,        // 25% - Trading intensity
    FEE_GENERATION_RATE: 0.20,    // 20% - Fees per hour
    LIQUIDITY_EFFICIENCY: 0.15,   // 15% - How well liquidity works
    VOLATILITY_BONUS: 0.05        // 5% - Volatility creates higher fees
  },

  // 🎨 DISPLAY SETTINGS
  DISPLAY: {
    TOP_POOLS_COUNT: 10,
    REFRESH_RATE: 2000,           // 2 seconds dashboard refresh
    COLORS: {
      HONEY: '#FFD700',           // Gold for honey pools
      PROFIT: '#00FF00',          // Green for profitable
      WARNING: '#FFA500',         // Orange for warnings
      DANGER: '#FF0000',          // Red for losses
      INFO: '#00BFFF'             // Blue for info
    }
  },

  // 🚨 ALERT SETTINGS - Never miss an opportunity
  ALERTS: {
    CONCENTRATION_SPIKE: true,    // Alert on fee concentration spikes
    NEW_HONEY_POOL: true,         // Alert when new honey pool detected
    VOLUME_SURGE: true,           // Alert on volume surges
    FEE_RATE_INCREASE: true,      // Alert when fees increase
    TVL_CHANGES: true             // Alert on significant TVL changes
  },

  // 🔍 ANALYSIS SETTINGS
  ANALYSIS: {
    HISTORICAL_WINDOW: 24,        // 24 hours of historical data
    TREND_ANALYSIS_POINTS: 12,    // 12 data points for trend analysis
    VOLATILITY_WINDOW: 60,        // 1 hour volatility calculation
    EFFICIENCY_CALCULATION: 'advanced' // Use advanced efficiency metrics
  }
};

// 🎯 POOL CATEGORIES - Different types of honey
export const POOL_CATEGORIES = {
  ULTRA_HONEY: {
    name: 'ULTRA HONEY 🍯🔥',
    criteria: {
      feeToTvlRatio: 2.0,         // 2%+ fee/TVL ratio
      volume24h: 100000,          // $100k+ volume
      feesPerHour: 500            // $500+ fees per hour
    }
  },
  PREMIUM_HONEY: {
    name: 'PREMIUM HONEY 🍯⭐',
    criteria: {
      feeToTvlRatio: 1.0,         // 1%+ fee/TVL ratio
      volume24h: 50000,           // $50k+ volume
      feesPerHour: 200            // $200+ fees per hour
    }
  },
  GOOD_HONEY: {
    name: 'GOOD HONEY 🍯✨',
    criteria: {
      feeToTvlRatio: 0.1,         // 0.1%+ fee/TVL ratio (more realistic)
      volume24h: 5000,            // $5k+ volume (lower threshold)
      feesPerHour: 20             // $20+ fees per hour (more realistic)
    }
  },
  POTENTIAL_HONEY: {
    name: 'POTENTIAL HONEY 🍯📈',
    criteria: {
      feeToTvlRatio: 0.01,        // 0.01%+ fee/TVL ratio (very permissive)
      volume24h: 100,             // $100+ volume (very low threshold)
      feesPerHour: 1              // $1+ fees per hour (very permissive)
    }
  }
};

// 🚀 PERFORMANCE METRICS - Track everything
export const METRICS = {
  CORE: [
    'fee_to_tvl_ratio',
    'volume_24h',
    'fees_24h',
    'tvl',
    'dynamic_fee_rate',
    'liquidity_utilization'
  ],
  ADVANCED: [
    'volume_velocity',
    'fee_generation_rate',
    'volatility_index',
    'efficiency_score',
    'concentration_index',
    'profit_potential'
  ],
  REAL_TIME: [
    'current_fee_rate',
    'active_liquidity',
    'trading_activity',
    'price_impact',
    'slippage_tolerance'
  ]
};

export default CONFIG;
