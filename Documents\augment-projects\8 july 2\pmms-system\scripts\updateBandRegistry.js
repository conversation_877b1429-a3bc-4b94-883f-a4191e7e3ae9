// scripts/updateBandRegistry.js
const { ethers } = require("hardhat");

async function main() {
  const bandRegistry = await ethers.getContractAt("BandFeedRegistry", "******************************************");
  const proxyAddress = "INSERT_PROXY_ADDRESS_HERE";
  await bandRegistry.setFeedAddress("XAUUSD", proxyAddress);
  await bandRegistry.setFeedAddress("ETHUSD", proxyAddress);
  console.log(`Updated BandFeedRegistry with XAUUSD and ETHUSD feeds`);
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});