const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("Soulbound Identity", function () {
  it("should mint a Soulbound Token", async function () {
    const soulbound = await ethers.getContractAt("ZiGSoulboundToken", "******************************************");
    const balance = await soulbound.balanceOf("******************************************");
    expect(balance).to.equal(0);
  });
});
