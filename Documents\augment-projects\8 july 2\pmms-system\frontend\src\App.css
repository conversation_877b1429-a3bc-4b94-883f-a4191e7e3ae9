.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.dashboard-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.execute-strategy,
.register-strategy,
.strategy-list {
  border: 1px solid #ccc;
  padding: 20px;
  border-radius: 8px;
}

h1, h2 {
  text-align: center;
}

form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

form div {
  display: flex;
  flex-direction: column;
}

input, button {
  padding: 8px;
  margin-top: 5px;
}

button {
  background-color: #007bff;
  color: gray;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.error {
  color: red;
  text-align: center;
}

ul {
  list-style: none;
  padding: 0;
}

li {
  margin: 10px 0;
}