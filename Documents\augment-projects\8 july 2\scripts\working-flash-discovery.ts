import { ethers } from 'ethers';
import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import dotenv from 'dotenv';

dotenv.config();

interface FlashLoanOpportunity {
  txHash: string;
  blockNumber: number;
  chainId: number;
  strategy: string;
  netProfitUSD: number;
  gasUsedUSD: number;
  timestamp: number;
  isReplicable: boolean;
}

class WorkingFlashDiscovery {
  private arbitrumProvider: ethers.providers.JsonRpcProvider;
  private polygonProvider: ethers.providers.JsonRpcProvider;
  private opportunities: FlashLoanOpportunity[] = [];
  private isRunning = false;

  // Flash loan event signatures
  private readonly AAVE_FLASH_LOAN = '0x631042c832b07452973831137f2d73e395028b44b250dedc5abb0ee766e168ac';
  private readonly BALANCER_FLASH_LOAN = '0x0d7d75e01ab95780d3cd1c8ec0dd6c2ce19e3a20427eec8bf53283b6fb8e95f0';

  constructor() {
    const alchemyKey = process.env.ALCHEMY_API_KEY;
    if (!alchemyKey) {
      throw new Error('ALCHEMY_API_KEY not found in environment variables');
    }

    this.arbitrumProvider = new ethers.providers.JsonRpcProvider(`https://arb-mainnet.g.alchemy.com/v2/${alchemyKey}`);
    this.polygonProvider = new ethers.providers.JsonRpcProvider(`https://polygon-mainnet.g.alchemy.com/v2/${alchemyKey}`);
  }

  async start() {
    console.log('🚀 STARTING WORKING FLASH LOAN DISCOVERY SYSTEM');
    console.log('=' .repeat(60));
    
    this.isRunning = true;

    // Start monitoring both networks
    this.monitorNetwork(this.arbitrumProvider, 42161, 'Arbitrum');
    this.monitorNetwork(this.polygonProvider, 137, 'Polygon');

    // Report every 2 minutes
    setInterval(() => {
      this.generateReport();
    }, 120000);

    console.log('✅ Discovery system is now running!');
    console.log('📊 Monitoring Arbitrum and Polygon for flash loan opportunities');
    console.log('🛑 Press Ctrl+C to stop\n');

    // Graceful shutdown
    process.on('SIGINT', () => {
      this.stop();
    });
  }

  private async monitorNetwork(provider: ethers.providers.JsonRpcProvider, chainId: number, networkName: string) {
    console.log(`📡 Starting ${networkName} monitoring...`);

    provider.on('block', async (blockNumber) => {
      if (!this.isRunning) return;

      try {
        await this.analyzeBlock(provider, blockNumber, chainId, networkName);
      } catch (error) {
        console.error(`❌ Error analyzing block ${blockNumber} on ${networkName}:`, error);
      }
    });
  }

  private async analyzeBlock(provider: ethers.providers.JsonRpcProvider, blockNumber: number, chainId: number, networkName: string) {
    try {
      const block = await provider.getBlock(blockNumber);
      if (!block || !block.transactions) return;

      console.log(`🔍 Analyzing block ${blockNumber} on ${networkName} (${block.transactions.length} transactions)`);

      // Check each transaction for flash loans
      for (const txHash of block.transactions) {
        if (typeof txHash === 'string') {
          try {
            const receipt = await provider.getTransactionReceipt(txHash);
            if (receipt && receipt.status === 1) {
              const opportunity = await this.analyzeTransaction(txHash, receipt, chainId, networkName);
              if (opportunity) {
                this.handleOpportunityFound(opportunity);
              }
            }
          } catch (error) {
            // Skip individual transaction errors
          }
        }
      }
    } catch (error) {
      console.error(`❌ Error analyzing block ${blockNumber}:`, error);
    }
  }

  private async analyzeTransaction(txHash: string, receipt: any, chainId: number, networkName: string): Promise<FlashLoanOpportunity | null> {
    // Check for flash loan events
    const hasFlashLoan = receipt.logs.some((log: any) => 
      log.topics[0] === this.AAVE_FLASH_LOAN || 
      log.topics[0] === this.BALANCER_FLASH_LOAN
    );

    if (!hasFlashLoan) return null;

    console.log(`💰 Flash loan detected: ${txHash} on ${networkName}`);

    try {
      // Get transaction details
      const tx = await (chainId === 42161 ? this.arbitrumProvider : this.polygonProvider).getTransaction(txHash);
      if (!tx) return null;

      // Calculate gas cost
      const gasUsed = receipt.gasUsed;
      const gasPrice = tx.gasPrice || ethers.BigNumber.from(0);
      const gasCostWei = gasUsed.mul(gasPrice);
      const gasCostETH = parseFloat(ethers.utils.formatEther(gasCostWei));
      
      // Get ETH price (simplified)
      const ethPriceUSD = await this.getETHPrice();
      const gasUsedUSD = gasCostETH * ethPriceUSD;

      // Estimate profit (simplified - in reality you'd analyze token transfers)
      const estimatedProfitUSD = this.estimateProfit(receipt.logs, gasUsedUSD);
      const netProfitUSD = estimatedProfitUSD - gasUsedUSD;

      // Only consider profitable transactions
      if (netProfitUSD < 10) return null;

      const opportunity: FlashLoanOpportunity = {
        txHash,
        blockNumber: receipt.blockNumber,
        chainId,
        strategy: this.identifyStrategy(receipt.logs),
        netProfitUSD,
        gasUsedUSD,
        timestamp: Date.now(),
        isReplicable: netProfitUSD > 20 && gasUsedUSD < netProfitUSD * 0.3
      };

      return opportunity;
    } catch (error) {
      console.error(`❌ Error analyzing transaction ${txHash}:`, error);
      return null;
    }
  }

  private estimateProfit(logs: any[], gasUsedUSD: number): number {
    // Simplified profit estimation
    // In reality, you'd analyze all ERC20 transfers to calculate exact profit
    
    // Look for large value transfers as a proxy for profit
    let estimatedProfit = 0;
    
    for (const log of logs) {
      // ERC20 Transfer event signature
      if (log.topics[0] === '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef') {
        try {
          const amount = ethers.BigNumber.from(log.data);
          const amountETH = parseFloat(ethers.utils.formatEther(amount));
          
          // If it's a significant transfer, estimate it as potential profit
          if (amountETH > 0.01) {
            estimatedProfit += amountETH * 2000; // Rough USD conversion
          }
        } catch (error) {
          // Skip invalid transfers
        }
      }
    }

    // Return a reasonable estimate
    return Math.min(estimatedProfit * 0.01, 500); // Max 1% of transfers, cap at $500
  }

  private identifyStrategy(logs: any[]): string {
    // Simplified strategy identification
    const transferCount = logs.filter(log => 
      log.topics[0] === '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef'
    ).length;

    if (transferCount > 10) return 'COMPLEX_ARBITRAGE';
    if (transferCount > 5) return 'DEX_ARBITRAGE';
    if (transferCount > 2) return 'SIMPLE_ARBITRAGE';
    return 'UNKNOWN';
  }

  private async getETHPrice(): Promise<number> {
    try {
      const response = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd', {
        timeout: 5000
      });
      return response.data.ethereum.usd;
    } catch {
      return 2000; // Fallback price
    }
  }

  private handleOpportunityFound(opportunity: FlashLoanOpportunity) {
    this.opportunities.push(opportunity);
    
    console.log(`\n🎯 PROFITABLE OPPORTUNITY FOUND!`);
    console.log(`   TX: ${opportunity.txHash}`);
    console.log(`   Chain: ${opportunity.chainId === 42161 ? 'Arbitrum' : 'Polygon'}`);
    console.log(`   Strategy: ${opportunity.strategy}`);
    console.log(`   Net Profit: $${opportunity.netProfitUSD.toFixed(2)}`);
    console.log(`   Gas Cost: $${opportunity.gasUsedUSD.toFixed(2)}`);
    console.log(`   Replicable: ${opportunity.isReplicable ? '✅ YES' : '❌ NO'}`);
    console.log('');

    // Save to file
    this.saveOpportunity(opportunity);

    // Alert for high-profit opportunities
    if (opportunity.netProfitUSD > 50 && opportunity.isReplicable) {
      console.log('🚨🚨🚨 HIGH-PROFIT REPLICABLE OPPORTUNITY! 🚨🚨🚨');
      console.log(`💰 Profit: $${opportunity.netProfitUSD.toFixed(2)}`);
      console.log(`🔗 TX: ${opportunity.txHash}`);
      console.log('🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨');
    }
  }

  private saveOpportunity(opportunity: FlashLoanOpportunity) {
    try {
      const discoveriesDir = path.join(__dirname, '../discoveries');
      if (!fs.existsSync(discoveriesDir)) {
        fs.mkdirSync(discoveriesDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `opportunity-${timestamp}.json`;
      const filepath = path.join(discoveriesDir, filename);

      fs.writeFileSync(filepath, JSON.stringify(opportunity, null, 2));
      console.log(`💾 Saved: ${filename}`);
    } catch (error) {
      console.error('❌ Error saving opportunity:', error);
    }
  }

  private generateReport() {
    if (this.opportunities.length === 0) {
      console.log('\n📊 No opportunities found yet...');
      return;
    }

    console.log('\n📊 DISCOVERY REPORT');
    console.log('=' .repeat(40));
    console.log(`Total Opportunities: ${this.opportunities.length}`);
    
    const recentOpps = this.opportunities.filter(opp => 
      Date.now() - opp.timestamp < 600000 // Last 10 minutes
    );
    console.log(`Recent (10min): ${recentOpps.length}`);

    const replicableOpps = this.opportunities.filter(opp => opp.isReplicable);
    console.log(`Replicable: ${replicableOpps.length}`);

    if (this.opportunities.length > 0) {
      const avgProfit = this.opportunities.reduce((sum, opp) => sum + opp.netProfitUSD, 0) / this.opportunities.length;
      const maxProfit = Math.max(...this.opportunities.map(opp => opp.netProfitUSD));
      
      console.log(`Average Profit: $${avgProfit.toFixed(2)}`);
      console.log(`Max Profit: $${maxProfit.toFixed(2)}`);
    }

    // Strategy breakdown
    const strategies = new Map<string, number>();
    this.opportunities.forEach(opp => {
      strategies.set(opp.strategy, (strategies.get(opp.strategy) || 0) + 1);
    });

    console.log('\n🎯 Strategy Breakdown:');
    Array.from(strategies.entries())
      .sort((a, b) => b[1] - a[1])
      .forEach(([strategy, count]) => {
        console.log(`  ${strategy}: ${count}`);
      });

    console.log('=' .repeat(40));
  }

  private stop() {
    console.log('\n🛑 Stopping discovery system...');
    this.isRunning = false;
    
    // Final report
    console.log('\n📈 FINAL REPORT');
    console.log('=' .repeat(50));
    console.log(`Total Opportunities Found: ${this.opportunities.length}`);
    console.log(`Replicable Opportunities: ${this.opportunities.filter(opp => opp.isReplicable).length}`);
    
    if (this.opportunities.length > 0) {
      const totalProfit = this.opportunities.reduce((sum, opp) => sum + opp.netProfitUSD, 0);
      console.log(`Total Potential Profit: $${totalProfit.toFixed(2)}`);
      
      const bestOpp = this.opportunities.reduce((best, current) => 
        current.netProfitUSD > best.netProfitUSD ? current : best
      );
      console.log(`Best Opportunity: $${bestOpp.netProfitUSD.toFixed(2)} (${bestOpp.txHash})`);
    }
    
    console.log('\n💡 Check ./discoveries/ folder for detailed data');
    console.log('🚀 Discovery session complete!');
    
    process.exit(0);
  }
}

// Start the discovery system
async function main() {
  try {
    const discovery = new WorkingFlashDiscovery();
    await discovery.start();
  } catch (error) {
    console.error('❌ Failed to start discovery system:', error);
    process.exit(1);
  }
}

main().catch(console.error);
