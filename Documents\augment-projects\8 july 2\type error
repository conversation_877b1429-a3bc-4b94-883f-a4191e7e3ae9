Microsoft Windows [Version 10.0.26100.4652]
(c) Microsoft Corporation. All rights reserved.

C:\Users\<USER>\Users\mm1nc\Documents\augment-projects\8 july 2"
'"C:\Users\<USER>\Documents\augment-projects\8 july 2"' is not recognized as an internal or external command,
operable program or batch file.

C:\Users\<USER>\Users\mm1nc\Documents\augment-projects\8 july 2"

C:\Users\<USER>\Documents\augment-projects\8 july 2>npm run build

> flash-loan-scout@1.0.0 build
> tsc

src/analysis/BatchAnalysisEngine.ts:465:11 - error TS2739: Type '{ 1: number; 42161: number; 10: number; 137: number; 8453: number; 11155111: number; }' is missing the following properties from type 'Record<ChainId, number>': 11155420, 421614, 84532, 80001

465     const competitionLevels: Record<ChainId, number> = {
              ~~~~~~~~~~~~~~~~~

src/analysis/extract-discoveries.ts:53:13 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

53             confidence: 0.5,
               ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:88:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

88       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: RiskLevel.HIGH }
                                                 ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:100:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

100       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 232, riskLevel: RiskLevel.HIGH }
                                                  ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:112:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

112       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 211, riskLevel: RiskLevel.HIGH }
                                                  ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:124:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

124       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 340, riskLevel: RiskLevel.HIGH }
                                                  ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:136:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

136       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: RiskLevel.HIGH }
                                                  ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:148:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

148       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1082, riskLevel: RiskLevel.HIGH }
                                                  ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:160:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

160       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'Low-profit pattern', estimatedCapital: 140, riskLevel: RiskLevel.HIGH }
                                                  ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:172:123 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

172       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }

      ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:184:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

184       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 351, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:184:114 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

184       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 351, riskLevel: 'HIGH' }
                                                                                                                     ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:196:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

196       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:196:112 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

196       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
                                                                                                                   ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:208:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

208       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:208:112 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

208       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
                                                                                                                   ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:220:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

220       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 406, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:220:114 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

220       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 406, riskLevel: 'HIGH' }
                                                                                                                     ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:232:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

232       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:232:113 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

232       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: 'HIGH' }
                                                                                                                    ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:244:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

244       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:244:112 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

244       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
                                                                                                                   ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:256:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

256       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:256:113 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

256       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: 'HIGH' }
                                                                                                                    ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:268:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

268       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 420, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:268:114 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

268       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 420, riskLevel: 'HIGH' }
                                                                                                                     ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/cli/research-cli.ts:78:46 - error TS18046: 'error' is of type 'unknown'.

78       console.error(chalk.red('\n❌ Error:'), error.message);
                                                ~~~~~

src/cli/research-cli.ts:139:46 - error TS18046: 'error' is of type 'unknown'.

139       console.error(chalk.red('\n❌ Error:'), error.message);
                                                 ~~~~~

src/cli/research-cli.ts:176:46 - error TS18046: 'error' is of type 'unknown'.

176       console.error(chalk.red('\n❌ Error:'), error.message);
                                                 ~~~~~

src/cli/research-cli.ts:202:46 - error TS18046: 'error' is of type 'unknown'.

202       console.error(chalk.red('\n❌ Error:'), error.message);
                                                 ~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:42:36 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

42   private arbitrumProvider: ethers.providers.JsonRpcProvider;
                                      ~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:43:35 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

43   private polygonProvider: ethers.providers.JsonRpcProvider;
                                     ~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:74:40 - error TS2339: Property 'providers' does not exist on type 'typeof import("C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers")'.

74     this.arbitrumProvider = new ethers.providers.JsonRpcProvider(config.arbitrumRpcUrl);
                                          ~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:75:39 - error TS2339: Property 'providers' does not exist on type 'typeof import("C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers")'.

75     this.polygonProvider = new ethers.providers.JsonRpcProvider(config.polygonRpcUrl);
                                         ~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:100:49 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

100   private async monitorNetwork(provider: ethers.providers.JsonRpcProvider, chainId: number, networkName: string): Promise<void> {
                                                    ~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:104:33 - error TS7006: Parameter 'blockNumber' implicitly has an 'any' type.

104     provider.on('block', async (blockNumber) => {
                                    ~~~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:114:37 - error TS7006: Parameter 'txHash' implicitly has an 'any' type.

114       provider.on('pending', async (txHash) => {
                                        ~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:127:47 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

127   private async analyzeBlock(provider: ethers.providers.JsonRpcProvider, blockNumber: number, chainId: number, networkName: string): Promise<void> {
                                                  ~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:135:62 - error TS7006: Parameter 'tx' implicitly has an 'any' type.

135       const analysisPromises = block.transactions.map(async (tx) => {
                                                                 ~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:168:49 - error TS7006: Parameter 'log' implicitly has an 'any' type.

168     const flashLoanEvents = receipt.logs.filter(log =>
                                                    ~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:186:13 - error TS2375: Type '{ txHash: any; blockNumber: any; chainId: number; strategy: string; flashLoanProvider: string; borrowedAssets: { token: string; amount: bigint; symbol: string; valueUSD: number; }[]; netProfitUSD: number; ... 8 more ...; riskLevel: "LOW" | ... 1 more ... | "HIGH"; }' is not assignable to type 'FlashLoanOpportunity' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'liquidationTarget' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.

186       const opportunity: FlashLoanOpportunity = {
                ~~~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:268:29 - error TS7006: Parameter 'log' implicitly has an 'any' type.

268       if (receipt.logs.some(log => log.address.toLowerCase() === address.toLowerCase())) {
                                ~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:274:5 - error TS2375: Type '{ type: string; provider: string; dexesUsed: string[]; liquidationTarget: string | undefined; mevType: string | undefined; }' is not assignable to type '{ type: string; provider: string; dexesUsed: string[]; liquidationTarget?: string; mevType?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'liquidationTarget' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.

274     return {
        ~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:423:60 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

423   private async analyzePendingTransaction(provider: ethers.providers.JsonRpcProvider, txHash: string, chainId: number): Promise<void> {
                                                               ~~~~~~~~~

src/monitoring/OpportunityScanner.ts:7:22 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

7     arbitrum: ethers.providers.JsonRpcProvider;
                       ~~~~~~~~~

src/monitoring/OpportunityScanner.ts:8:21 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

8     polygon: ethers.providers.JsonRpcProvider;
                      ~~~~~~~~~

src/monitoring/OpportunityScanner.ts:164:56 - error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.

164         const prices = await this.getPricesAcrossDEXes(tokenA, tokenB, chainId);
                                                           ~~~~~~

src/monitoring/OpportunityScanner.ts:165:68 - error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.

165         const arbitrageOpp = this.findArbitrageOpportunity(prices, tokenA, tokenB, chainId);
                                                                       ~~~~~~

src/monitoring/OpportunityScanner.ts:217:29 - error TS18048: 'highestPrice' is possibly 'undefined'.

217     const priceDifference = highestPrice.price - lowestPrice.price;
                                ~~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:217:50 - error TS18048: 'lowestPrice' is possibly 'undefined'.

217     const priceDifference = highestPrice.price - lowestPrice.price;
                                                     ~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:218:49 - error TS18048: 'lowestPrice' is possibly 'undefined'.

218     const profitPercentage = (priceDifference / lowestPrice.price) * 100;
                                                    ~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:221:27 - error TS18048: 'highestPrice' is possibly 'undefined'.

221     const totalSlippage = highestPrice.slippage + lowestPrice.slippage;
                              ~~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:221:51 - error TS18048: 'lowestPrice' is possibly 'undefined'.

221     const totalSlippage = highestPrice.slippage + lowestPrice.slippage;
                                                      ~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:241:17 - error TS18048: 'lowestPrice' is possibly 'undefined'.

241         buyDEX: lowestPrice.dex,
                    ~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:242:18 - error TS18048: 'highestPrice' is possibly 'undefined'.

242         sellDEX: highestPrice.dex,
                     ~~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:245:19 - error TS18048: 'lowestPrice' is possibly 'undefined'.

245         buyPrice: lowestPrice.price,
                      ~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:246:20 - error TS18048: 'highestPrice' is possibly 'undefined'.

246         sellPrice: highestPrice.price,
                       ~~~~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:68:28 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

68   private provider: ethers.providers.JsonRpcProvider;
                              ~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:103:32 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

103   constructor(provider: ethers.providers.JsonRpcProvider, chainId: number) {
                                   ~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:138:13 - error TS2375: Type '{ hash: any; blockNumber: any; from: any; to: any; value: any; gasUsed: number; gasPrice: any; status: any; flashLoanDetails: FlashLoanDetails | undefined; arbitrageDetails: ArbitrageDetails | undefined; liquidationDetails: LiquidationDetails | undefined; mevDetails: MEVDetails | undefined; profitAnalysis: ProfitAna...' is not assignable to type 'AnalyzedTransaction' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'flashLoanDetails' are incompatible.
    Type 'FlashLoanDetails | undefined' is not assignable to type 'FlashLoanDetails'.
      Type 'undefined' is not assignable to type 'FlashLoanDetails'.

138       const analyzedTx: AnalyzedTransaction = {
                ~~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:165:47 - error TS7006: Parameter 'log' implicitly has an 'any' type.

165     const flashLoanLogs = receipt.logs.filter(log =>
                                                  ~~~

src/monitoring/TransactionAnalyzer.ts:201:50 - error TS2532: Object is possibly 'undefined'.

201       const feePercentage = assets.length > 0 && assets[0].amount > 0
                                                     ~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:202:45 - error TS2532: Object is possibly 'undefined'.

202         ? Number(totalFee * BigInt(10000) / assets[0].amount) / 100
                                                ~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:222:49 - error TS7006: Parameter 'log' implicitly has an 'any' type.

222     const dexInteractions = receipt.logs.filter(log =>
                                                    ~~~

src/monitoring/TransactionAnalyzer.ts:230:44 - error TS7006: Parameter 'log' implicitly has an 'any' type.

230       const swapLogs = receipt.logs.filter(log => log.topics[0] === this.EVENT_SIGNATURES.SWAP);
                                               ~~~

src/monitoring/TransactionAnalyzer.ts:265:49 - error TS7006: Parameter 'log' implicitly has an 'any' type.

265     const liquidationLogs = receipt.logs.filter(log =>
                                                    ~~~

src/monitoring/TransactionAnalyzer.ts:299:52 - error TS7006: Parameter 't' implicitly has an 'any' type.

299       const txIndex = block.transactions.findIndex(t =>
                                                       ~

src/monitoring/TransactionAnalyzer.ts:320:29 - error TS7006: Parameter 'log' implicitly has an 'any' type.

320       if (receipt.logs.some(log => log.topics[0] === this.EVENT_SIGNATURES.LIQUIDATION_CALL)) {
                                ~~~

src/monitoring/TransactionAnalyzer.ts:422:9 - error TS2722: Cannot invoke an object which is possibly 'undefined'.

422         contract.symbol(),
            ~~~~~~~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:423:9 - error TS2722: Cannot invoke an object which is possibly 'undefined'.

423         contract.decimals()
            ~~~~~~~~~~~~~~~~~

src/monitoring/UltraEfficientMonitor.ts:56:7 - error TS2740: Type '{}' is missing the following properties from type 'Record<ChainId, number>': 1, 10, 137, 42161, and 6 more.

56       networkDistribution: {},
         ~~~~~~~~~~~~~~~~~~~

  src/monitoring/UltraEfficientMonitor.ts:30:3
    30   networkDistribution: Record<ChainId, number>;
         ~~~~~~~~~~~~~~~~~~~
    The expected type comes from property 'networkDistribution' which is declared here on type 'MonitoringStats'

src/monitoring/UltraEfficientMonitor.ts:174:14 - error TS7053: Element implicitly has an 'any' type because expression of type 'ChainId' can't be used to index type '{ 137: number; 8453: number; 42161: number; 10: number; 1: number; }'.
  Property '[ChainId.SEPOLIA]' does not exist on type '{ 137: number; 8453: number; 42161: number; 10: number; 1: number; }'.

174     score += networkBonus[transaction.chainId] || 0;
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/monitoring/UltraEfficientMonitor.ts:203:5 - error TS2740: Type '{}' is missing the following properties from type 'Record<ChainId, number>': 1, 10, 137, 42161, and 6 more.

203     this.stats.networkDistribution = {};
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/research/FlashLoanResearchEngine.ts:81:67 - error TS2339: Property 'providers' does not exist on type 'typeof import("C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers")'.

81         this.providers.set(Number(chainId) as ChainId, new ethers.providers.JsonRpcProvider(rpcUrl));
                                                                     ~~~~~~~~~

src/research/FlashLoanResearchEngine.ts:178:32 - error TS2722: Cannot invoke an object which is possibly 'undefined'.

178         const reserves = await poolContract.getReservesList();
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/research/FlashLoanResearchEngine.ts:178:32 - error TS18048: 'poolContract.getReservesList' is possibly 'undefined'.

178         const reserves = await poolContract.getReservesList();
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/research/MasterResearchOrchestrator.ts:135:60 - error TS18046: 'error' is of type 'unknown'.

135       this.currentProgress.errors.push(`Research failed: ${error.message}`);
                                                               ~~~~~

src/research/SmartContractMapper.ts:53:67 - error TS2339: Property 'providers' does not exist on type 'typeof import("C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers")'.

53         this.providers.set(Number(chainId) as ChainId, new ethers.providers.JsonRpcProvider(rpcUrl));
                                                                     ~~~~~~~~~

src/research/StrategyDiscoveryEngine.ts:364:5 - error TS2322: Type 'FlashLoanProviderInfo | undefined' is not assignable to type 'FlashLoanProviderInfo'.
  Type 'undefined' is not assignable to type 'FlashLoanProviderInfo'.

364     return this.providers.find(p => p.chainId === chainId && p.feeRate === 0) || this.providers[0];
        ~~~~~~

src/research/StrategyDiscoveryEngine.ts:368:5 - error TS2322: Type 'SmartContractCapability | undefined' is not assignable to type 'SmartContractCapability'.
  Type 'undefined' is not assignable to type 'SmartContractCapability'.

368     return this.contracts.find(c => c.name.includes(name)) || this.contracts[0];
        ~~~~~~

src/research/StrategyDiscoveryEngine.ts:372:5 - error TS2322: Type 'SmartContractCapability | undefined' is not assignable to type 'SmartContractCapability'.
  Type 'undefined' is not assignable to type 'SmartContractCapability'.

372     return this.contracts.find(c => c.name.includes(name) && c.chainId === chainId) || this.contracts[0];
        ~~~~~~

src/research/StrategyDiscoveryEngine.ts:376:5 - error TS2322: Type 'SmartContractCapability | undefined' is not assignable to type 'SmartContractCapability'.
  Type 'undefined' is not assignable to type 'SmartContractCapability'.

376     return this.contracts.find(c => c.name.includes(name)) || this.contracts[0];
        ~~~~~~


Found 82 errors in 11 files.

Errors  Files
     1  src/analysis/BatchAnalysisEngine.ts:465
    25  src/analysis/extract-discoveries.ts:53
     4  src/cli/research-cli.ts:78
    14  src/monitoring/ComprehensiveFlashLoanMonitor.ts:42
    13  src/monitoring/OpportunityScanner.ts:7
    13  src/monitoring/TransactionAnalyzer.ts:68
     3  src/monitoring/UltraEfficientMonitor.ts:56
     3  src/research/FlashLoanResearchEngine.ts:81
     1  src/research/MasterResearchOrchestrator.ts:135
     1  src/research/SmartContractMapper.ts:53
     4  src/research/StrategyDiscoveryEngine.ts:364

C:\Users\<USER>\Documents\augment-projects\8 july 2>npm run build

> flash-loan-scout@1.0.0 build
> tsc

src/analysis/BatchAnalysisEngine.ts:465:11 - error TS2739: Type '{ 1: number; 42161: number; 10: number; 137: number; 8453: number; 11155111: number; }' is missing the following properties from type 'Record<ChainId, number>': 11155420, 421614, 84532, 80001

465     const competitionLevels: Record<ChainId, number> = {
              ~~~~~~~~~~~~~~~~~

src/analysis/extract-discoveries.ts:53:13 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

53             confidence: 0.5,
               ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:88:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

88       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: RiskLevel.HIGH }
                                                 ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:100:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

100       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 232, riskLevel: RiskLevel.HIGH }
                                                  ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:112:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

112       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 211, riskLevel: RiskLevel.HIGH }
                                                  ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:124:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

124       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 340, riskLevel: RiskLevel.HIGH }
                                                  ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:136:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

136       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: RiskLevel.HIGH }
                                                  ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:148:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

148       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1082, riskLevel: RiskLevel.HIGH }
                                                  ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:160:47 - error TS2353: Object literal may only specify known properties, and 'confidence' does not exist in type 'TransactionStrategy'.

160       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'Low-profit pattern', estimatedCapital: 140, riskLevel: RiskLevel.HIGH }
                                                  ~~~~~~~~~~

  src/types/index.ts:41:3
    41   strategy: TransactionStrategy;
         ~~~~~~~~
    The expected type comes from property 'strategy' which is declared here on type 'FlashLoanTransaction'

src/analysis/extract-discoveries.ts:172:123 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

172       strategy: { type: StrategyType.UNKNOWN, confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }

      ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:184:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

184       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 351, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:184:114 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

184       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 351, riskLevel: 'HIGH' }
                                                                                                                     ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:196:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

196       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:196:112 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

196       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
                                                                                                                   ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:208:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

208       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:208:112 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

208       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
                                                                                                                   ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:220:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

220       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 406, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:220:114 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

220       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 406, riskLevel: 'HIGH' }
                                                                                                                     ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:232:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

232       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:232:113 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

232       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: 'HIGH' }
                                                                                                                    ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:244:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

244       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:244:112 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

244       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 823, riskLevel: 'HIGH' }
                                                                                                                   ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:256:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

256       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:256:113 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

256       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'High-profit pattern', estimatedCapital: 1080, riskLevel: 'HIGH' }
                                                                                                                    ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:268:19 - error TS2322: Type '"UNKNOWN"' is not assignable to type 'StrategyType'.

268       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 420, riskLevel: 'HIGH' }
                      ~~~~

  src/types/index.ts:74:3
    74   type: StrategyType;
         ~~~~
    The expected type comes from property 'type' which is declared here on type 'TransactionStrategy'

src/analysis/extract-discoveries.ts:268:114 - error TS2322: Type '"HIGH"' is not assignable to type 'RiskLevel'.

268       strategy: { type: 'UNKNOWN', confidence: 0.5, description: 'Medium-profit pattern', estimatedCapital: 420, riskLevel: 'HIGH' }
                                                                                                                     ~~~~~~~~~

  src/types/index.ts:79:3
    79   riskLevel: RiskLevel;
         ~~~~~~~~~
    The expected type comes from property 'riskLevel' which is declared here on type 'TransactionStrategy'

src/cli/research-cli.ts:78:46 - error TS18046: 'error' is of type 'unknown'.

78       console.error(chalk.red('\n❌ Error:'), error.message);
                                                ~~~~~

src/cli/research-cli.ts:139:46 - error TS18046: 'error' is of type 'unknown'.

139       console.error(chalk.red('\n❌ Error:'), error.message);
                                                 ~~~~~

src/cli/research-cli.ts:176:46 - error TS18046: 'error' is of type 'unknown'.

176       console.error(chalk.red('\n❌ Error:'), error.message);
                                                 ~~~~~

src/cli/research-cli.ts:202:46 - error TS18046: 'error' is of type 'unknown'.

202       console.error(chalk.red('\n❌ Error:'), error.message);
                                                 ~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:42:36 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

42   private arbitrumProvider: ethers.providers.JsonRpcProvider;
                                      ~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:43:35 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

43   private polygonProvider: ethers.providers.JsonRpcProvider;
                                     ~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:74:40 - error TS2339: Property 'providers' does not exist on type 'typeof import("C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers")'.

74     this.arbitrumProvider = new ethers.providers.JsonRpcProvider(config.arbitrumRpcUrl);
                                          ~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:75:39 - error TS2339: Property 'providers' does not exist on type 'typeof import("C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers")'.

75     this.polygonProvider = new ethers.providers.JsonRpcProvider(config.polygonRpcUrl);
                                         ~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:100:49 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

100   private async monitorNetwork(provider: ethers.providers.JsonRpcProvider, chainId: number, networkName: string): Promise<void> {
                                                    ~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:104:33 - error TS7006: Parameter 'blockNumber' implicitly has an 'any' type.

104     provider.on('block', async (blockNumber) => {
                                    ~~~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:114:37 - error TS7006: Parameter 'txHash' implicitly has an 'any' type.

114       provider.on('pending', async (txHash) => {
                                        ~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:127:47 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

127   private async analyzeBlock(provider: ethers.providers.JsonRpcProvider, blockNumber: number, chainId: number, networkName: string): Promise<void> {
                                                  ~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:135:62 - error TS7006: Parameter 'tx' implicitly has an 'any' type.

135       const analysisPromises = block.transactions.map(async (tx) => {
                                                                 ~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:168:49 - error TS7006: Parameter 'log' implicitly has an 'any' type.

168     const flashLoanEvents = receipt.logs.filter(log =>
                                                    ~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:186:13 - error TS2375: Type '{ txHash: any; blockNumber: any; chainId: number; strategy: string; flashLoanProvider: string; borrowedAssets: { token: string; amount: bigint; symbol: string; valueUSD: number; }[]; netProfitUSD: number; ... 8 more ...; riskLevel: "LOW" | ... 1 more ... | "HIGH"; }' is not assignable to type 'FlashLoanOpportunity' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'liquidationTarget' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.

186       const opportunity: FlashLoanOpportunity = {
                ~~~~~~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:268:29 - error TS7006: Parameter 'log' implicitly has an 'any' type.

268       if (receipt.logs.some(log => log.address.toLowerCase() === address.toLowerCase())) {
                                ~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:274:5 - error TS2375: Type '{ type: string; provider: string; dexesUsed: string[]; liquidationTarget: string | undefined; mevType: string | undefined; }' is not assignable to type '{ type: string; provider: string; dexesUsed: string[]; liquidationTarget?: string; mevType?: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'liquidationTarget' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.

274     return {
        ~~~~~~

src/monitoring/ComprehensiveFlashLoanMonitor.ts:423:60 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

423   private async analyzePendingTransaction(provider: ethers.providers.JsonRpcProvider, txHash: string, chainId: number): Promise<void> {
                                                               ~~~~~~~~~

src/monitoring/OpportunityScanner.ts:7:22 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

7     arbitrum: ethers.providers.JsonRpcProvider;
                       ~~~~~~~~~

src/monitoring/OpportunityScanner.ts:8:21 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

8     polygon: ethers.providers.JsonRpcProvider;
                      ~~~~~~~~~

src/monitoring/OpportunityScanner.ts:164:56 - error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.

164         const prices = await this.getPricesAcrossDEXes(tokenA, tokenB, chainId);
                                                           ~~~~~~

src/monitoring/OpportunityScanner.ts:165:68 - error TS2345: Argument of type 'string | undefined' is not assignable to parameter of type 'string'.
  Type 'undefined' is not assignable to type 'string'.

165         const arbitrageOpp = this.findArbitrageOpportunity(prices, tokenA, tokenB, chainId);
                                                                       ~~~~~~

src/monitoring/OpportunityScanner.ts:217:29 - error TS18048: 'highestPrice' is possibly 'undefined'.

217     const priceDifference = highestPrice.price - lowestPrice.price;
                                ~~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:217:50 - error TS18048: 'lowestPrice' is possibly 'undefined'.

217     const priceDifference = highestPrice.price - lowestPrice.price;
                                                     ~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:218:49 - error TS18048: 'lowestPrice' is possibly 'undefined'.

218     const profitPercentage = (priceDifference / lowestPrice.price) * 100;
                                                    ~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:221:27 - error TS18048: 'highestPrice' is possibly 'undefined'.

221     const totalSlippage = highestPrice.slippage + lowestPrice.slippage;
                              ~~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:221:51 - error TS18048: 'lowestPrice' is possibly 'undefined'.

221     const totalSlippage = highestPrice.slippage + lowestPrice.slippage;
                                                      ~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:241:17 - error TS18048: 'lowestPrice' is possibly 'undefined'.

241         buyDEX: lowestPrice.dex,
                    ~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:242:18 - error TS18048: 'highestPrice' is possibly 'undefined'.

242         sellDEX: highestPrice.dex,
                     ~~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:245:19 - error TS18048: 'lowestPrice' is possibly 'undefined'.

245         buyPrice: lowestPrice.price,
                      ~~~~~~~~~~~

src/monitoring/OpportunityScanner.ts:246:20 - error TS18048: 'highestPrice' is possibly 'undefined'.

246         sellPrice: highestPrice.price,
                       ~~~~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:68:28 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

68   private provider: ethers.providers.JsonRpcProvider;
                              ~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:103:32 - error TS2724: '"C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers"' has no exported member named 'providers'. Did you mean 'Provider'?

103   constructor(provider: ethers.providers.JsonRpcProvider, chainId: number) {
                                   ~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:138:13 - error TS2375: Type '{ hash: any; blockNumber: any; from: any; to: any; value: any; gasUsed: number; gasPrice: any; status: any; flashLoanDetails: FlashLoanDetails | undefined; arbitrageDetails: ArbitrageDetails | undefined; liquidationDetails: LiquidationDetails | undefined; mevDetails: MEVDetails | undefined; profitAnalysis: ProfitAna...' is not assignable to type 'AnalyzedTransaction' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'flashLoanDetails' are incompatible.
    Type 'FlashLoanDetails | undefined' is not assignable to type 'FlashLoanDetails'.
      Type 'undefined' is not assignable to type 'FlashLoanDetails'.

138       const analyzedTx: AnalyzedTransaction = {
                ~~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:165:47 - error TS7006: Parameter 'log' implicitly has an 'any' type.

165     const flashLoanLogs = receipt.logs.filter(log =>
                                                  ~~~

src/monitoring/TransactionAnalyzer.ts:201:50 - error TS2532: Object is possibly 'undefined'.

201       const feePercentage = assets.length > 0 && assets[0].amount > 0
                                                     ~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:202:45 - error TS2532: Object is possibly 'undefined'.

202         ? Number(totalFee * BigInt(10000) / assets[0].amount) / 100
                                                ~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:222:49 - error TS7006: Parameter 'log' implicitly has an 'any' type.

222     const dexInteractions = receipt.logs.filter(log =>
                                                    ~~~

src/monitoring/TransactionAnalyzer.ts:230:44 - error TS7006: Parameter 'log' implicitly has an 'any' type.

230       const swapLogs = receipt.logs.filter(log => log.topics[0] === this.EVENT_SIGNATURES.SWAP);
                                               ~~~

src/monitoring/TransactionAnalyzer.ts:265:49 - error TS7006: Parameter 'log' implicitly has an 'any' type.

265     const liquidationLogs = receipt.logs.filter(log =>
                                                    ~~~

src/monitoring/TransactionAnalyzer.ts:299:52 - error TS7006: Parameter 't' implicitly has an 'any' type.

299       const txIndex = block.transactions.findIndex(t =>
                                                       ~

src/monitoring/TransactionAnalyzer.ts:320:29 - error TS7006: Parameter 'log' implicitly has an 'any' type.

320       if (receipt.logs.some(log => log.topics[0] === this.EVENT_SIGNATURES.LIQUIDATION_CALL)) {
                                ~~~

src/monitoring/TransactionAnalyzer.ts:422:9 - error TS2722: Cannot invoke an object which is possibly 'undefined'.

422         contract.symbol(),
            ~~~~~~~~~~~~~~~

src/monitoring/TransactionAnalyzer.ts:423:9 - error TS2722: Cannot invoke an object which is possibly 'undefined'.

423         contract.decimals()
            ~~~~~~~~~~~~~~~~~

src/monitoring/UltraEfficientMonitor.ts:56:7 - error TS2740: Type '{}' is missing the following properties from type 'Record<ChainId, number>': 1, 10, 137, 42161, and 6 more.

56       networkDistribution: {},
         ~~~~~~~~~~~~~~~~~~~

  src/monitoring/UltraEfficientMonitor.ts:30:3
    30   networkDistribution: Record<ChainId, number>;
         ~~~~~~~~~~~~~~~~~~~
    The expected type comes from property 'networkDistribution' which is declared here on type 'MonitoringStats'

src/monitoring/UltraEfficientMonitor.ts:174:14 - error TS7053: Element implicitly has an 'any' type because expression of type 'ChainId' can't be used to index type '{ 137: number; 8453: number; 42161: number; 10: number; 1: number; }'.
  Property '[ChainId.SEPOLIA]' does not exist on type '{ 137: number; 8453: number; 42161: number; 10: number; 1: number; }'.

174     score += networkBonus[transaction.chainId] || 0;
                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/monitoring/UltraEfficientMonitor.ts:203:5 - error TS2740: Type '{}' is missing the following properties from type 'Record<ChainId, number>': 1, 10, 137, 42161, and 6 more.

203     this.stats.networkDistribution = {};
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/research/FlashLoanResearchEngine.ts:81:67 - error TS2339: Property 'providers' does not exist on type 'typeof import("C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers")'.

81         this.providers.set(Number(chainId) as ChainId, new ethers.providers.JsonRpcProvider(rpcUrl));
                                                                     ~~~~~~~~~

src/research/FlashLoanResearchEngine.ts:178:32 - error TS2722: Cannot invoke an object which is possibly 'undefined'.

178         const reserves = await poolContract.getReservesList();
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/research/FlashLoanResearchEngine.ts:178:32 - error TS18048: 'poolContract.getReservesList' is possibly 'undefined'.

178         const reserves = await poolContract.getReservesList();
                                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~

src/research/MasterResearchOrchestrator.ts:135:60 - error TS18046: 'error' is of type 'unknown'.

135       this.currentProgress.errors.push(`Research failed: ${error.message}`);
                                                               ~~~~~

src/research/SmartContractMapper.ts:53:67 - error TS2339: Property 'providers' does not exist on type 'typeof import("C:/Users/<USER>/Documents/augment-projects/8 july 2/node_modules/ethers/lib.commonjs/ethers")'.

53         this.providers.set(Number(chainId) as ChainId, new ethers.providers.JsonRpcProvider(rpcUrl));
                                                                     ~~~~~~~~~

src/research/StrategyDiscoveryEngine.ts:364:5 - error TS2322: Type 'FlashLoanProviderInfo | undefined' is not assignable to type 'FlashLoanProviderInfo'.
  Type 'undefined' is not assignable to type 'FlashLoanProviderInfo'.

364     return this.providers.find(p => p.chainId === chainId && p.feeRate === 0) || this.providers[0];
        ~~~~~~

src/research/StrategyDiscoveryEngine.ts:368:5 - error TS2322: Type 'SmartContractCapability | undefined' is not assignable to type 'SmartContractCapability'.
  Type 'undefined' is not assignable to type 'SmartContractCapability'.

368     return this.contracts.find(c => c.name.includes(name)) || this.contracts[0];
        ~~~~~~

src/research/StrategyDiscoveryEngine.ts:372:5 - error TS2322: Type 'SmartContractCapability | undefined' is not assignable to type 'SmartContractCapability'.
  Type 'undefined' is not assignable to type 'SmartContractCapability'.

372     return this.contracts.find(c => c.name.includes(name) && c.chainId === chainId) || this.contracts[0];
        ~~~~~~

src/research/StrategyDiscoveryEngine.ts:376:5 - error TS2322: Type 'SmartContractCapability | undefined' is not assignable to type 'SmartContractCapability'.
  Type 'undefined' is not assignable to type 'SmartContractCapability'.

376     return this.contracts.find(c => c.name.includes(name)) || this.contracts[0];
        ~~~~~~


Found 82 errors in 11 files.

Errors  Files
     1  src/analysis/BatchAnalysisEngine.ts:465
    25  src/analysis/extract-discoveries.ts:53
     4  src/cli/research-cli.ts:78
    14  src/monitoring/ComprehensiveFlashLoanMonitor.ts:42
    13  src/monitoring/OpportunityScanner.ts:7
    13  src/monitoring/TransactionAnalyzer.ts:68
     3  src/monitoring/UltraEfficientMonitor.ts:56
     3  src/research/FlashLoanResearchEngine.ts:81
     1  src/research/MasterResearchOrchestrator.ts:135
     1  src/research/SmartContractMapper.ts:53
     4  src/research/StrategyDiscoveryEngine.ts:364

C:\Users\<USER>\Documents\augment-projects\8 july 2>