// scripts/checkBandFeed.js
const { ethers } = require("hardhat");

async function main() {
  const bandRegistry = await ethers.getContractAt("BandFeedRegistry", "******************************************");
  const xauFeed = await bandRegistry.getFeedAddress("XAUUSD");
  const ethFeed = await bandRegistry.getFeedAddress("ETHUSD");
  console.log(`XAUUSD Feed: ${xauFeed}`);
  console.log(`ETHUSD Feed: ${ethFeed}`);
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});