// 🧠 TEST ULTIMATE BLOCKCHAIN INTELLIGENCE SYSTEM
// Deep analysis with robust criteria for TOP 20 pools only

import UltimateBlockchainIntelligence from './src/ultimate-blockchain-intelligence.js';
import chalk from 'chalk';

async function testUltimateIntelligence() {
  console.log(chalk.yellow.bold('🧠 TESTING ULTIMATE BLOCKCHAIN INTELLIGENCE SYSTEM'));
  console.log(chalk.cyan('🎯 Deep blockchain analysis with ROBUST criteria'));
  console.log(chalk.green('💰 Finding TOP 20 pools with comprehensive intelligence'));
  console.log('═'.repeat(80));
  
  const intelligence = new UltimateBlockchainIntelligence();
  
  try {
    console.log(chalk.blue('\n🔍 PHASE 1: Deep Blockchain Scan'));
    console.log('📊 Extracting comprehensive data from ALL Meteora DLMM pools...');
    
    // Start the ultimate intelligence system
    const topPools = await intelligence.performDeepBlockchainScan();
    
    if (topPools.length === 0) {
      console.log(chalk.red('❌ No pools found or analysis failed'));
      return;
    }
    
    console.log(chalk.green(`✅ Analysis complete! Found ${topPools.length} top pools`));
    
    // Display comprehensive results
    console.log(chalk.yellow.bold('\n🏆 TOP 20 POOLS - ULTIMATE INTELLIGENCE RANKING'));
    console.log('═'.repeat(80));
    
    topPools.forEach((pool, index) => {
      const rank = index + 1;
      const addr = pool.poolAddress.substring(0, 12) + '...';
      const score = pool.ultimateScore.toFixed(2);
      const volume = pool.transactionAnalysis.totalVolume24h.toFixed(0);
      const fees = pool.transactionAnalysis.totalFees24h.toFixed(2);
      const wallets = pool.transactionAnalysis.uniqueWalletCount;
      const consistency = (pool.profitabilityMetrics.profitConsistency * 100).toFixed(1);
      const risk = pool.riskAssessment.overallRisk;
      
      console.log(chalk.cyan(`\n${rank}. ${addr}`));
      console.log(chalk.white(`   🏆 Ultimate Score: ${chalk.yellow.bold(score)}`));
      console.log(chalk.white(`   💰 24h Volume: $${chalk.green(volume)}`));
      console.log(chalk.white(`   💸 24h Fees: $${chalk.green(fees)}`));
      console.log(chalk.white(`   👥 Unique Wallets: ${chalk.blue(wallets)}`));
      console.log(chalk.white(`   📊 Profit Consistency: ${chalk.yellow(consistency)}%`));
      console.log(chalk.white(`   ⚠️ Risk Level: ${getRiskColor(risk)}`));
      
      // Show transaction breakdown
      const tx = pool.transactionAnalysis;
      console.log(chalk.gray(`   📈 Swaps: ${tx.swapTransactions} | Liquidity: ${tx.liquidityTransactions} | Arbitrage: ${tx.arbitrageTransactions}`));
    });
    
    // Show filtering statistics
    console.log(chalk.blue.bold('\n📊 FILTERING STATISTICS'));
    console.log('═'.repeat(50));
    console.log(chalk.white(`• Robust Criteria Applied:`));
    console.log(chalk.white(`  - Minimum Volume: $${intelligence.CRITERIA.MIN_VOLUME_24H.toLocaleString()}`));
    console.log(chalk.white(`  - Minimum Fees: $${intelligence.CRITERIA.MIN_FEES_24H}`));
    console.log(chalk.white(`  - Minimum Transactions: ${intelligence.CRITERIA.MIN_TRANSACTIONS}`));
    console.log(chalk.white(`  - Minimum Unique Wallets: ${intelligence.CRITERIA.MIN_UNIQUE_WALLETS}`));
    console.log(chalk.white(`  - Minimum Profit Consistency: ${(intelligence.CRITERIA.MIN_PROFIT_CONSISTENCY * 100).toFixed(0)}%`));
    
    // Show top pool detailed analysis
    if (topPools.length > 0) {
      const topPool = topPools[0];
      console.log(chalk.magenta.bold('\n🔍 DETAILED ANALYSIS - TOP POOL'));
      console.log('═'.repeat(50));
      console.log(chalk.white(`Pool Address: ${topPool.poolAddress}`));
      console.log(chalk.white(`Token Pair: ${topPool.basicData.tokenX.substring(0, 8)}.../${topPool.basicData.tokenY.substring(0, 8)}...`));
      
      const tx = topPool.transactionAnalysis;
      console.log(chalk.cyan('\n📊 Transaction Analysis:'));
      console.log(chalk.white(`  • Total Transactions (24h): ${tx.totalTransactions}`));
      console.log(chalk.white(`  • Unique Wallets: ${tx.uniqueWalletCount}`));
      console.log(chalk.white(`  • Swap Transactions: ${tx.swapTransactions}`));
      console.log(chalk.white(`  • Arbitrage Transactions: ${tx.arbitrageTransactions}`));
      console.log(chalk.white(`  • Volume Consistency: ${(tx.volumeConsistency * 100).toFixed(1)}%`));
      
      const profit = topPool.profitabilityMetrics;
      console.log(chalk.green('\n💰 Profitability Metrics:'));
      console.log(chalk.white(`  • Total Volume (24h): $${profit.totalVolume.toFixed(2)}`));
      console.log(chalk.white(`  • Total Fees (24h): $${profit.totalFees.toFixed(2)}`));
      console.log(chalk.white(`  • Profitable Transactions: ${profit.profitableTransactions}/${profit.totalTransactions}`));
      console.log(chalk.white(`  • Profit Consistency: ${(profit.profitConsistency * 100).toFixed(1)}%`));
      console.log(chalk.white(`  • Risk-Adjusted Return: ${profit.riskAdjustedReturn.toFixed(3)}`));
      
      const risk = topPool.riskAssessment;
      console.log(chalk.yellow('\n⚠️ Risk Assessment:'));
      console.log(chalk.white(`  • Overall Risk: ${getRiskColor(risk.overallRisk)}`));
      console.log(chalk.white(`  • Liquidity Risk: ${getRiskColor(risk.liquidityRisk)}`));
      console.log(chalk.white(`  • Volume Risk: ${getRiskColor(risk.volumeRisk)}`));
      console.log(chalk.white(`  • Concentration Risk: ${getRiskColor(risk.concentrationRisk)}`));
      console.log(chalk.white(`  • Risk Score: ${risk.riskScore}/10`));
    }
    
    // Show hourly volume distribution for top pool
    if (topPools.length > 0) {
      const topPool = topPools[0];
      const hourlyVolume = topPool.transactionAnalysis.volumeByHour;
      
      console.log(chalk.blue.bold('\n📈 HOURLY VOLUME DISTRIBUTION (TOP POOL)'));
      console.log('═'.repeat(50));
      
      for (let hour = 0; hour < 24; hour++) {
        const volume = hourlyVolume[hour] || 0;
        const bar = '█'.repeat(Math.min(Math.floor(volume / 1000), 20));
        const timeStr = `${hour.toString().padStart(2, '0')}:00`;
        console.log(chalk.white(`${timeStr} │${chalk.green(bar)} $${volume.toFixed(0)}`));
      }
    }
    
    console.log(chalk.green.bold('\n✅ ULTIMATE BLOCKCHAIN INTELLIGENCE TEST COMPLETE!'));
    console.log(chalk.cyan('🎯 System successfully identified TOP 20 pools with robust criteria'));
    console.log(chalk.yellow('🧠 Deep blockchain analysis provides comprehensive intelligence'));
    console.log(chalk.magenta('💰 Ready for systematic trading with highest quality opportunities'));
    
  } catch (error) {
    console.error(chalk.red('❌ Test failed:'), error.message);
    console.error(error.stack);
  }
}

function getRiskColor(risk) {
  switch (risk) {
    case 'LOW': return chalk.green(risk);
    case 'MEDIUM': return chalk.yellow(risk);
    case 'HIGH': return chalk.red(risk);
    default: return chalk.gray(risk);
  }
}

// 🚀 SIMPLE USAGE EXAMPLE
export async function getUltimateTop20() {
  const intelligence = new UltimateBlockchainIntelligence();
  const topPools = await intelligence.performDeepBlockchainScan();
  
  return topPools.map((pool, index) => ({
    rank: index + 1,
    poolAddress: pool.poolAddress,
    ultimateScore: pool.ultimateScore,
    volume24h: pool.transactionAnalysis.totalVolume24h,
    fees24h: pool.transactionAnalysis.totalFees24h,
    uniqueWallets: pool.transactionAnalysis.uniqueWalletCount,
    profitConsistency: pool.profitabilityMetrics.profitConsistency,
    riskLevel: pool.riskAssessment.overallRisk,
    recommendation: pool.ultimateScore > 100 ? 'STRONG_BUY' : 
                   pool.ultimateScore > 50 ? 'BUY' : 'MONITOR'
  }));
}

// 🔍 ANALYZE SPECIFIC POOL
export async function analyzeSpecificPool(poolAddress) {
  const intelligence = new UltimateBlockchainIntelligence();
  
  // This would analyze a specific pool in detail
  console.log(`🔍 Analyzing pool: ${poolAddress}`);
  
  // For now, return placeholder
  return {
    poolAddress,
    analysis: 'Deep analysis would be performed here',
    recommendation: 'MONITOR'
  };
}

// Run the test
console.log(chalk.magenta.bold('🧠 STARTING ULTIMATE BLOCKCHAIN INTELLIGENCE TEST...'));
console.log(chalk.cyan('This will perform DEEP analysis of Meteora DLMM pools with ROBUST criteria!'));
console.log('');

testUltimateIntelligence().catch(error => {
  console.error(chalk.red('❌ Test failed:'), error.message);
});
