{"name": "meteora-ultimate-monitor", "version": "1.0.0", "description": "Ultimate Meteora pool monitoring system to find top earning pools and fee concentration", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "monitor": "node src/monitor.js", "dashboard": "node src/dashboard.js", "test": "node src/test.js"}, "keywords": ["meteora", "solana", "defi", "pools", "monitoring", "fees", "tvl", "analytics"], "author": "Meteora Monitor System", "license": "MIT", "dependencies": {"axios": "^1.6.0", "express": "^4.18.2", "ws": "^8.14.2", "node-cron": "^3.0.3", "chalk": "^5.3.0", "cli-table3": "^0.6.3", "moment": "^2.29.4", "lodash": "^4.17.21", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1"}}