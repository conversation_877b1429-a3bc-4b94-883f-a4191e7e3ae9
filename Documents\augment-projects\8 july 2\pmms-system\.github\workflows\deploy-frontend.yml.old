name: Deploy Frontend to GitHub Pages

on:
  push:
    branches:
      - main # Or your main development branch

jobs:
  deploy:
    runs-on: ubuntu-latest
    permissions: # <--- ADD THIS BLOCK
      contents: write # This grants write permission to the GITHUB_TOKEN
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20' # Or your preferred Node.js version

      - name: Cache Node Modules
        id: cache-npm
        uses: actions/cache@v4
        with:
          path: ~/.npm # Cache npm's global cache directory
          key: ${{ runner.os }}-npm-${{ hashFiles('frontend/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-npm-

      - name: Install dependencies
        run: npm ci # Use npm ci for clean installs in CI, or npm install
        working-directory: ./frontend

      - name: Configure Git for gh-pages
        run: |
          git config user.name "${{ github.actor }}"
          git config user.email "${{ github.actor }}@users.noreply.github.com"
          git remote set-url origin https://x-access-token:${{ secrets.GITHUB_TOKEN }}@github.com/${{ github.repository }}.git
        working-directory: ./frontend # Ensure Git commands run in the frontend context if it's a sub-repo clone

      - name: Deploy to GitHub Pages
        run: npm run deploy
        working-directory: ./frontend
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }} # This is still good practice to explicitly pass
