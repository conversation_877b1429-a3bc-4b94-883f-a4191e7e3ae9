import { ethers } from 'ethers';
import dotenv from 'dotenv';

dotenv.config();

/**
 * 🧪 TEST DISCOVERY SYSTEM
 * 
 * This script tests the flash loan discovery system components
 * to ensure everything is working correctly before running
 * the full monitoring system.
 */

async function testConnections() {
  console.log('🧪 TESTING FLASH LOAN DISCOVERY SYSTEM');
  console.log('=' .repeat(50));

  // Test 1: Environment Variables
  console.log('\n1️⃣ Testing Environment Variables...');
  const alchemyKey = process.env.ALCHEMY_API_KEY;
  
  if (!alchemyKey) {
    console.log('❌ ALCHEMY_API_KEY not found');
    console.log('Please add ALCHEMY_API_KEY to your .env file');
    return false;
  } else {
    console.log('✅ ALCHEMY_API_KEY found');
  }

  // Test 2: RPC Connections
  console.log('\n2️⃣ Testing RPC Connections...');
  
  try {
    const arbitrumProvider = new ethers.providers.JsonRpcProvider(`https://arb-mainnet.g.alchemy.com/v2/${alchemyKey}`);
    const polygonProvider = new ethers.providers.JsonRpcProvider(`https://polygon-mainnet.g.alchemy.com/v2/${alchemyKey}`);

    // Test Arbitrum connection
    console.log('   Testing Arbitrum connection...');
    const arbBlockNumber = await arbitrumProvider.getBlockNumber();
    console.log(`   ✅ Arbitrum connected - Block: ${arbBlockNumber}`);

    // Test Polygon connection
    console.log('   Testing Polygon connection...');
    const polyBlockNumber = await polygonProvider.getBlockNumber();
    console.log(`   ✅ Polygon connected - Block: ${polyBlockNumber}`);

  } catch (error) {
    console.log('❌ RPC connection failed:', error);
    return false;
  }

  // Test 3: Flash Loan Provider Addresses
  console.log('\n3️⃣ Testing Flash Loan Provider Addresses...');
  
  const flashLoanProviders = {
    ARBITRUM: {
      AAVE_V3: '******************************************',
      BALANCER: '******************************************'
    },
    POLYGON: {
      AAVE_V3: '******************************************',
      BALANCER: '******************************************'
    }
  };

  try {
    const arbitrumProvider = new ethers.providers.JsonRpcProvider(`https://arb-mainnet.g.alchemy.com/v2/${alchemyKey}`);
    
    // Test Aave V3 on Arbitrum
    const aaveCode = await arbitrumProvider.getCode(flashLoanProviders.ARBITRUM.AAVE_V3);
    if (aaveCode !== '0x') {
      console.log('   ✅ Aave V3 contract found on Arbitrum');
    } else {
      console.log('   ❌ Aave V3 contract not found on Arbitrum');
    }

    // Test Balancer on Arbitrum
    const balancerCode = await arbitrumProvider.getCode(flashLoanProviders.ARBITRUM.BALANCER);
    if (balancerCode !== '0x') {
      console.log('   ✅ Balancer contract found on Arbitrum');
    } else {
      console.log('   ❌ Balancer contract not found on Arbitrum');
    }

  } catch (error) {
    console.log('❌ Contract verification failed:', error);
    return false;
  }

  // Test 4: Price API Access
  console.log('\n4️⃣ Testing Price API Access...');
  
  try {
    const axios = require('axios');
    const response = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd', {
      timeout: 10000
    });
    
    if (response.data && response.data.ethereum && response.data.ethereum.usd) {
      console.log(`   ✅ Price API working - ETH: $${response.data.ethereum.usd}`);
    } else {
      console.log('   ❌ Price API response invalid');
    }
  } catch (error) {
    console.log('   ❌ Price API failed:', error);
    return false;
  }

  // Test 5: File System Access
  console.log('\n5️⃣ Testing File System Access...');
  
  try {
    const fs = require('fs');
    const path = require('path');
    
    const testDir = path.join(__dirname, '../discoveries');
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
      console.log('   ✅ Created discoveries directory');
    } else {
      console.log('   ✅ Discoveries directory exists');
    }

    // Test write access
    const testFile = path.join(testDir, 'test.json');
    fs.writeFileSync(testFile, JSON.stringify({ test: true }));
    fs.unlinkSync(testFile);
    console.log('   ✅ File write/delete access confirmed');

  } catch (error) {
    console.log('   ❌ File system access failed:', error);
    return false;
  }

  // Test 6: Memory and Performance
  console.log('\n6️⃣ Testing System Resources...');
  
  const memUsage = process.memoryUsage();
  console.log(`   Memory Usage: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
  console.log(`   Available Memory: ${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`);
  
  if (memUsage.heapUsed / 1024 / 1024 > 500) {
    console.log('   ⚠️  High memory usage detected');
  } else {
    console.log('   ✅ Memory usage normal');
  }

  console.log('\n✅ ALL TESTS PASSED!');
  console.log('🚀 System is ready to run the discovery system');
  
  return true;
}

async function testFlashLoanDetection() {
  console.log('\n🔍 TESTING FLASH LOAN DETECTION...');
  
  try {
    const alchemyKey = process.env.ALCHEMY_API_KEY;
    const provider = new ethers.providers.JsonRpcProvider(`https://arb-mainnet.g.alchemy.com/v2/${alchemyKey}`);
    
    // Get recent blocks and look for flash loan transactions
    const latestBlock = await provider.getBlockNumber();
    console.log(`   Latest block: ${latestBlock}`);
    
    // Check last 10 blocks for flash loan events
    console.log('   Scanning last 10 blocks for flash loan events...');
    
    let flashLoanCount = 0;
    for (let i = 0; i < 10; i++) {
      const blockNumber = latestBlock - i;
      const block = await provider.getBlock(blockNumber, true);
      
      if (block && block.transactions) {
        for (const tx of block.transactions) {
          if (typeof tx === 'object') {
            try {
              const receipt = await provider.getTransactionReceipt(tx.hash);
              if (receipt && receipt.logs) {
                // Check for flash loan event signatures
                const flashLoanEvents = receipt.logs.filter((log: any) =>
                  log.topics[0] === '0x631042c832b07452973831137f2d73e395028b44b250dedc5abb0ee766e168ac' || // Aave
                  log.topics[0] === '0x0d7d75e01ab95780d3cd1c8ec0dd6c2ce19e3a20427eec8bf53283b6fb8e95f0'    // Balancer
                );
                
                if (flashLoanEvents.length > 0) {
                  flashLoanCount++;
                  console.log(`   📦 Flash loan found in block ${blockNumber}: ${tx.hash}`);
                }
              }
            } catch (error) {
              // Skip individual transaction errors
            }
          }
        }
      }
    }
    
    console.log(`   ✅ Scanned 10 blocks, found ${flashLoanCount} flash loan transactions`);
    
    if (flashLoanCount > 0) {
      console.log('   🎯 Flash loan detection is working!');
    } else {
      console.log('   ℹ️  No flash loans in recent blocks (this is normal)');
    }
    
  } catch (error) {
    console.log('   ❌ Flash loan detection test failed:', error);
  }
}

async function displaySystemRequirements() {
  console.log('\n📋 SYSTEM REQUIREMENTS CHECK');
  console.log('=' .repeat(50));
  
  // Node.js version
  console.log(`Node.js Version: ${process.version}`);
  if (parseInt(process.version.slice(1)) >= 16) {
    console.log('✅ Node.js version is compatible');
  } else {
    console.log('❌ Node.js version should be 16 or higher');
  }
  
  // Platform
  console.log(`Platform: ${process.platform}`);
  console.log(`Architecture: ${process.arch}`);
  
  // Memory
  const totalMem = process.memoryUsage();
  console.log(`Total Memory: ${Math.round(totalMem.heapTotal / 1024 / 1024)}MB`);
  
  console.log('\n📦 REQUIRED PACKAGES:');
  console.log('✅ ethers');
  console.log('✅ axios');
  console.log('✅ dotenv');
  console.log('✅ typescript');
  
  console.log('\n🔑 REQUIRED ENVIRONMENT VARIABLES:');
  console.log(`ALCHEMY_API_KEY: ${process.env.ALCHEMY_API_KEY ? '✅ Set' : '❌ Missing'}`);
}

async function main() {
  await displaySystemRequirements();
  
  const testsPass = await testConnections();
  
  if (testsPass) {
    await testFlashLoanDetection();
    
    console.log('\n🎉 SYSTEM TEST COMPLETE!');
    console.log('=' .repeat(50));
    console.log('Your system is ready to run the Ultimate Flash Loan Discovery System.');
    console.log('\nTo start the full system, run:');
    console.log('npm run start:discovery');
    console.log('\nOr manually:');
    console.log('npx ts-node scripts/ultimate-flash-loan-discovery-system.ts');
    
  } else {
    console.log('\n❌ SYSTEM TEST FAILED!');
    console.log('Please fix the issues above before running the discovery system.');
  }
}

main().catch(console.error);
