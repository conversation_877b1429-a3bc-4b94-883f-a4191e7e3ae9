/**
 * 🚀 TEST WORKING ARBITRAGE
 * Test the contract that uses confirmed working Balancer flash loans
 */

const hre = require("hardhat");

async function main() {
    console.log("🚀 TESTING WORKING ARBITRAGE...");
    
    const contractAddress = "******************************************";
    const [deployer] = await hre.ethers.getSigners();
    
    console.log("📝 Testing with account:", deployer.address);
    console.log("📍 Contract address:", contractAddress);
    
    // Get contract instance
    const WorkingArbitrage = await hre.ethers.getContractFactory("WorkingArbitrage");
    const contract = WorkingArbitrage.attach(contractAddress);
    
    // Check initial state
    console.log("\n📊 INITIAL STATE:");
    const initialResults = await contract.getResults();
    console.log("💰 Last profit:", hre.ethers.utils.formatUnits(initialResults[0], 6), "USDC");
    console.log("✅ Last success:", initialResults[1]);
    console.log("🔢 Total executions:", initialResults[2].toString());
    
    const currentBalance = await contract.getCurrentBalance();
    console.log("💳 Current balance:", hre.ethers.utils.formatUnits(currentBalance, 6), "USDC");
    
    // Execute working arbitrage
    console.log("\n🚀 EXECUTING WORKING ARBITRAGE...");
    console.log("💡 Using confirmed working Balancer flash loans (0% fee)");
    console.log("💡 With proper balance management to prevent transfer errors");
    console.log("💡 Flash loan amount: $1,000 USDC");
    
    try {
        const tx = await contract.executeWorkingArbitrage({
            gasLimit: 2500000,
            gasPrice: hre.ethers.utils.parseUnits("50", "gwei")
        });
        
        console.log("📝 Transaction hash:", tx.hash);
        console.log("🔗 Polygonscan:", `https://polygonscan.com/tx/${tx.hash}`);
        
        console.log("⏳ Waiting for confirmation...");
        const receipt = await tx.wait();
        
        console.log("✅ TRANSACTION CONFIRMED!");
        console.log("⛽ Gas used:", receipt.gasUsed.toString());
        console.log("💰 Gas cost:", hre.ethers.utils.formatEther(receipt.gasUsed.mul(tx.gasPrice)), "MATIC");
        
        // Parse events
        console.log("\n📊 DETAILED EVENTS:");
        for (const log of receipt.logs) {
            try {
                const parsed = contract.interface.parseLog(log);
                console.log(`🎯 ${parsed.name}:`, parsed.args);
            } catch (e) {
                // Skip unparseable logs
            }
        }
        
        // Check final results
        console.log("\n📊 FINAL RESULTS:");
        const finalResults = await contract.getResults();
        console.log("💰 Last profit:", hre.ethers.utils.formatUnits(finalResults[0], 6), "USDC");
        console.log("✅ Last success:", finalResults[1]);
        console.log("🔢 Total executions:", finalResults[2].toString());
        
        const finalBalance = await contract.getCurrentBalance();
        console.log("💳 Final balance:", hre.ethers.utils.formatUnits(finalBalance, 6), "USDC");
        
        if (finalResults[1]) {
            console.log("\n🎉 WORKING ARBITRAGE SUCCESS!");
            if (finalResults[0].gt(0)) {
                console.log("💰 PROFIT GENERATED:", hre.ethers.utils.formatUnits(finalResults[0], 6), "USDC");
                console.log("🏦 Profit sent to:", "******************************************");
                
                // Calculate profit percentage
                const flashAmount = hre.ethers.utils.parseUnits("1000", 6);
                const profitPercent = finalResults[0].mul(10000).div(flashAmount).toNumber() / 100;
                console.log("📊 Profit percentage:", profitPercent.toFixed(4) + "%");
                
                // Calculate gas efficiency
                const gasCostMatic = receipt.gasUsed.mul(tx.gasPrice);
                const gasCostUsd = parseFloat(hre.ethers.utils.formatEther(gasCostMatic)) * 0.20; // Assume $0.20 per MATIC
                const profitUsd = parseFloat(hre.ethers.utils.formatUnits(finalResults[0], 6));
                const netProfitUsd = profitUsd - gasCostUsd;
                
                console.log("💸 Gas cost (USD):", gasCostUsd.toFixed(4));
                console.log("💰 Net profit (USD):", netProfitUsd.toFixed(4));
                
                if (netProfitUsd > 0) {
                    console.log("🎯 NET POSITIVE ARBITRAGE!");
                } else {
                    console.log("⚠️ Gas costs exceed profit");
                }
                
            } else {
                console.log("💡 No profit this round, but execution successful!");
                console.log("💡 This confirms the system works - just need better arbitrage opportunities");
            }
        } else {
            console.log("\n❌ EXECUTION FAILED - CHECK EVENTS FOR DETAILS");
        }
        
    } catch (error) {
        console.error("❌ EXECUTION FAILED:", error.message);
        
        if (error.message.includes("revert")) {
            console.log("💡 Contract reverted - check the revert reason in events");
        }
        
        if (error.message.includes("gas")) {
            console.log("⛽ Gas issue - try increasing gas limit");
        }
        
        if (error.message.includes("balance")) {
            console.log("💳 Balance issue - this should be fixed in the working version");
        }
    }
    
    console.log("\n🎯 WORKING ARBITRAGE TEST COMPLETE!");
    console.log("💡 This test uses the confirmed working Balancer flash loan mechanism");
    console.log("💡 With proper balance management to prevent transfer errors");
}

main()
    .then(() => {
        console.log("✅ TEST COMPLETE");
        process.exit(0);
    })
    .catch((error) => {
        console.error("❌ TEST FAILED:", error);
        process.exit(1);
    });
