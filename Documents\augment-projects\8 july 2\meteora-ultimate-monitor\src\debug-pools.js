// 🔍 DEBUG POOLS - Let's see what the actual pool data looks like
import MeteoraAPIClient from './api-client.js';

async function debugPools() {
  const apiClient = new MeteoraAPIClient();

  console.log('🔍 Fetching sample pools to understand data structure...');

  try {
    const pools = await apiClient.getAllDLMMPools();
    console.log(`Found ${pools.length} total pools`);

    if (pools.length > 0) {
      console.log('\n📊 First 3 pools data structure:');
      pools.slice(0, 3).forEach((pool, index) => {
        console.log(`\nPool ${index + 1}:`, JSON.stringify(pool, null, 2));
      });

      // Check available fields
      console.log('\n🔍 Available fields in pool data:');
      const allFields = new Set();
      pools.slice(0, 10).forEach(pool => {
        Object.keys(pool).forEach(key => allFields.add(key));
      });

      console.log('Fields:', Array.from(allFields).sort());

      // Look for pools with any activity
      console.log('\n🔍 Looking for pools with any data...');
      const activePools = pools.filter(pool => {
        return Object.values(pool).some(value =>
          value !== null && value !== undefined && value !== '' && value !== '0'
        );
      });

      console.log(`Found ${activePools.length} pools with some data`);

      if (activePools.length > 0) {
        console.log('\nSample active pool:', JSON.stringify(activePools[0], null, 2));
      }
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

debugPools().catch(console.error);
