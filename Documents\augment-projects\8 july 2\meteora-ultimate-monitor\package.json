{"name": "meteora-ultimate-dlmm-monitor", "version": "1.0.0", "description": "THE ULTIMATE METEORA DLMM MONITORING MACHINE - Find the highest earning pools in real-time", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js", "honey-hunter": "node src/honey-hunter.js", "live-monitor": "node src/live-monitor.js", "dashboard": "node src/dashboard.js", "profit-tracker": "node src/profit-tracker.js", "concentration-scanner": "node src/concentration-scanner.js", "test-apis": "node src/test-apis.js", "alchemy-intelligence": "node test-alchemy-intelligence.js", "setup-alchemy": "node setup-alchemy.js"}, "keywords": ["meteora", "dlmm", "solana", "defi", "pools", "monitoring", "fees", "concentration", "profit", "real-time"], "author": "DLMM Honey Hunter", "license": "MIT", "dependencies": {"@solana/buffer-layout": "^4.0.1", "@solana/buffer-layout-utils": "^0.2.0", "@solana/web3.js": "^1.98.2", "axios": "^1.6.0", "blessed": "^0.1.81", "blessed-contrib": "^4.11.0", "chalk": "^5.3.0", "cli-table3": "^0.6.3", "dotenv": "^16.3.1", "express": "^4.18.2", "lodash": "^4.17.21", "moment": "^2.29.4", "node-cron": "^3.0.3", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1"}}