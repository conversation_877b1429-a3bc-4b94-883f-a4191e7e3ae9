// 🧪 API TESTING - Verify all data sources are working
import chalk from 'chalk';
import MeteoraAPIClient from './api-client.js';
import HoneyAnalyzer from './honey-analyzer.js';

class APITester {
  constructor() {
    this.apiClient = new MeteoraAPIClient();
    this.analyzer = new HoneyAnalyzer();
  }

  async runAllTests() {
    console.log(chalk.yellow.bold('🧪 METEORA API TESTING SUITE'));
    console.log(chalk.gray('═'.repeat(60)));
    
    const tests = [
      { name: 'DLMM Pools API', test: () => this.testDLMMPools() },
      { name: 'Pool Details API', test: () => this.testPoolDetails() },
      { name: 'Universal Search API', test: () => this.testUniversalSearch() },
      { name: 'DexScreener API', test: () => this.testDexScreener() },
      { name: 'DeFiLlama API', test: () => this.testDefiLlama() },
      { name: 'Honey Analysis', test: () => this.testHoneyAnalysis() },
      { name: 'Concentration Scanner', test: () => this.testConcentrationScanner() }
    ];

    const results = [];
    
    for (const test of tests) {
      console.log(chalk.blue(`\n🔍 Testing ${test.name}...`));
      
      try {
        const startTime = Date.now();
        const result = await test.test();
        const duration = Date.now() - startTime;
        
        if (result.success) {
          console.log(chalk.green(`✅ ${test.name} - PASSED (${duration}ms)`));
          if (result.data) {
            console.log(chalk.gray(`   Data: ${result.data}`));
          }
        } else {
          console.log(chalk.red(`❌ ${test.name} - FAILED`));
          console.log(chalk.red(`   Error: ${result.error}`));
        }
        
        results.push({
          name: test.name,
          success: result.success,
          duration,
          error: result.error
        });
        
      } catch (error) {
        console.log(chalk.red(`❌ ${test.name} - CRASHED`));
        console.log(chalk.red(`   Error: ${error.message}`));
        
        results.push({
          name: test.name,
          success: false,
          duration: 0,
          error: error.message
        });
      }
    }
    
    // Summary
    console.log(chalk.gray('\n═'.repeat(60)));
    console.log(chalk.yellow.bold('📊 TEST SUMMARY'));
    
    const passed = results.filter(r => r.success).length;
    const total = results.length;
    
    console.log(chalk.white(`Tests Passed: ${chalk.green(passed)} / ${total}`));
    console.log(chalk.white(`Success Rate: ${chalk.cyan(((passed/total) * 100).toFixed(1))}%`));
    
    if (passed === total) {
      console.log(chalk.green.bold('\n🎉 ALL TESTS PASSED! System is ready to hunt honey!'));
    } else {
      console.log(chalk.yellow.bold('\n⚠️  Some tests failed. Check the errors above.'));
    }
  }

  async testDLMMPools() {
    try {
      const pools = await this.apiClient.getAllDLMMPools();
      
      if (!Array.isArray(pools)) {
        return { success: false, error: 'Response is not an array' };
      }
      
      if (pools.length === 0) {
        return { success: false, error: 'No pools returned' };
      }
      
      return { 
        success: true, 
        data: `Found ${pools.length} DLMM pools` 
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async testPoolDetails() {
    try {
      // First get a pool address
      const pools = await this.apiClient.getAllDLMMPools();
      if (!pools.length) {
        return { success: false, error: 'No pools to test with' };
      }
      
      const testPool = pools[0];
      const poolAddress = testPool.address || testPool.pool_address;
      
      if (!poolAddress) {
        return { success: false, error: 'No pool address found' };
      }
      
      const details = await this.apiClient.getPoolDetails(poolAddress);
      
      if (!details) {
        return { success: false, error: 'No pool details returned' };
      }
      
      return { 
        success: true, 
        data: `Got details for pool ${poolAddress.substring(0, 8)}...` 
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async testUniversalSearch() {
    try {
      const results = await this.apiClient.searchPools('sol');
      
      if (!results) {
        return { success: false, error: 'No search results returned' };
      }
      
      return { 
        success: true, 
        data: `Search returned results` 
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async testDexScreener() {
    try {
      const data = await this.apiClient.getDexScreenerData();
      
      return { 
        success: true, 
        data: `DexScreener data retrieved` 
      };
    } catch (error) {
      // DexScreener might not always be available, so we'll be lenient
      return { 
        success: true, 
        data: `DexScreener unavailable (${error.message})` 
      };
    }
  }

  async testDefiLlama() {
    try {
      const data = await this.apiClient.getDefiLlamaData();
      
      return { 
        success: true, 
        data: data ? 'Found Meteora data' : 'No Meteora data found' 
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async testHoneyAnalysis() {
    try {
      // Create a mock pool for testing
      const mockPool = {
        address: 'test_pool_address',
        name: 'TEST/USDC',
        liquidity_usd: '50000',
        volume_24h: '25000',
        fees_24h: '250',
        mint_x_symbol: 'TEST',
        mint_y_symbol: 'USDC'
      };
      
      const analysis = this.analyzer.analyzePool(mockPool);
      
      if (!analysis) {
        return { success: false, error: 'Analysis returned null' };
      }
      
      if (typeof analysis.honeyScore !== 'number') {
        return { success: false, error: 'Invalid honey score' };
      }
      
      return { 
        success: true, 
        data: `Honey score: ${analysis.honeyScore.toFixed(1)}, Category: ${analysis.category}` 
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async testConcentrationScanner() {
    try {
      const pools = await this.apiClient.scanForConcentration();
      
      if (!Array.isArray(pools)) {
        return { success: false, error: 'Scanner did not return array' };
      }
      
      return { 
        success: true, 
        data: `Scanned and found ${pools.length} qualifying pools` 
      };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }
}

// Run the tests
const tester = new APITester();
tester.runAllTests().catch(console.error);
